#!/usr/bin/env node

/**
 * Test script for GeezSMS Worker Monitoring System
 * 
 * This script tests the various monitoring endpoints to verify functionality
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000';
const API_TOKEN = 'ccda1683d8c97f8f2dff2ea7d649b42c';

// Test endpoints with correct API version prefix
const ENDPOINTS = [
  {
    name: 'Comprehensive Worker Status',
    path: '/api/v1/worker-status',
    method: 'GET'
  },
  {
    name: 'Worker Health Check',
    path: '/api/v1/worker-status/health',
    method: 'GET'
  },
  {
    name: 'Worker Performance Metrics',
    path: '/api/v1/worker-status/metrics',
    method: 'GET'
  },
  {
    name: 'Failed SMS Messages',
    path: '/api/v1/worker-status/failed-sms',
    method: 'GET'
  },
  {
    name: 'Pending SMS Messages',
    path: '/api/v1/worker-status/pending-sms',
    method: 'GET'
  },
  {
    name: 'Retry Statistics',
    path: '/api/v1/worker-status/retry-stats',
    method: 'GET'
  },
  {
    name: 'Retry All Failed SMS',
    path: '/api/v1/worker-status/retry-all',
    method: 'GET'
  }
];

// Test results tracking
let passedTests = 0;
let failedTests = 0;

console.log('🚀 Starting GeezSMS Worker Monitoring Tests');
console.log(`📍 Base URL: ${BASE_URL}`);
console.log(`🔑 API Token: ${API_TOKEN ? 'Configured' : 'Missing'}\n`);

async function testEndpoint(endpoint) {
  console.log(`🔍 Testing: ${endpoint.name}`);
  console.log(`   Endpoint: ${endpoint.path}`);
  
  try {
    const response = await axios({
      method: endpoint.method,
      url: `${BASE_URL}${endpoint.path}`,
      headers: {
        'Authorization': `Bearer ${API_TOKEN}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    console.log(`   ✅ Success: ${response.status}`);
    console.log(`   📊 Response: ${JSON.stringify(response.data, null, 2).substring(0, 200)}...`);
    passedTests++;
    
  } catch (error) {
    console.log(`   ❌ Error: ${error.response ? error.response.status : error.message}`);
    if (error.response) {
      console.log(`   📊 Response: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    failedTests++;
  }
  
  console.log('');
}

async function runTests() {
  for (const endpoint of ENDPOINTS) {
    await testEndpoint(endpoint);
  }
  
  // Summary
  console.log('📈 Test Results Summary');
  console.log(`✅ Passed: ${passedTests}`);
  console.log(`❌ Failed: ${failedTests}`);
  console.log(`📊 Total: ${passedTests + failedTests}`);
  
  if (failedTests > 0) {
    console.log('\n⚠️  Some tests failed. Please check the server logs and configuration.');
  } else {
    console.log('\n🎉 All tests passed! The worker monitoring system is working correctly.');
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
GeezSMS Worker Monitoring Test Script

Usage: node test-worker-monitoring.js [options]

Options:
  --help, -h     Show this help message
  --url <url>    Set base URL (default: http://localhost:3000)
  --token <token> Set API token

Environment Variables:
  APP_URL        Base URL for the API
  API_AUTH_TOKEN API authentication token

Examples:
  node test-worker-monitoring.js
  node test-worker-monitoring.js --url http://localhost:3000 --token your_token
  APP_URL=http://localhost:3000 API_AUTH_TOKEN=your_token node test-worker-monitoring.js
  `);
  process.exit(0);
}

// Parse command line arguments
for (let i = 0; i < args.length; i++) {
  if (args[i] === '--url' && args[i + 1]) {
    process.env.APP_URL = args[i + 1];
    i++;
  } else if (args[i] === '--token' && args[i + 1]) {
    process.env.API_AUTH_TOKEN = args[i + 1];
    i++;
  }
}

// Run the tests
runTests().catch(console.error); 