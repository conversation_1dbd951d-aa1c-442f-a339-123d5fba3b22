const http = require('http');

/**
 * Function to send an SMS request with authentication
 * @param {string} to - The phone number to send to
 * @param {string} message - The message to send
 * @param {string} provider - The SMS service provider (TELE or SAFARICOM)
 * @returns {Promise<object>} - The response data
 */
function sendSmsWithAuth(to, message, provider) {
  return new Promise((resolve, reject) => {
    // Create the request data
    const data = JSON.stringify({
      message: message,
      to: to,
      sms_credential: {
        usr: "testuser",
        pwd: "testpass",
        from: "SENDER",
        token: "testtoken",
        sms_service_provider: provider
      }
    });

    // Configure the request options with authentication
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/sms/send/single',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length,
        'Authorization': 'Bearer ccda1683d8c97f8f2dff2ea7d649b42c' // API token from .env
      }
    };

    // Send the request
    const req = http.request(options, (res) => {
      console.log(`STATUS: ${res.statusCode}`);
      console.log(`HEADERS: ${JSON.stringify(res.headers)}`);

      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: parsedData
          });
        } catch (e) {
          reject(new Error(`Failed to parse response: ${e.message}`));
        }
      });
    });

    req.on('error', (e) => {
      reject(new Error(`Problem with request: ${e.message}`));
    });

    // Write the request data
    req.write(data);
    req.end();
  });
}

// Test both providers
async function testBothProviders() {
  try {
    console.log('=== Testing TELE Provider with Authentication ===');
    console.log('Sending SMS to TELE number (251953960596)...');
    const teleResponse = await sendSmsWithAuth('251953960596', 'Test message for TELE provider', 'TELE');
    console.log('RESPONSE BODY:');
    console.log(JSON.stringify(teleResponse.data, null, 2));

    // Wait a bit to allow processing
    console.log('\nWaiting for 2 seconds...\n');
    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log('=== Testing SAFARICOM Provider with Authentication ===');
    console.log('Sending SMS to SAFARICOM number (251711220033)...');
    const safaricomResponse = await sendSmsWithAuth('251711220033', 'Test message for SAFARICOM provider', 'SAFARICOM');
    console.log('RESPONSE BODY:');
    console.log(JSON.stringify(safaricomResponse.data, null, 2));

    console.log('\nTests completed successfully!');
  } catch (error) {
    console.error('Error during testing:', error.message);
  }
}

// Run the tests
console.log('Starting SMS provider tests with authentication...');
testBothProviders(); 