# SMS Worker Architecture

This document explains how to use the SMS worker architecture in the GeezSMS system.

## Overview

The SMS worker architecture is designed to:

1. Start automatically with the NestJS application
2. Scale based on environment configuration
3. Support worker categorization for specialized processing
4. Provide health monitoring
5. Ensure fault tolerance

## Running the Application with Workers

There are several ways to run the application with worker threads:

### 1. Using the start:workers script

```bash
npm run start:workers
```

This will start the application with 4 worker threads by default.

### 2. Specifying the number of workers

```bash
node run-with-workers.js 8
```

This will start the application with 8 worker threads.

### 3. Using environment variables

```bash
SMS_WORKER_COUNT=6 npm run start:dev
```

This will start the application with 6 worker threads.

## Configuration

The worker architecture can be configured using environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `SMS_WORKER_COUNT` | Number of worker instances to start | CPU cores - 1 |
| `SMS_WORKER_CATEGORIES` | Comma-separated list of worker categories | single,bulk |

You can set these variables in the `.env.development` file:

```
SMS_WORKER_COUNT=4
SMS_WORKER_CATEGORIES=default,bulk,marketing,transactional,high-priority
```

## Worker Categories

Workers are assigned to categories in a round-robin fashion. Categories include:

- `default`: Handles general SMS messages
- `bulk`: Optimized for bulk SMS processing
- `marketing`: For marketing messages
- `transactional`: For transactional messages
- `high-priority`: For urgent messages

When sending an SMS, you can specify a category to route it to the appropriate worker:

```json
{
  "to": "+**********",
  "message": "Your verification code is 123456",
  "workerCategory": "high-priority"
}
```

## Health Monitoring

You can monitor the health of the workers using the health endpoints:

```
GET /api/v1/health
GET /api/v1/health/workers
```

The health endpoint will return information about the workers, including their status and the time since the last heartbeat.

## Testing

You can test the worker architecture using the test-workers.js script:

```bash
node test-workers.js
```

This will send 100 SMS requests (50 concurrent) to the application and measure the performance.

## Troubleshooting

If you encounter issues with the worker architecture, check the following:

1. Make sure RabbitMQ is running and accessible
2. Check the logs for any errors
3. Verify that the worker threads are being created
4. Check the health endpoint to see if the workers are healthy

## Implementation Details

The worker architecture consists of the following components:

1. **WorkerManagerService**: Creates and manages worker threads
2. **SmsProviderService**: Routes SMS messages to the appropriate worker
3. **SmsWorkerService**: Processes SMS messages
4. **HealthController**: Monitors the health of the workers

The worker threads are created using the Node.js worker_threads module and communicate with the main thread using message passing.
