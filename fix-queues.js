const amqp = require('amqplib');

async function fixQueues() {
  try {
    console.log('Connecting to RabbitMQ...');
    const connection = await amqp.connect('amqp://localhost:5672');
    const channel = await connection.createChannel();

    // Check if the queues exist
    console.log('Checking queues...');
    
    try {
      await channel.checkQueue('sms_single_queue');
      console.log('sms_single_queue exists');
    } catch (error) {
      console.log('sms_single_queue does not exist, creating it...');
      await channel.assertQueue('sms_single_queue', {
        durable: true,
        arguments: {
          'x-dead-letter-exchange': 'dlx',
          'x-max-priority': 10,
          'x-message-ttl': 86400000
        }
      });
      console.log('sms_single_queue created');
    }
    
    try {
      await channel.checkQueue('sms_bulk_queue');
      console.log('sms_bulk_queue exists');
    } catch (error) {
      console.log('sms_bulk_queue does not exist, creating it...');
      await channel.assertQueue('sms_bulk_queue', {
        durable: true,
        arguments: {
          'x-dead-letter-exchange': 'dlx',
          'x-max-priority': 10,
          'x-message-ttl': 86400000
        }
      });
      console.log('sms_bulk_queue created');
    }
    
    // Check if the hyphenated queues exist and move messages
    try {
      const singleQueueInfo = await channel.checkQueue('sms-single-queue');
      console.log(`sms-single-queue exists with ${singleQueueInfo.messageCount} messages`);
      
      if (singleQueueInfo.messageCount > 0) {
        console.log('Moving messages from sms-single-queue to sms_single_queue...');
        
        // Get messages from the old queue
        const messages = await channel.get('sms-single-queue', { noAck: false });
        if (messages) {
          console.log(`Got message: ${JSON.stringify(messages.content.toString())}`);
          
          // Publish to the new queue
          await channel.publish('', 'sms_single_queue', messages.content);
          console.log('Published message to sms_single_queue');
          
          // Acknowledge the message from the old queue
          channel.ack(messages);
          console.log('Acknowledged message from sms-single-queue');
        }
      }
    } catch (error) {
      console.log('sms-single-queue does not exist or error occurred:', error.message);
    }
    
    try {
      const bulkQueueInfo = await channel.checkQueue('sms-bulk-queue');
      console.log(`sms-bulk-queue exists with ${bulkQueueInfo.messageCount} messages`);
      
      if (bulkQueueInfo.messageCount > 0) {
        console.log('Moving messages from sms-bulk-queue to sms_bulk_queue...');
        
        // Get messages from the old queue
        const messages = await channel.get('sms-bulk-queue', { noAck: false });
        if (messages) {
          console.log(`Got message: ${JSON.stringify(messages.content.toString())}`);
          
          // Publish to the new queue
          await channel.publish('', 'sms_bulk_queue', messages.content);
          console.log('Published message to sms_bulk_queue');
          
          // Acknowledge the message from the old queue
          channel.ack(messages);
          console.log('Acknowledged message from sms-bulk-queue');
        }
      }
    } catch (error) {
      console.log('sms-bulk-queue does not exist or error occurred:', error.message);
    }
    
    console.log('Done');
    await channel.close();
    await connection.close();
  } catch (error) {
    console.error('Error:', error);
  }
}

fixQueues();
