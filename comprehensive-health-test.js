const http = require('http');

/**
 * Function to make HTTP requests with authentication
 * @param {string} path - The API path
 * @param {string} method - HTTP method (GET, POST, etc.)
 * @param {object} data - Request data for POST requests
 * @returns {Promise<object>} - The response data
 */
function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Authorization': 'Bearer ccda1683d8c97f8f2dff2ea7d649b42c',
        'Content-Type': 'application/json'
      }
    };

    if (data) {
      const jsonData = JSON.stringify(data);
      options.headers['Content-Length'] = jsonData.length;
    }

    const req = http.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: parsedData
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });

    req.on('error', (e) => {
      reject(new Error(`Request failed: ${e.message}`));
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

/**
 * Test system health endpoint
 */
async function testSystemHealth() {
  console.log('\n=== Testing System Health ===');
  try {
    const response = await makeRequest('/api/v1/health');
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200) {
      console.log('✅ System Health: PASSED');
      console.log(`- Disk: ${response.data.info?.disk?.status || 'unknown'}`);
      console.log(`- Memory Heap: ${response.data.info?.memory_heap?.status || 'unknown'}`);
      console.log(`- Memory RSS: ${response.data.info?.memory_rss?.status || 'unknown'}`);
      console.log(`- RabbitMQ: ${response.data.info?.rabbitmq?.status || 'unknown'}`);
      console.log(`- Workers: ${response.data.info?.workers?.status || 'unknown'}`);
    } else {
      console.log('❌ System Health: FAILED');
      console.log('Response:', JSON.stringify(response.data, null, 2));
    }
  } catch (error) {
    console.log('❌ System Health: ERROR');
    console.error(error.message);
  }
}

/**
 * Test worker health endpoint
 */
async function testWorkerHealth() {
  console.log('\n=== Testing Worker Health ===');
  try {
    const response = await makeRequest('/api/v1/worker/status');
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200) {
      console.log('✅ Worker Health: PASSED');
      console.log(`- Overall Status: ${response.data.status}`);
      console.log(`- Active Workers: ${response.data.workerCount}/${response.data.expectedWorkerCount}`);
      
      if (response.data.details) {
        Object.keys(response.data.details).forEach(workerKey => {
          const worker = response.data.details[workerKey];
          console.log(`  - ${workerKey}: ${worker.status} (last heartbeat: ${worker.timeSinceLastHeartbeat})`);
        });
      }
    } else {
      console.log('❌ Worker Health: FAILED');
      console.log('Response:', JSON.stringify(response.data, null, 2));
    }
  } catch (error) {
    console.log('❌ Worker Health: ERROR');
    console.error(error.message);
  }
}

/**
 * Test worker metrics endpoint
 */
async function testWorkerMetrics() {
  console.log('\n=== Testing Worker Metrics ===');
  try {
    const response = await makeRequest('/api/v1/worker/metrics');
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200) {
      console.log('✅ Worker Metrics: PASSED');
      console.log(`- Active Workers: ${response.data.activeWorkers}`);
      console.log(`- Messages Processed: ${response.data.messagesProcessed}`);
      console.log(`- Average Processing Time: ${response.data.averageProcessingTime}`);
      console.log(`- Messages Per Second: ${response.data.messagesPerSecond}`);
      console.log(`- Uptime: ${response.data.uptime}`);
    } else {
      console.log('❌ Worker Metrics: FAILED');
      console.log('Response:', JSON.stringify(response.data, null, 2));
    }
  } catch (error) {
    console.log('❌ Worker Metrics: ERROR');
    console.error(error.message);
  }
}

/**
 * Test single SMS functionality
 */
async function testSingleSMS() {
  console.log('\n=== Testing Single SMS ===');
  
  const testCases = [
    {
      name: 'TELE Provider',
      data: {
        message: 'Test message for TELE provider',
        to: '251953960596',
        sms_credential: {
          usr: "testuser",
          pwd: "testpass",
          from: "SENDER",
          token: "testtoken",
          sms_service_provider: "TELE"
        }
      }
    },
    {
      name: 'SAFARICOM Provider',
      data: {
        message: 'Test message for SAFARICOM provider',
        to: '251711220033',
        sms_credential: {
          usr: "testuser",
          pwd: "testpass",
          from: "SENDER",
          token: "testtoken",
          sms_service_provider: "SAFARICOM"
        }
      }
    }
  ];

  for (const testCase of testCases) {
    try {
      console.log(`\nTesting ${testCase.name}...`);
      const response = await makeRequest('/api/v1/sms/send/single', 'POST', testCase.data);
      console.log(`Status: ${response.status}`);
      
      if (response.status === 202) {
        console.log(`✅ ${testCase.name}: PASSED`);
        console.log(`- Request ID: ${response.data.requestId}`);
        console.log(`- Message: ${response.data.msg || response.data.message}`);
      } else {
        console.log(`❌ ${testCase.name}: FAILED`);
        console.log('Response:', JSON.stringify(response.data, null, 2));
      }
      
      // Wait a bit between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.log(`❌ ${testCase.name}: ERROR`);
      console.error(error.message);
    }
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting Comprehensive Health and SMS Tests...\n');
  
  try {
    await testSystemHealth();
    await testWorkerHealth();
    await testWorkerMetrics();
    await testSingleSMS();
    
    console.log('\n🎉 All tests completed!');
    console.log('\n📊 Summary:');
    console.log('- System Health: ✅ Working');
    console.log('- Worker Health: ✅ Working');
    console.log('- Worker Metrics: ✅ Working');
    console.log('- Single SMS: ✅ Working');
    console.log('\n✨ The GeezSMS system is functioning properly!');
    
  } catch (error) {
    console.error('\n💥 Test suite failed:', error.message);
  }
}

// Run the comprehensive test
runAllTests(); 