const axios = require('axios');
const { performance } = require('perf_hooks');

/**
 * Robust Performance Test Configuration
 */
const CONFIG = {
  targetThroughput: 5000, // SMS per second (5K target)
  testDuration: 30, // seconds (30 second test)
  concurrentRequests: 200, // per batch
  apiToken: 'ccda1683d8c97f8f2dff2ea7d649b42c',
  baseUrl: 'http://localhost:3000',
  apiVersion: 'v1',
  maxRetries: 3,
  timeout: 10000
};

/**
 * Generate test SMS data
 */
function generateTestSms(index) {
  return {
    message: `Performance test message ${index}`,
    to: index % 2 === 0 ? '251953960596' : '251711220033',
    sms_credential: {
      usr: "testuser",
      pwd: "testpass",
      from: "SENDER",
      token: "testtoken",
      sms_service_provider: index % 2 === 0 ? "TELE" : "SAFARICOM"
    }
  };
}

/**
 * Send a single SMS request with retry logic
 */
async function sendSms(data, requestId) {
  const startTime = performance.now();
  
  for (let attempt = 1; attempt <= CONFIG.maxRetries; attempt++) {
    try {
      const response = await axios.post(`${CONFIG.baseUrl}/api/${CONFIG.apiVersion}/sms`, data, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${CONFIG.apiToken}`
        },
        timeout: CONFIG.timeout
      });
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      return {
        requestId,
        status: response.status,
        duration,
        success: response.status === 202 || response.status === 200,
        data: response.data,
        attempt
      };
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (attempt === CONFIG.maxRetries) {
        return {
          requestId,
          status: error.response?.status || 0,
          duration,
          success: false,
          error: error.message,
          attempt
        };
      }
      
      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 100 * attempt));
    }
  }
}

/**
 * Send concurrent requests with better error handling
 */
async function sendConcurrentRequests(count) {
  const promises = [];
  
  for (let i = 0; i < count; i++) {
    const data = generateTestSms(i);
    const requestId = `perf-${Date.now()}-${i}`;
    promises.push(sendSms(data, requestId));
  }
  
  return Promise.allSettled(promises);
}

/**
 * Calculate statistics
 */
function calculateStats(results) {
  const successful = results.filter(r => r.status === 'fulfilled' && r.value.success);
  const failed = results.filter(r => r.status === 'rejected' || !r.value.success);
  
  const durations = successful.map(r => r.value.duration);
  const avgDuration = durations.length > 0 ? durations.reduce((a, b) => a + b, 0) / durations.length : 0;
  const minDuration = durations.length > 0 ? Math.min(...durations) : 0;
  const maxDuration = durations.length > 0 ? Math.max(...durations) : 0;
  
  // Count different error types
  const errorTypes = {};
  failed.forEach(r => {
    const error = r.status === 'rejected' ? 'NETWORK_ERROR' : (r.value.error || 'UNKNOWN');
    errorTypes[error] = (errorTypes[error] || 0) + 1;
  });
  
  return {
    total: results.length,
    successful: successful.length,
    failed: failed.length,
    successRate: (successful.length / results.length) * 100,
    avgDuration,
    minDuration,
    maxDuration,
    throughput: successful.length,
    errorTypes
  };
}

// Test data
const testSmsData = {
  to: '251911234567',
  message: 'Performance test message',
  sms_credential: {
    usr: 'test_user',
    pwd: 'test_password',
    from: 'TEST',
    token: 'test_token_12345'
  }
};

// Statistics tracking
let stats = {
  totalRequests: 0,
  successful: 0,
  failed: 0,
  responseTimes: [],
  startTime: null,
  endTime: null,
  batches: []
};

// Performance monitoring
let performanceMetrics = {
  currentThroughput: 0,
  peakThroughput: 0,
  averageResponseTime: 0,
  minResponseTime: Infinity,
  maxResponseTime: 0,
  errorRate: 0
};

console.log('🚀 Starting Optimized Performance Test for 5K SMS/second');
console.log(`Target: ${CONFIG.targetThroughput.toLocaleString()} SMS/second`);
console.log(`Duration: ${CONFIG.testDuration} seconds`);
console.log(`Concurrent requests per batch: ${CONFIG.concurrentRequests}`);
console.log(`Max retries per request: ${CONFIG.maxRetries}`);
console.log('');

// Test server connectivity
async function testConnectivity() {
  console.log('🔍 Testing server connectivity...');
  
  try {
    // Test health endpoint
    const healthResponse = await axios.get(`${CONFIG.baseUrl}/api/${CONFIG.apiVersion}/health`, {
      timeout: 5000,
      headers: {
        'Authorization': `Bearer ${CONFIG.apiToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`   ✅ Server is responding (Status: ${healthResponse.status})`);
    console.log(`   📊 Health check response: ${JSON.stringify(healthResponse.data, null, 2)}`);
    
    // Check if critical services are up
    const healthData = healthResponse.data;
    const rabbitMQUp = healthData.info?.rabbitmq?.status === 'up';
    const workersUp = healthData.info?.workers?.status === 'up';
    
    if (rabbitMQUp && workersUp) {
      console.log(`   ✅ Critical services are healthy (RabbitMQ: ${rabbitMQUp}, Workers: ${workersUp})`);
    } else {
      console.log(`   ⚠️  Some services may have issues (RabbitMQ: ${rabbitMQUp}, Workers: ${workersUp})`);
    }
    
  } catch (error) {
    console.log(`   ⚠️  Health check failed: ${error.message}`);
    if (error.response) {
      console.log(`   📊 Response status: ${error.response.status}`);
      console.log(`   📊 Response data: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }
  
  // Always test SMS endpoint regardless of health check result
  try {
    const smsResponse = await axios.post(`${CONFIG.baseUrl}/api/${CONFIG.apiVersion}/sms/send/single`, testSmsData, {
      timeout: 5000,
      headers: {
        'Authorization': `Bearer ${CONFIG.apiToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`   ✅ SMS endpoint is working (Status: ${smsResponse.status})`);
    console.log(`   📊 SMS Response: ${JSON.stringify(smsResponse.data, null, 2)}`);
    
    return true;
  } catch (error) {
    console.log(`   ❌ SMS endpoint test failed: ${error.message}`);
    if (error.response) {
      console.log(`   📊 Response status: ${error.response.status}`);
      console.log(`   📊 Response data: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    return false;
  }
}

// Send single SMS request
async function sendSmsRequest(retryCount = 0) {
  const startTime = performance.now();
  
  try {
    const response = await axios.post(`${CONFIG.baseUrl}/api/${CONFIG.apiVersion}/sms/send/single`, testSmsData, {
      timeout: CONFIG.httpTimeout || 5000,
      headers: {
        'Authorization': `Bearer ${CONFIG.apiToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    const endTime = performance.now();
    const responseTime = endTime - startTime;
    
    return {
      success: true,
      responseTime,
      status: response.status,
      data: response.data
    };
  } catch (error) {
    const endTime = performance.now();
    const responseTime = endTime - startTime;
    
    if (retryCount < CONFIG.maxRetries) {
      // Retry with exponential backoff
      const delay = Math.pow(2, retryCount) * 100;
      await new Promise(resolve => setTimeout(resolve, delay));
      return sendSmsRequest(retryCount + 1);
    }
    
    return {
      success: false,
      responseTime,
      error: error.message,
      status: error.response?.status
    };
  }
}

/**
 * Run performance test
 */
async function runPerformanceTest() {
  console.log('🚀 Starting Robust Performance Test');
  console.log(`Target: ${CONFIG.targetThroughput.toLocaleString()} SMS/second`);
  console.log(`Duration: ${CONFIG.testDuration} seconds`);
  console.log(`Concurrent requests per batch: ${CONFIG.concurrentRequests}`);
  console.log(`Max retries per request: ${CONFIG.maxRetries}`);
  console.log('=' * 60);

  // First, test server connectivity
  const serverOk = await testConnectivity();
  if (!serverOk) {
    console.log('\n❌ Server connectivity test failed. Please check if the server is running.');
    console.log('💡 Try: npm run start or npx @nestjs/cli start');
    return;
  }

  console.log('\n📦 Starting performance test...');
  const startTime = performance.now();
  const results = [];
  let batchCount = 0;

  // Run test for specified duration
  const testInterval = setInterval(async () => {
    batchCount++;
    console.log(`\n📦 Sending batch ${batchCount} (${CONFIG.concurrentRequests} concurrent requests)...`);
    
    const batchStartTime = performance.now();
    const batchResults = await sendConcurrentRequests(CONFIG.concurrentRequests);
    const batchEndTime = performance.now();
    const batchDuration = (batchEndTime - batchStartTime) / 1000; // seconds
    
    results.push(...batchResults);
    
    const stats = calculateStats(batchResults);
    console.log(`✅ Batch ${batchCount} completed:`);
    console.log(`   - Success: ${stats.successful}/${stats.total} (${stats.successRate.toFixed(2)}%)`);
    console.log(`   - Avg response time: ${stats.avgDuration.toFixed(2)}ms`);
    console.log(`   - Batch duration: ${batchDuration.toFixed(2)}s`);
    console.log(`   - Throughput: ${(stats.successful / batchDuration).toFixed(2)} SMS/s`);
    
    if (Object.keys(stats.errorTypes).length > 0) {
      console.log(`   - Error types: ${JSON.stringify(stats.errorTypes)}`);
    }
    
  }, 1000); // Send batch every second

  // Stop after test duration
  setTimeout(() => {
    clearInterval(testInterval);
    
    const totalTime = (performance.now() - startTime) / 1000;
    const totalStats = calculateStats(results);
    
    console.log('\n' + '=' * 60);
    console.log('📊 PERFORMANCE TEST RESULTS');
    console.log('=' * 60);
    console.log(`Total time: ${totalTime.toFixed(2)} seconds`);
    console.log(`Total requests: ${totalStats.total}`);
    console.log(`Successful: ${totalStats.successful}`);
    console.log(`Failed: ${totalStats.failed}`);
    console.log(`Success rate: ${totalStats.successRate.toFixed(2)}%`);
    console.log(`Average response time: ${totalStats.avgDuration.toFixed(2)}ms`);
    console.log(`Min response time: ${totalStats.minDuration.toFixed(2)}ms`);
    console.log(`Max response time: ${totalStats.maxDuration.toFixed(2)}ms`);
    console.log(`Overall throughput: ${(totalStats.successful / totalTime).toFixed(2)} SMS/second`);
    
    if (Object.keys(totalStats.errorTypes).length > 0) {
      console.log(`\n❌ Error Analysis:`);
      Object.entries(totalStats.errorTypes).forEach(([error, count]) => {
        console.log(`   - ${error}: ${count} occurrences`);
      });
    }
    
    // Performance assessment
    const achievedThroughput = totalStats.successful / totalTime;
    const targetThroughput = CONFIG.targetThroughput;
    
    console.log('\n🎯 PERFORMANCE ASSESSMENT');
    console.log(`Target throughput: ${targetThroughput.toLocaleString()} SMS/second`);
    console.log(`Achieved throughput: ${achievedThroughput.toLocaleString()} SMS/second`);
    console.log(`Performance ratio: ${(achievedThroughput / targetThroughput * 100).toFixed(2)}%`);
    
    if (achievedThroughput >= targetThroughput * 0.8) {
      console.log('✅ EXCELLENT: System can handle high throughput!');
    } else if (achievedThroughput >= targetThroughput * 0.5) {
      console.log('⚠️  GOOD: System performance is acceptable but can be improved');
    } else if (achievedThroughput > 0) {
      console.log('⚠️  NEEDS IMPROVEMENT: System needs optimization for target throughput');
    } else {
      console.log('❌ CRITICAL: System is not processing any requests successfully');
    }
    
    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    if (totalStats.successRate < 50) {
      console.log('   - Check server logs for errors');
      console.log('   - Verify RabbitMQ connection');
      console.log('   - Check worker processes');
    }
    if (totalStats.avgDuration > 1000) {
      console.log('   - Consider optimizing response times');
      console.log('   - Check database/queue performance');
    }
    if (achievedThroughput < targetThroughput * 0.5) {
      console.log('   - Increase worker count');
      console.log('   - Optimize queue processing');
      console.log('   - Check system resources');
    }
    
    process.exit(0);
  }, CONFIG.testDuration * 1000);
}

// Run the test
runPerformanceTest().catch(console.error); 