const http = require('http');
const { v4: uuidv4 } = require('uuid');

/**
 * Function to send an SMS request
 * @param {string} to - The phone number to send to
 * @param {string} message - The message to send
 * @param {string} provider - The SMS service provider (TELE or SAFARICOM)
 * @returns {Promise<object>} - The response data
 */
function sendSms(to, message, provider) {
  return new Promise((resolve, reject) => {
    // Create the request data
    const data = JSON.stringify({
      message: message,
      to: to,
      sms_credential: {
        usr: "testuser",
        pwd: "testpass",
        from: "SENDER",
        token: "testtoken",
        sms_service_provider: provider
      }
    });

    // Configure the request options
    const options = {
      // hostname: 'localhost',
      hostname: '*************',
      port: 3000,
      path: '/api/v1/sms/send/single',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length,
        'x-api-token': "geezsms-api-token-2025",
      }
    };

    // Send the request
    const req = http.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: parsedData
          });
        } catch (e) {
          reject(new Error(`Failed to parse response: ${e.message}`));
        }
      });
    });

    req.on('error', (e) => {
      reject(new Error(`Problem with request: ${e.message}`));
    });

    // Write the request data
    req.write(data);
    req.end();
  });
}

/**
 * Run a load test with the specified number of concurrent requests
 * @param {number} concurrentRequests - The number of concurrent requests to send
 */
async function runLoadTest(concurrentRequests) {
  console.log(`Starting load test with ${concurrentRequests} concurrent requests...`);
  
  const startTime = Date.now();
  
  // Create an array of promises for the requests
  const requests = [];
  
  for (let i = 0; i < concurrentRequests; i++) {
    // Alternate between TELE and SAFARICOM providers
    const provider = i % 2 === 0 ? 'TELE' : 'SAFARICOM';
    
    // Generate a phone number based on the provider
    const phoneNumber = provider === 'TELE' 
      ? `2519${String(i).padStart(8, '0')}` 
      : `2517${String(i).padStart(8, '0')}`;
    
    // Generate a unique message
    const message = `Test message ${i} - ${uuidv4().substring(0, 8)}`;
    
    // Add the request to the array
    requests.push(sendSms(phoneNumber, message, provider));
  }
  
  try {
    // Send all requests simultaneously
    const responses = await Promise.all(requests);
    
    // Calculate statistics
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    const successCount = responses.filter(r => r.status === 202).length;
    const failureCount = responses.length - successCount;
    
    // Log the results
    console.log('\n=== Load Test Results ===');
    console.log(`Total requests: ${responses.length}`);
    console.log(`Successful requests: ${successCount}`);
    console.log(`Failed requests: ${failureCount}`);
    console.log(`Total time: ${totalTime}ms`);
    console.log(`Average time per request: ${totalTime / responses.length}ms`);
    console.log(`Requests per second: ${(responses.length / totalTime) * 1000}`);
    
    // Check if all requests were successful
    if (successCount === responses.length) {
      console.log('\nLoad test completed successfully!');
    } else {
      console.log('\nLoad test completed with some failures.');
    }
  } catch (error) {
    console.error('Error during load test:', error.message);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const concurrentRequests = parseInt(args[0]) || 50; // Default to 50 concurrent requests

// Run the load test
runLoadTest(concurrentRequests);
