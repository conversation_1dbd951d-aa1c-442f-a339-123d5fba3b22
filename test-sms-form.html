<!DOCTYPE html>
<html>
<head>
    <title>Test SMS API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        #response {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            white-space: pre-wrap;
            display: none;
        }
    </style>
</head>
<body>
    <h1>Test SMS API</h1>
    
    <form id="smsForm">
        <div class="form-group">
            <label for="message">Message:</label>
            <textarea id="message" name="message" rows="4" required>Testing interfaces with utility functions</textarea>
        </div>
        
        <div class="form-group">
            <label for="to">Phone Number:</label>
            <input type="text" id="to" name="to" value="251912345678" required>
        </div>
        
        <div class="form-group">
            <label for="usr">Username:</label>
            <input type="text" id="usr" name="usr" value="testuser" required>
        </div>
        
        <div class="form-group">
            <label for="pwd">Password:</label>
            <input type="password" id="pwd" name="pwd" value="testpass" required>
        </div>
        
        <div class="form-group">
            <label for="from">From:</label>
            <input type="text" id="from" name="from" value="SENDER" required>
        </div>
        
        <div class="form-group">
            <label for="token">Token:</label>
            <input type="text" id="token" name="token" value="testtoken" required>
        </div>
        
        <button type="submit">Send SMS</button>
    </form>
    
    <div id="response"></div>
    
    <script>
        document.getElementById('smsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const message = document.getElementById('message').value;
            const to = document.getElementById('to').value;
            const usr = document.getElementById('usr').value;
            const pwd = document.getElementById('pwd').value;
            const from = document.getElementById('from').value;
            const token = document.getElementById('token').value;
            
            const data = {
                message: message,
                to: to,
                sms_credential: {
                    usr: usr,
                    pwd: pwd,
                    from: from,
                    token: token
                }
            };
            
            fetch('http://localhost:3000/api/v1/sms/send/single', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                const responseElement = document.getElementById('response');
                responseElement.textContent = JSON.stringify(data, null, 2);
                responseElement.style.display = 'block';
            })
            .catch(error => {
                const responseElement = document.getElementById('response');
                responseElement.textContent = 'Error: ' + error.message;
                responseElement.style.display = 'block';
            });
        });
    </script>
</body>
</html>
