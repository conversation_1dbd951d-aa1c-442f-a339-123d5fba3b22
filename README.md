# GeezSMS - SMS Processing System

A scalable, modular NestJS application for processing SMS messages with worker threads and RabbitMQ.

## Architecture

GeezSMS is built with a modular architecture consisting of:

- **SMS Module**: Handles SMS business logic and API endpoints
- **RabbitMQ Module**: Manages message queuing and communication
- **Worker Module**: Processes SMS messages in separate threads
- **Health Module**: Monitors system health and provides health check endpoints

## Features

- **Scalable Worker Architecture**: Automatically scales based on CPU cores
- **Worker Categorization**: Route specific messages to specialized workers
- **Fault Tolerance**: Automatic worker replacement and message retries
- **Health Monitoring**: Comprehensive health checks for all components
- **Modular Design**: Clean separation of concerns for maintainability

## Prerequisites

- Node.js (v14+)
- RabbitMQ
- npm or yarn

## Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/geezsms.git
cd geezsms

# Install dependencies
npm install

# Create environment file
cp .env.example .env
# Edit .env with your configuration
```

## Running the Application

```bash
# Development mode (without workers)
npm run start:dev

# With default worker configuration (4 workers)
npm run start:workers

# With high-priority workers (4 workers focused on high-priority messages)
npm run start:workers:high

# With bulk processing workers (8 workers focused on bulk processing)
npm run start:workers:bulk

# With maximum workers based on CPU count
npm run start:workers:max

# Production mode
npm run start:prod
```

## API Endpoints

### SMS Endpoints

- `POST /api/v1/sms/send/single` - Send a single SMS
- `POST /api/v1/sms/send/bulk` - Send multiple SMS messages

### Health Endpoints

- `GET /api/v1/health` - Overall system health
- `GET /api/v1/health/workers` - Worker health status

## Worker Categories

Workers can be assigned to specific categories:

- `default`: Handles general SMS messages
- `single`: Handles single, individual SMS messages.
- `bulk`: Optimized for bulk SMS processing.

## Configuration

Configuration is managed through environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Application port | 3000 |
| `RABBITMQ_URL` | RabbitMQ connection URL | amqp://localhost:5672 |
| `SMS_RETRIES` | Number of retry attempts | 3 |
| `SMS_WORKER_COUNT` | Number of worker threads | CPU cores - 1 |
| `SMS_WORKER_CATEGORIES` | Worker categories | single,bulk |

See `.env.example` for all available configuration options.

## Project Structure

```
src/
├── app.module.ts              # Main application module
├── main.ts                    # Application entry point
├── config/                    # Configuration
├── health/                    # Health monitoring
│   ├── health.controller.ts   # Health endpoints
│   └── health.module.ts       # Health module
├── rabbitmq/                  # RabbitMQ module
│   ├── rabbitmq.module.ts     # RabbitMQ module definition
│   ├── services/              # RabbitMQ services
│   └── health/                # RabbitMQ health indicators
├── sms/                       # SMS module
│   ├── sms.module.ts          # SMS module definition
│   ├── controllers/           # SMS controllers
│   ├── services/              # SMS services
│   └── dto/                   # Data transfer objects
└── worker/                    # Worker module
    ├── worker.module.ts       # Worker module definition
    ├── services/              # Worker services
    ├── health/                # Worker health indicators
    └── threads/               # Worker thread implementations
```

## Testing

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov
```

## Performance Testing

Use the included test script to verify performance under load:

```bash
node test-workers.js
```

This will send 100 SMS requests (50 concurrent) and measure performance.

## License

[MIT](LICENSE)
