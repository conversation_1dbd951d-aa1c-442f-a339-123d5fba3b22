const axios = require('axios');
const { performance } = require('perf_hooks');

// Memory-safe test configuration
const CONFIG = {
  targetThroughput: 5000, // SMS per second
  testDuration: 30, // seconds
  initialConcurrentRequests: 50, // Start with lower concurrency
  maxConcurrentRequests: 200, // Maximum concurrency
  maxRetries: 2,
  baseUrl: 'http://localhost:3000',
  apiToken: 'ccda1683d8c97f8f2dff2ea7d649b42c',
  apiVersion: 'v1'
};

// Test data
const testSmsData = {
  to: '251911234567',
  message: 'Memory-safe test message',
  sms_credential: {
    usr: 'test_user',
    pwd: 'test_password',
    from: 'TEST',
    token: 'test_token_12345'
  }
};

// Statistics tracking
let stats = {
  totalRequests: 0,
  successful: 0,
  failed: 0,
  responseTimes: [],
  startTime: null,
  endTime: null,
  batches: []
};

console.log('🚀 Starting Memory-Safe Performance Test');
console.log(`Target: ${CONFIG.targetThroughput.toLocaleString()} SMS/second`);
console.log(`Duration: ${CONFIG.testDuration} seconds`);
console.log(`Initial concurrent requests: ${CONFIG.initialConcurrentRequests}`);
console.log(`Max concurrent requests: ${CONFIG.maxConcurrentRequests}`);
console.log(`Max retries per request: ${CONFIG.maxRetries}`);
console.log('');

// Test server connectivity
async function testConnectivity() {
  console.log('🔍 Testing server connectivity...');
  
  try {
    // Test health endpoint
    const healthResponse = await axios.get(`${CONFIG.baseUrl}/api/${CONFIG.apiVersion}/health`, {
      timeout: 10000,
      headers: {
        'Authorization': `Bearer ${CONFIG.apiToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`   ✅ Server is responding (Status: ${healthResponse.status})`);
    
    // Check worker count
    const healthData = healthResponse.data;
    const workerInfo = healthData.info?.workers;
    if (workerInfo) {
      console.log(`   👥 Workers: ${workerInfo.healthyWorkers}/${workerInfo.totalWorkers} healthy`);
      console.log(`   🎯 Target throughput: ${workerInfo.targetThroughput} SMS/second`);
    }
    
    // Test SMS endpoint
    const smsResponse = await axios.post(`${CONFIG.baseUrl}/api/${CONFIG.apiVersion}/sms/send/single`, testSmsData, {
      timeout: 10000,
      headers: {
        'Authorization': `Bearer ${CONFIG.apiToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`   ✅ SMS endpoint is working (Status: ${smsResponse.status})`);
    
    return true;
  } catch (error) {
    console.log(`   ❌ Connectivity test failed: ${error.message}`);
    return false;
  }
}

// Send single SMS request with memory-safe error handling
async function sendSmsRequest(retryCount = 0) {
  const startTime = performance.now();
  
  try {
    const response = await axios.post(`${CONFIG.baseUrl}/api/${CONFIG.apiVersion}/sms/send/single`, testSmsData, {
      timeout: 15000,
      headers: {
        'Authorization': `Bearer ${CONFIG.apiToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    const endTime = performance.now();
    const responseTime = endTime - startTime;
    
    return {
      success: true,
      responseTime,
      status: response.status,
      data: response.data
    };
  } catch (error) {
    const endTime = performance.now();
    const responseTime = endTime - startTime;
    
    if (retryCount < CONFIG.maxRetries) {
      // Retry with exponential backoff
      const delay = Math.pow(2, retryCount) * 100;
      await new Promise(resolve => setTimeout(resolve, delay));
      return sendSmsRequest(retryCount + 1);
    }
    
    return {
      success: false,
      responseTime,
      error: error.message,
      status: error.response?.status
    };
  }
}

// Send batch with memory management
async function sendBatch(concurrentRequests, batchNumber) {
  console.log(`📦 Sending batch ${batchNumber} (${concurrentRequests} concurrent requests)...`);
  
  const batchStartTime = performance.now();
  
  // Process requests in smaller chunks to prevent memory issues
  const chunkSize = Math.min(concurrentRequests, 25);
  const chunks = Math.ceil(concurrentRequests / chunkSize);
  const allResults = [];
  
  for (let chunk = 0; chunk < chunks; chunk++) {
    const chunkStart = chunk * chunkSize;
    const chunkEnd = Math.min(chunkStart + chunkSize, concurrentRequests);
    const chunkSizeActual = chunkEnd - chunkStart;
    
    const promises = Array.from({ length: chunkSizeActual }, (_, i) => {
      const data = {
        ...testSmsData,
        message: `Batch ${batchNumber} - Chunk ${chunk + 1} - Message ${i + 1}`
      };
      
      return sendSmsRequest();
    });
    
    const chunkResults = await Promise.all(promises);
    allResults.push(...chunkResults);
    
    // Small delay between chunks to prevent overwhelming the system
    if (chunk < chunks - 1) {
      await new Promise(resolve => setTimeout(resolve, 50));
    }
  }
  
  const batchEndTime = performance.now();
  const batchDuration = (batchEndTime - batchStartTime) / 1000;
  
  // Process results
  const successful = allResults.filter(r => r.success).length;
  const failed = allResults.filter(r => !r.success).length;
  const responseTimes = allResults.map(r => r.responseTime);
  const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
  const throughput = successful / batchDuration;
  
  // Update statistics
  stats.totalRequests += concurrentRequests;
  stats.successful += successful;
  stats.failed += failed;
  stats.responseTimes.push(...responseTimes);
  
  // Store batch results
  stats.batches.push({
    batchNumber,
    concurrentRequests,
    successful,
    failed,
    avgResponseTime,
    batchDuration,
    throughput
  });
  
  console.log(`✅ Batch ${batchNumber} completed:`);
  console.log(`   - Success: ${successful}/${concurrentRequests} (${((successful / concurrentRequests) * 100).toFixed(2)}%)`);
  console.log(`   - Avg response time: ${avgResponseTime.toFixed(2)}ms`);
  console.log(`   - Batch duration: ${batchDuration.toFixed(2)}s`);
  console.log(`   - Throughput: ${throughput.toFixed(2)} SMS/s`);
  
  if (failed > 0) {
    const errorTypes = {};
    allResults.filter(r => !r.success).forEach(r => {
      const errorKey = r.error || `Status ${r.status}`;
      errorTypes[errorKey] = (errorTypes[errorKey] || 0) + 1;
    });
    console.log(`   - Error types: ${JSON.stringify(errorTypes)}`);
  }
  
  return { successful, failed, throughput, avgResponseTime };
}

// Calculate final statistics
function calculateFinalStats() {
  const totalTime = (stats.endTime - stats.startTime) / 1000;
  const successRate = (stats.successful / stats.totalRequests) * 100;
  const avgResponseTime = stats.responseTimes.reduce((a, b) => a + b, 0) / stats.responseTimes.length;
  const minResponseTime = Math.min(...stats.responseTimes);
  const maxResponseTime = Math.max(...stats.responseTimes);
  const overallThroughput = stats.successful / totalTime;
  
  return {
    totalTime,
    totalRequests: stats.totalRequests,
    successful: stats.successful,
    failed: stats.failed,
    successRate,
    avgResponseTime,
    minResponseTime,
    maxResponseTime,
    overallThroughput
  };
}

// Display final results
function displayResults(finalStats) {
  console.log('');
  console.log('📊 MEMORY-SAFE PERFORMANCE TEST RESULTS');
  console.log('========================================');
  console.log(`Total time: ${finalStats.totalTime.toFixed(2)} seconds`);
  console.log(`Total requests: ${finalStats.totalRequests.toLocaleString()}`);
  console.log(`Successful: ${finalStats.successful.toLocaleString()}`);
  console.log(`Failed: ${finalStats.failed.toLocaleString()}`);
  console.log(`Success rate: ${finalStats.successRate.toFixed(2)}%`);
  console.log(`Average response time: ${finalStats.avgResponseTime.toFixed(2)}ms`);
  console.log(`Min response time: ${finalStats.minResponseTime.toFixed(2)}ms`);
  console.log(`Max response time: ${finalStats.maxResponseTime.toFixed(2)}ms`);
  console.log(`Overall throughput: ${finalStats.overallThroughput.toFixed(2)} SMS/second`);
  console.log('');
  
  // Performance assessment
  const performanceRatio = (finalStats.overallThroughput / CONFIG.targetThroughput) * 100;
  console.log('🎯 PERFORMANCE ASSESSMENT');
  console.log(`Target throughput: ${CONFIG.targetThroughput.toLocaleString()} SMS/second`);
  console.log(`Achieved throughput: ${finalStats.overallThroughput.toFixed(2)} SMS/second`);
  console.log(`Performance ratio: ${performanceRatio.toFixed(2)}%`);
  
  if (performanceRatio >= 100) {
    console.log('✅ EXCELLENT: Target throughput achieved!');
  } else if (performanceRatio >= 80) {
    console.log('🟡 GOOD: Close to target throughput');
  } else if (performanceRatio >= 50) {
    console.log('🟠 MODERATE: Significant improvement needed');
  } else {
    console.log('🔴 NEEDS IMPROVEMENT: System needs optimization for target throughput');
  }
}

// Main test execution
async function runMemorySafeTest() {
  // Test connectivity first
  const isConnected = await testConnectivity();
  if (!isConnected) {
    console.log('❌ Cannot proceed with performance test due to connectivity issues');
    return;
  }
  
  console.log('');
  console.log('📦 Starting memory-safe performance test...');
  console.log('');
  
  stats.startTime = performance.now();
  
  // Adaptive concurrency based on performance
  let currentConcurrency = CONFIG.initialConcurrentRequests;
  let batchNumber = 1;
  const startTime = performance.now();
  
  while ((performance.now() - startTime) / 1000 < CONFIG.testDuration) {
    const result = await sendBatch(currentConcurrency, batchNumber);
    
    // Adjust concurrency based on performance
    if (result.successful / currentConcurrency >= 0.95 && result.throughput > 100) {
      // Good performance, increase concurrency
      currentConcurrency = Math.min(currentConcurrency * 1.2, CONFIG.maxConcurrentRequests);
      console.log(`📈 Increasing concurrency to ${Math.round(currentConcurrency)}`);
    } else if (result.successful / currentConcurrency < 0.8) {
      // Poor performance, decrease concurrency
      currentConcurrency = Math.max(currentConcurrency * 0.8, 10);
      console.log(`📉 Decreasing concurrency to ${Math.round(currentConcurrency)}`);
    }
    
    batchNumber++;
    
    // Wait between batches to prevent overwhelming the system
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  
  stats.endTime = performance.now();
  
  // Calculate and display results
  const finalStats = calculateFinalStats();
  displayResults(finalStats);
}

// Run the test
runMemorySafeTest().catch(error => {
  console.error('❌ Memory-safe test failed:', error.message);
  process.exit(1);
}); 