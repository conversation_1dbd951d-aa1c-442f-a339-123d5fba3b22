const axios = require('axios');
const { performance } = require('perf_hooks');
const SystemMonitor = require('./system-monitor');

// High-throughput test configuration
const CONFIG = {
  targetThroughput: 5000, // SMS per second
  testDuration: 60, // seconds
  concurrentRequests: 500, // per batch
  maxRetries: 2,
  baseUrl: 'http://localhost:3000',
  apiToken: 'ccda1683d8c97f8f2dff2ea7d649b42c',
  apiVersion: 'v1'
};

// Test data
const testSmsData = {
  to: '251911234567',
  message: 'High-throughput test message',
  sms_credential: {
    usr: 'test_user',
    pwd: 'test_password',
    from: 'TEST',
    token: 'test_token_12345'
  }
};

// Statistics tracking
let stats = {
  totalRequests: 0,
  successful: 0,
  failed: 0,
  responseTimes: [],
  startTime: null,
  endTime: null,
  batches: []
};

// Performance monitoring
let performanceMetrics = {
  currentThroughput: 0,
  peakThroughput: 0,
  averageResponseTime: 0,
  minResponseTime: Infinity,
  maxResponseTime: 0,
  errorRate: 0
};

// System monitor
let systemMonitor = null;

console.log('🚀 Starting High-Throughput Performance Test');
console.log(`Target: ${CONFIG.targetThroughput.toLocaleString()} SMS/second`);
console.log(`Duration: ${CONFIG.testDuration} seconds`);
console.log(`Concurrent requests per batch: ${CONFIG.concurrentRequests}`);
console.log(`Max retries per request: ${CONFIG.maxRetries}`);
console.log('');

// Test server connectivity
async function testConnectivity() {
  console.log('🔍 Testing server connectivity...');
  
  try {
    // Test health endpoint
    const healthResponse = await axios.get(`${CONFIG.baseUrl}/api/${CONFIG.apiVersion}/health`, {
      timeout: 10000,
      headers: {
        'Authorization': `Bearer ${CONFIG.apiToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`   ✅ Server is responding (Status: ${healthResponse.status})`);
    
    // Check worker count
    const healthData = healthResponse.data;
    const workerInfo = healthData.info?.workers;
    if (workerInfo) {
      console.log(`   👥 Workers: ${workerInfo.healthyWorkers}/${workerInfo.totalWorkers} healthy`);
      console.log(`   🎯 Target throughput: ${workerInfo.targetThroughput} SMS/second`);
    }
    
    // Test SMS endpoint
    const smsResponse = await axios.post(`${CONFIG.baseUrl}/api/${CONFIG.apiVersion}/sms/send/single`, testSmsData, {
      timeout: 10000,
      headers: {
        'Authorization': `Bearer ${CONFIG.apiToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`   ✅ SMS endpoint is working (Status: ${smsResponse.status})`);
    
    return true;
  } catch (error) {
    console.log(`   ❌ Connectivity test failed: ${error.message}`);
    return false;
  }
}

// Send single SMS request
async function sendSmsRequest(retryCount = 0) {
  const startTime = performance.now();
  
  try {
    const response = await axios.post(`${CONFIG.baseUrl}/api/${CONFIG.apiVersion}/sms/send/single`, testSmsData, {
      timeout: 15000,
      headers: {
        'Authorization': `Bearer ${CONFIG.apiToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    const endTime = performance.now();
    const responseTime = endTime - startTime;
    
    return {
      success: true,
      responseTime,
      status: response.status,
      data: response.data
    };
  } catch (error) {
    const endTime = performance.now();
    const responseTime = endTime - startTime;
    
    if (retryCount < CONFIG.maxRetries) {
      // Retry with exponential backoff
      const delay = Math.pow(2, retryCount) * 100;
      await new Promise(resolve => setTimeout(resolve, delay));
      return sendSmsRequest(retryCount + 1);
    }
    
    return {
      success: false,
      responseTime,
      error: error.message,
      status: error.response?.status
    };
  }
}

// Send batch of concurrent requests
async function sendBatch(batchNumber) {
  console.log(`📦 Sending batch ${batchNumber} (${CONFIG.concurrentRequests} concurrent requests)...`);
  
  const batchStartTime = performance.now();
  const promises = Array.from({ length: CONFIG.concurrentRequests }, (_, i) => {
    const data = {
      ...testSmsData,
      message: `Batch ${batchNumber} - Message ${i + 1}`
    };
    
    return sendSmsRequest();
  });
  
  const results = await Promise.all(promises);
  const batchEndTime = performance.now();
  const batchDuration = (batchEndTime - batchStartTime) / 1000;
  
  // Process results
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  const responseTimes = results.map(r => r.responseTime);
  const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
  const throughput = successful / batchDuration;
  
  // Update statistics
  stats.totalRequests += CONFIG.concurrentRequests;
  stats.successful += successful;
  stats.failed += failed;
  stats.responseTimes.push(...responseTimes);
  
  // Update performance metrics
  performanceMetrics.currentThroughput = throughput;
  performanceMetrics.peakThroughput = Math.max(performanceMetrics.peakThroughput, throughput);
  performanceMetrics.minResponseTime = Math.min(performanceMetrics.minResponseTime, ...responseTimes);
  performanceMetrics.maxResponseTime = Math.max(performanceMetrics.maxResponseTime, ...responseTimes);
  
  // Store batch results
  stats.batches.push({
    batchNumber,
    successful,
    failed,
    avgResponseTime,
    batchDuration,
    throughput
  });
  
  console.log(`✅ Batch ${batchNumber} completed:`);
  console.log(`   - Success: ${successful}/${CONFIG.concurrentRequests} (${((successful / CONFIG.concurrentRequests) * 100).toFixed(2)}%)`);
  console.log(`   - Avg response time: ${avgResponseTime.toFixed(2)}ms`);
  console.log(`   - Batch duration: ${batchDuration.toFixed(2)}s`);
  console.log(`   - Throughput: ${throughput.toFixed(2)} SMS/s`);
  
  if (failed > 0) {
    const errorTypes = {};
    results.filter(r => !r.success).forEach(r => {
      const errorKey = r.error || `Status ${r.status}`;
      errorTypes[errorKey] = (errorTypes[errorKey] || 0) + 1;
    });
    console.log(`   - Error types: ${JSON.stringify(errorTypes)}`);
  }
  
  return { successful, failed, throughput, avgResponseTime };
}

// Calculate final statistics
function calculateFinalStats() {
  const totalTime = (stats.endTime - stats.startTime) / 1000;
  const successRate = (stats.successful / stats.totalRequests) * 100;
  const avgResponseTime = stats.responseTimes.reduce((a, b) => a + b, 0) / stats.responseTimes.length;
  const minResponseTime = Math.min(...stats.responseTimes);
  const maxResponseTime = Math.max(...stats.responseTimes);
  const overallThroughput = stats.successful / totalTime;
  
  performanceMetrics.averageResponseTime = avgResponseTime;
  performanceMetrics.errorRate = (stats.failed / stats.totalRequests) * 100;
  
  return {
    totalTime,
    totalRequests: stats.totalRequests,
    successful: stats.successful,
    failed: stats.failed,
    successRate,
    avgResponseTime,
    minResponseTime,
    maxResponseTime,
    overallThroughput,
    peakThroughput: performanceMetrics.peakThroughput
  };
}

// Display final results
function displayResults(finalStats) {
  console.log('');
  console.log('📊 HIGH-THROUGHPUT PERFORMANCE TEST RESULTS');
  console.log('============================================');
  console.log(`Total time: ${finalStats.totalTime.toFixed(2)} seconds`);
  console.log(`Total requests: ${finalStats.totalRequests.toLocaleString()}`);
  console.log(`Successful: ${finalStats.successful.toLocaleString()}`);
  console.log(`Failed: ${finalStats.failed.toLocaleString()}`);
  console.log(`Success rate: ${finalStats.successRate.toFixed(2)}%`);
  console.log(`Average response time: ${finalStats.avgResponseTime.toFixed(2)}ms`);
  console.log(`Min response time: ${finalStats.minResponseTime.toFixed(2)}ms`);
  console.log(`Max response time: ${finalStats.maxResponseTime.toFixed(2)}ms`);
  console.log(`Overall throughput: ${finalStats.overallThroughput.toFixed(2)} SMS/second`);
  console.log(`Peak throughput: ${finalStats.peakThroughput.toFixed(2)} SMS/second`);
  console.log('');
  
  // Performance assessment
  const performanceRatio = (finalStats.overallThroughput / CONFIG.targetThroughput) * 100;
  console.log('🎯 PERFORMANCE ASSESSMENT');
  console.log(`Target throughput: ${CONFIG.targetThroughput.toLocaleString()} SMS/second`);
  console.log(`Achieved throughput: ${finalStats.overallThroughput.toFixed(2)} SMS/second`);
  console.log(`Performance ratio: ${performanceRatio.toFixed(2)}%`);
  
  if (performanceRatio >= 100) {
    console.log('✅ EXCELLENT: Target throughput achieved!');
  } else if (performanceRatio >= 80) {
    console.log('🟡 GOOD: Close to target throughput');
  } else if (performanceRatio >= 50) {
    console.log('🟠 MODERATE: Significant improvement needed');
  } else {
    console.log('🔴 NEEDS IMPROVEMENT: System needs optimization for target throughput');
  }
  
  console.log('');
  console.log('💡 RECOMMENDATIONS:');
  if (performanceRatio < 100) {
    console.log('   - Increase worker count further');
    console.log('   - Optimize queue processing');
    console.log('   - Check system resources');
    console.log('   - Review network latency');
    console.log('   - Consider batch processing');
  } else {
    console.log('   - System is performing excellently!');
    console.log('   - Consider stress testing for higher loads');
    console.log('   - Monitor for sustained performance');
  }
}

// Main test execution
async function runHighThroughputTest() {
  // Test connectivity first
  const isConnected = await testConnectivity();
  if (!isConnected) {
    console.log('❌ Cannot proceed with performance test due to connectivity issues');
    return;
  }
  
  console.log('');
  console.log('📦 Starting high-throughput performance test...');
  console.log('');
  
  // Start system monitoring
  systemMonitor = new SystemMonitor();
  await systemMonitor.startMonitoring(5000);
  
  stats.startTime = performance.now();
  
  // Calculate number of batches needed
  const totalRequestsNeeded = CONFIG.targetThroughput * CONFIG.testDuration;
  const numBatches = Math.ceil(totalRequestsNeeded / CONFIG.concurrentRequests);
  const batchInterval = (CONFIG.testDuration * 1000) / numBatches;
  
  console.log(`📈 Test plan: ${numBatches} batches over ${CONFIG.testDuration} seconds`);
  console.log(`⏱️  Batch interval: ${batchInterval.toFixed(0)}ms`);
  console.log('');
  
  // Send batches with controlled timing
  for (let i = 1; i <= numBatches; i++) {
    await sendBatch(i);
    
    // Wait for next batch if not the last one
    if (i < numBatches) {
      await new Promise(resolve => setTimeout(resolve, batchInterval));
    }
  }
  
  stats.endTime = performance.now();
  
  // Stop system monitoring
  if (systemMonitor) {
    systemMonitor.stopMonitoring();
    const systemReport = systemMonitor.generateReport();
    console.log('');
    console.log('🔍 SYSTEM PERFORMANCE REPORT');
    console.log('============================');
    console.log(`Duration: ${systemReport.duration.toFixed(2)} seconds`);
    console.log(`Average Load: ${systemReport.averages.load.toFixed(2)}`);
    console.log(`Average CPU: ${systemReport.averages.cpu.toFixed(1)}%`);
    console.log(`Average Memory: ${systemReport.averages.memory.toFixed(1)}%`);
    console.log(`Peak Load: ${systemReport.peaks.load.toFixed(2)}`);
    console.log(`Peak CPU: ${systemReport.peaks.cpu.toFixed(1)}%`);
    console.log(`Peak Memory: ${systemReport.peaks.memory.toFixed(1)}%`);
  }
  
  // Calculate and display results
  const finalStats = calculateFinalStats();
  displayResults(finalStats);
}

// Run the test
runHighThroughputTest().catch(error => {
  console.error('❌ High-throughput test failed:', error.message);
  if (systemMonitor) {
    systemMonitor.stopMonitoring();
  }
  process.exit(1);
}); 