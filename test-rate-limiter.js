const http = require('http');

/**
 * Make a request to the API
 * @param {string} method - The HTTP method
 * @param {string} path - The path to request
 * @param {object} body - The request body (for POST/PUT/PATCH)
 * @returns {Promise<object>} - The response
 */
function makeRequest(method, path, body = null) {
  return new Promise((resolve, reject) => {
    // Configure the request options
    const options = {
      hostname: 'localhost',
      port: 3000,
      path,
      method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    // Create the request
    const req = http.request(options, (res) => {
      let data = '';

      // Collect the response data
      res.on('data', (chunk) => {
        data += chunk;
      });

      // Resolve the promise when the response is complete
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: parsedData
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: data
          });
        }
      });
    });

    // Handle request errors
    req.on('error', (error) => {
      reject(error);
    });

    // Send the request body if provided
    if (body) {
      req.write(JSON.stringify(body));
    }

    // End the request
    req.end();
  });
}

/**
 * Test the rate limiter middleware by making many requests in a short time
 */
async function testRateLimiter() {
  console.log('Testing rate limiter middleware...');

  try {
    // Make many requests to the config endpoint
    const numRequests = 20; // More than our configured limit of 10
    const requests = [];

    console.log(`Making ${numRequests} requests to the config endpoint...`);

    for (let i = 0; i < numRequests; i++) {
      requests.push(makeRequest('GET', '/api/v1/config'));
    }

    // Wait for all requests to complete
    const responses = await Promise.all(requests);

    // Count responses by status code
    const statusCounts = {};
    responses.forEach(response => {
      const status = response.statusCode;
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });

    console.log('\nResponse status counts:');
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`  ${status}: ${count}`);
    });

    // Check if rate limiting is working
    if (statusCounts['429']) {
      console.log('\nRate limiting is working! Some requests were rate limited.');

      // Get a rate-limited response
      const rateLimitedResponse = responses.find(r => r.statusCode === 429);
      if (rateLimitedResponse) {
        console.log('\nRate limit headers:');
        console.log(`  X-RateLimit-Limit: ${rateLimitedResponse.headers['x-ratelimit-limit']}`);
        console.log(`  X-RateLimit-Remaining: ${rateLimitedResponse.headers['x-ratelimit-remaining']}`);
        console.log(`  X-RateLimit-Reset: ${rateLimitedResponse.headers['x-ratelimit-reset']}`);
        console.log(`  Retry-After: ${rateLimitedResponse.headers['retry-after']}`);
      }
    } else {
      console.log('\nRate limiting does not appear to be working. No requests were rate limited.');
    }

    console.log('\nRate limiter test completed!');
  } catch (error) {
    console.error('Error testing rate limiter:', error.message);
  }
}

// Run the test
testRateLimiter();
