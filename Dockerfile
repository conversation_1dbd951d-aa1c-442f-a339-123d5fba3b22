# Stage 1: Build the application
FROM node:18-alpine AS builder

# Set the working directory inside the container
WORKDIR /app

# Copy package.json and package-lock.json to the working directory
COPY package*.json ./

# Install the application dependencies
RUN npm ci

# Copy the rest of the application files
COPY . .

# Build the NestJS application
RUN npm run build

# Stage 2: Run the application
FROM node:18-alpine

# Set the working directory inside the container
WORKDIR /app

# Copy package.json and package-lock.json to the working directory
COPY package*.json ./

# Install the production dependencies
RUN npm ci --only=production

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist
# COPY --from=builder /app/src/worker/threads/sms-worker-thread.js ./dist/src/worker/threads/
COPY --from=builder /app/src/worker/threads/* ./dist/src/worker/threads/

# Create logs directory
RUN mkdir -p logs

# Set environment variables
ENV SMS_WORKER_COUNT=4

# Expose the port
EXPOSE ${PORT}

# Start the application
CMD ["node", "dist/src/main"]