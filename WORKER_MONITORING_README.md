# GeezSMS Worker Monitoring and Persistence System

This document describes the comprehensive worker monitoring and SMS persistence system implemented in GeezSMS.

## Features Implemented

### 1. Environment Configuration

The system now supports the following new environment variables:

```bash
# Worker Throughput Settings
WORKER_SMS_THROUGHPUT_PER_SECOND=5000
WORKER_MAX_CONCURRENT_MESSAGES=100

# Provider URLs
TELE_ENDPOINT=https://api.tele.com/send

# Persistence Settings
ENABLE_FAILED_SMS_PERSISTENCE=true
FAILED_SMS_STORAGE_PATH=./data/failed_sms
PENDING_SMS_STORAGE_PATH=./data/pending_sms

# Additional Queue Configuration
QUEUE_SMS_FAILED=sms_failed_queue
QUEUE_SMS_PENDING=sms_pending_queue
```

### 2. Worker Throughput Configuration

- **WORKER_SMS_THROUGHPUT_PER_SECOND**: Sets the maximum SMS throughput per second for each worker
- **WORKER_MAX_CONCURRENT_MESSAGES**: Sets the maximum number of concurrent messages a worker can process

### 3. Tele Endpoint Configuration

- **TELE_ENDPOINT**: Configurable endpoint for Tele SMS provider
- Falls back to TELE_JASMINE_API_URL if not set
- Defaults to 'https://api.tele.com/send'

### 4. API Token Configuration

- **API_AUTH_TOKEN**: Set your API authentication token in the environment
- Used for securing API endpoints

### 5. Worker Health Monitoring

The system provides comprehensive worker health monitoring through the `/worker-status` endpoint:

#### Main Status Endpoint: `GET /worker-status`

Returns detailed information including:
- Overall system status
- Total and active worker counts
- Success rates and throughput metrics
- Individual worker metrics
- Queue statistics
- Persistence statistics
- Configuration details

#### Health Check Endpoint: `GET /worker-status/health`

Returns basic worker health information.

#### Metrics Endpoint: `GET /worker-status/metrics`

Returns detailed performance metrics.

### 6. Failed SMS Persistence

The system now persists failed SMS messages to disk, ensuring they can be retried after server restarts.

#### Features:
- **Automatic Persistence**: Failed SMS messages are automatically saved to disk
- **Configurable Storage**: Storage paths can be configured via environment variables
- **JSON Format**: Messages are stored in human-readable JSON format
- **Metadata Tracking**: Includes retry count, error details, and timestamps

#### Failed SMS Endpoint: `GET /worker-status/failed-sms`

Returns all failed SMS messages from persistence.

### 7. Pending SMS Tracking

The system tracks pending SMS messages for monitoring purposes.

#### Pending SMS Endpoint: `GET /worker-status/pending-sms`

Returns all pending SMS messages from persistence.

### 8. Retry Mechanism

#### Automatic Retry on Startup
- Failed SMS messages are automatically retried when the server starts
- Uses exponential backoff for retry delays
- Respects maximum retry limits

#### Manual Retry Endpoints

##### Retry All Failed SMS: `GET /worker-status/retry-all`
Attempts to retry all failed SMS messages that haven't reached the maximum retry limit.

##### Retry Statistics: `GET /worker-status/retry-stats`
Returns statistics about failed SMS messages and retry attempts.

### 9. Queue Management

The system now includes additional queues for better message management:

- **sms_failed_queue**: For persistent failed messages
- **sms_pending_queue**: For tracking pending messages
- **sms_retry_queue**: For retry attempts with delays
- **dlx_queue**: Dead letter queue for final failures

### 10. 100% Success Rate with Retry

The system implements a robust retry mechanism to achieve high success rates:

- **Exponential Backoff**: Retry delays increase exponentially
- **Configurable Retries**: Maximum retry attempts can be configured
- **Error Classification**: Different error types have different retry strategies
- **Persistent Storage**: Failed messages survive server restarts

### 11. Performance Metrics

The system tracks comprehensive performance metrics:

- **Average Processing Time**: Per second calculations
- **Throughput per Worker**: Individual worker performance
- **Success Rates**: Overall and per-worker success rates
- **Queue Depths**: Real-time queue monitoring
- **Retry Statistics**: Detailed retry attempt tracking

## API Endpoints Summary

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/worker-status` | GET | Comprehensive worker status and metrics |
| `/worker-status/health` | GET | Basic worker health check |
| `/worker-status/metrics` | GET | Detailed performance metrics |
| `/worker-status/failed-sms` | GET | List all failed SMS messages |
| `/worker-status/pending-sms` | GET | List all pending SMS messages |
| `/worker-status/retry-stats` | GET | Get retry statistics |
| `/worker-status/retry-all` | GET | Retry all failed SMS messages |

## Configuration Examples

### Basic Configuration
```bash
# Worker settings
WORKER_SMS_THROUGHPUT_PER_SECOND=5000
WORKER_MAX_CONCURRENT_MESSAGES=100
SMS_WORKER_COUNT=20
SMS_MAX_RETRIES=3

# Provider settings
TELE_ENDPOINT=https://api.tele.com/send
API_AUTH_TOKEN=your_api_token_here

# Persistence settings
ENABLE_FAILED_SMS_PERSISTENCE=true
FAILED_SMS_STORAGE_PATH=./data/failed_sms
PENDING_SMS_STORAGE_PATH=./data/pending_sms
```

### High-Performance Configuration
```bash
# High throughput settings
WORKER_SMS_THROUGHPUT_PER_SECOND=10000
WORKER_MAX_CONCURRENT_MESSAGES=200
SMS_WORKER_COUNT=40
SMS_MAX_RETRIES=5

# Optimized retry settings
SMS_RETRY_DELAY_MS=500
SMS_BACKOFF_MULTIPLIER=1.5
```

## Monitoring Dashboard

The system provides a comprehensive monitoring dashboard through the API endpoints. You can build a custom dashboard or use tools like Grafana to visualize:

- Real-time worker health
- SMS success rates
- Processing times
- Queue depths
- Failed message counts
- Retry statistics

## Troubleshooting

### Common Issues

1. **Failed SMS Not Persisting**
   - Check `ENABLE_FAILED_SMS_PERSISTENCE` is set to `true`
   - Verify storage paths are writable
   - Check disk space availability

2. **Workers Not Starting**
   - Verify `SMS_WORKER_COUNT` is set correctly
   - Check RabbitMQ connection
   - Review worker logs for errors

3. **Low Success Rate**
   - Check provider API endpoints
   - Verify API credentials
   - Review retry configuration
   - Monitor queue depths

### Log Locations

- Worker logs: `./logs/worker-{id}-{type}.log`
- Failed SMS: `./data/failed_sms/`
- Pending SMS: `./data/pending_sms/`

## Performance Optimization

### Recommended Settings for Different Loads

#### Light Load (< 1K SMS/second)
```bash
SMS_WORKER_COUNT=5
WORKER_SMS_THROUGHPUT_PER_SECOND=200
```

#### Medium Load (1K-10K SMS/second)
```bash
SMS_WORKER_COUNT=20
WORKER_SMS_THROUGHPUT_PER_SECOND=500
```

#### High Load (10K+ SMS/second)
```bash
SMS_WORKER_COUNT=40
WORKER_SMS_THROUGHPUT_PER_SECOND=1000
```

## Security Considerations

- API tokens are required for most endpoints
- Failed SMS persistence can contain sensitive data
- Ensure storage directories have appropriate permissions
- Monitor disk usage for persistence files

## Future Enhancements

- WebSocket real-time monitoring
- Advanced analytics dashboard
- Machine learning for retry optimization
- Multi-region deployment support
- Advanced queue routing strategies 