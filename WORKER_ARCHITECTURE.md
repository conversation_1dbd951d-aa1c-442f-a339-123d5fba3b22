# SMS Worker Architecture

This document explains the SMS worker architecture implemented in the GeezSMS system.

## Overview

The SMS worker architecture is designed to:

1. Start automatically with the NestJS application
2. Scale based on environment configuration
3. Support worker categorization for specialized processing
4. Provide health monitoring
5. Ensure fault tolerance

## Architecture Components

### WorkerManagerService

The `WorkerManagerService` is the core of the worker architecture. It:

- Creates and manages worker threads
- Assigns categories to workers
- Routes SMS messages to appropriate workers
- Monitors worker health
- Replaces failed workers automatically

### Worker Threads

Each worker thread:

- Processes SMS messages independently
- Sends heartbeats to the manager
- Reports processing status
- Handles errors gracefully

### Health Monitoring

The system includes health endpoints:

- `/api/v1/health` - Overall system health including workers
- `/api/v1/health/workers` - Detailed worker health information

## Configuration

Configure the worker architecture using environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `SMS_WORKER_COUNT` | Number of worker instances to start | CPU cores - 1 |
| `SMS_WORKER_CATEGORIES` | Comma-separated list of worker categories | single,bulk |

Example in `.env.development`:

```
SMS_WORKER_COUNT=4
SMS_WORKER_CATEGORIES=default,bulk,marketing,transactional,high-priority
```

## Worker Categories

Workers are assigned to categories in a round-robin fashion. Categories include:

- `default`: Handles general SMS messages
- `bulk`: Optimized for bulk SMS processing
- `marketing`: For marketing messages
- `transactional`: For transactional messages
- `high-priority`: For urgent messages

When sending an SMS, you can specify a category to route it to the appropriate worker:

```typescript
// Send to a specific worker category
await smsService.sendSingle({
  to: '+**********',
  message: 'Your verification code is 123456',
  workerCategory: 'high-priority'
});
```

## Worker Assignment

For consistent routing (e.g., sending all messages for a specific phone number to the same worker), use the `workerAssignmentValue`:

```typescript
// Use phone number as assignment value
const phoneNumber = '+**********';
const numericPart = phoneNumber.replace(/\D/g, '');

await smsService.sendSingle({
  to: phoneNumber,
  message: 'Your verification code is 123456',
  workerAssignmentValue: parseInt(numericPart, 10)
});
```

## Monitoring

### Health Checks

Access worker health information:

```
GET /api/v1/health/workers
```

Response example:

```json
{
  "status": "ok",
  "info": {
    "workers": {
      "status": "up",
      "worker_0": {
        "status": "up",
        "timeSinceLastHeartbeat": "2s",
        "category": "default"
      },
      "worker_1": {
        "status": "up",
        "timeSinceLastHeartbeat": "1s",
        "category": "bulk"
      }
    }
  }
}
```

### Logs

Worker activity is logged with worker ID and category:

```
[2025-04-19T13:03:00.247Z] Worker 1 (high-priority) successfully processed SMS to +********** with messageId: msg_ne26w8fs3on
```

## Fault Tolerance

The system provides fault tolerance through:

1. **Automatic worker replacement**: If a worker crashes, it's automatically replaced
2. **Retry mechanism**: Failed SMS attempts are retried with exponential backoff
3. **Fallback providers**: If the primary provider fails, the system tries fallback providers

## Performance Considerations

- Each worker runs in its own thread, utilizing multiple CPU cores
- The number of workers should be tuned based on your hardware
- For high-volume systems, consider increasing the worker count
- Monitor processing times to identify bottlenecks
