const fs = require('fs');
const path = require('path');

/**
 * Worker Log Management Tool
 */
class WorkerLogManager {
  constructor(logsDir = './logs') {
    this.logsDir = logsDir;
  }

  /**
   * Get all worker log files
   */
  getWorkerLogFiles() {
    if (!fs.existsSync(this.logsDir)) {
      return [];
    }

    const files = fs.readdirSync(this.logsDir);
    return files.filter(file => file.startsWith('worker-') && file.endsWith('.log'));
  }

  /**
   * Get worker statistics
   */
  getWorkerStats() {
    const files = this.getWorkerLogFiles();
    const stats = {};

    files.forEach(file => {
      const match = file.match(/worker-(\d+)-(\w+)/);
      if (match) {
        const [, workerId, logType] = match;
        const filePath = path.join(this.logsDir, file);
        const fileStats = fs.statSync(filePath);
        
        if (!stats[workerId]) {
          stats[workerId] = {};
        }
        
        stats[workerId][logType] = {
          filename: file,
          size: fileStats.size,
          sizeMB: (fileStats.size / 1024 / 1024).toFixed(2),
          lines: this.countLines(filePath),
          lastModified: fileStats.mtime
        };
      }
    });

    return stats;
  }

  /**
   * Count lines in a file
   */
  countLines(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      return content.split('\n').filter(line => line.trim()).length;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Display worker log statistics
   */
  displayStats() {
    console.log('📊 Worker Log Statistics');
    console.log('========================');
    
    const stats = this.getWorkerStats();
    const workerIds = Object.keys(stats).sort((a, b) => parseInt(a) - parseInt(b));
    
    if (workerIds.length === 0) {
      console.log('   No worker log files found');
      return;
    }

    workerIds.forEach(workerId => {
      const workerStats = stats[workerId];
      console.log(`\n👥 Worker ${workerId}:`);
      
      if (workerStats.error) {
        console.log(`   ❌ Errors: ${workerStats.error.lines} lines (${workerStats.error.sizeMB} MB)`);
      }
      if (workerStats.success) {
        console.log(`   ✅ Success: ${workerStats.success.lines} lines (${workerStats.success.sizeMB} MB)`);
      }
      if (workerStats.combined) {
        console.log(`   📋 Combined: ${workerStats.combined.lines} lines (${workerStats.combined.sizeMB} MB)`);
      }
    });

    // Summary
    const totalWorkers = workerIds.length;
    const totalErrorLines = workerIds.reduce((sum, id) => sum + (stats[id].error?.lines || 0), 0);
    const totalSuccessLines = workerIds.reduce((sum, id) => sum + (stats[id].success?.lines || 0), 0);
    const totalSize = workerIds.reduce((sum, id) => {
      return sum + (stats[id].error?.size || 0) + (stats[id].success?.size || 0) + (stats[id].combined?.size || 0);
    }, 0);

    console.log('\n📈 Summary:');
    console.log(`   Total Workers: ${totalWorkers}`);
    console.log(`   Total Error Logs: ${totalErrorLines} lines`);
    console.log(`   Total Success Logs: ${totalSuccessLines} lines`);
    console.log(`   Total Size: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
  }

  /**
   * Show recent log entries for a specific worker
   */
  showRecentLogs(workerId, logType = 'combined', lines = 10) {
    const files = this.getWorkerLogFiles();
    const targetFile = files.find(file => 
      file.includes(`worker-${workerId}-${logType}`) || 
      (logType === 'combined' && file.includes(`worker-${workerId}`))
    );

    if (!targetFile) {
      console.log(`❌ No ${logType} log file found for worker ${workerId}`);
      return;
    }

    const filePath = path.join(this.logsDir, targetFile);
    console.log(`\n📋 Recent ${logType} logs for Worker ${workerId}:`);
    console.log(`File: ${targetFile}`);
    console.log('=' * 60);

    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const logLines = content.split('\n').filter(line => line.trim());
      const recentLines = logLines.slice(-lines);

      recentLines.forEach(line => {
        try {
          const logEntry = JSON.parse(line);
          console.log(`${logEntry.timestamp} [${logEntry.level}] ${logEntry.message}`);
        } catch (e) {
          console.log(line);
        }
      });
    } catch (error) {
      console.log(`❌ Error reading log file: ${error.message}`);
    }
  }

  /**
   * Clean old log files
   */
  cleanOldLogs(daysOld = 7) {
    console.log(`🧹 Cleaning log files older than ${daysOld} days...`);
    
    const files = this.getWorkerLogFiles();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);
    
    let cleanedCount = 0;
    let totalSizeCleaned = 0;

    files.forEach(file => {
      const filePath = path.join(this.logsDir, file);
      const fileStats = fs.statSync(filePath);
      
      if (fileStats.mtime < cutoffDate) {
        try {
          fs.unlinkSync(filePath);
          cleanedCount++;
          totalSizeCleaned += fileStats.size;
          console.log(`   ✅ Deleted: ${file} (${(fileStats.size / 1024 / 1024).toFixed(2)} MB)`);
        } catch (error) {
          console.log(`   ❌ Failed to delete ${file}: ${error.message}`);
        }
      }
    });

    console.log(`\n📊 Cleanup Summary:`);
    console.log(`   Files deleted: ${cleanedCount}`);
    console.log(`   Space freed: ${(totalSizeCleaned / 1024 / 1024).toFixed(2)} MB`);
  }

  /**
   * Monitor logs in real-time
   */
  monitorLogs(workerId = null, logType = 'combined') {
    console.log(`👀 Monitoring ${logType} logs${workerId ? ` for Worker ${workerId}` : ' for all workers'}...`);
    console.log('Press Ctrl+C to stop monitoring');
    console.log('=' * 60);

    const files = this.getWorkerLogFiles();
    const targetFiles = workerId 
      ? files.filter(file => file.includes(`worker-${workerId}-${logType}`))
      : files.filter(file => file.includes(`-${logType}`));

    if (targetFiles.length === 0) {
      console.log(`❌ No ${logType} log files found${workerId ? ` for worker ${workerId}` : ''}`);
      return;
    }

    // Simple file watching (for demonstration)
    targetFiles.forEach(file => {
      const filePath = path.join(this.logsDir, file);
      console.log(`   Watching: ${file}`);
      
      // Note: This is a simplified version. In production, you'd want to use proper file watching
      // with libraries like chokidar for better performance
    });
  }
}

/**
 * Main function
 */
function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'stats';
  const manager = new WorkerLogManager();

  switch (command) {
    case 'stats':
      manager.displayStats();
      break;
      
    case 'recent':
      const workerId = args[1] || '0';
      const logType = args[2] || 'combined';
      const lines = parseInt(args[3]) || 10;
      manager.showRecentLogs(workerId, logType, lines);
      break;
      
    case 'clean':
      const days = parseInt(args[1]) || 7;
      manager.cleanOldLogs(days);
      break;
      
    case 'monitor':
      const monitorWorkerId = args[1] || null;
      const monitorLogType = args[2] || 'combined';
      manager.monitorLogs(monitorWorkerId, monitorLogType);
      break;
      
    default:
      console.log('📖 Worker Log Management Tool');
      console.log('============================');
      console.log('Usage:');
      console.log('   node manage-worker-logs.js stats                    - Show log statistics');
      console.log('   node manage-worker-logs.js recent [workerId] [type] [lines] - Show recent logs');
      console.log('   node manage-worker-logs.js clean [days]             - Clean old log files');
      console.log('   node manage-worker-logs.js monitor [workerId] [type] - Monitor logs in real-time');
      console.log('\nExamples:');
      console.log('   node manage-worker-logs.js');
      console.log('   node manage-worker-logs.js recent 0 error 20');
      console.log('   node manage-worker-logs.js clean 3');
      console.log('   node manage-worker-logs.js monitor 1 success');
      break;
  }
}

// Run the tool
main(); 