import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../app.module';
import { SmsServiceProvider } from '../sms/dto/sms-request.dto';

/**
 * Simple test to verify that the SMS API is working correctly
 */
describe('SMS API', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('should return 202 for a valid single SMS request to TELE', async () => {
    const response = await request(app.getHttpServer())
      .post('/sms/send/single')
      .send({
        to: '251953960596',
        message: 'Test message for TELE provider',
        sms_log_id: 1,
        sms_credential: {
          usr: 'testuser',
          pwd: 'testpass',
          from: 'SENDER',
          token: 'testtoken',
          sms_service_provider: SmsServiceProvider.TELE
        }
      });

    expect(response.status).toBe(202);
    expect(response.body).toHaveProperty('statusCode', 202);
    expect(response.body).toHaveProperty('message', 'SMS has been queued for delivery');
  });

  it('should return 202 for a valid single SMS request to SAFARICOM', async () => {
    const response = await request(app.getHttpServer())
      .post('/sms/send/single')
      .send({
        to: '251711220033',
        message: 'Test message for SAFARICOM provider',
        sms_log_id: 1,
        sms_credential: {
          usr: 'testuser',
          pwd: 'testpass',
          from: 'SENDER',
          token: 'testtoken',
          sms_service_provider: SmsServiceProvider.SAFARICOM
        }
      });

    expect(response.status).toBe(202);
    expect(response.body).toHaveProperty('statusCode', 202);
    expect(response.body).toHaveProperty('message', 'SMS has been queued for delivery');
  });
});
