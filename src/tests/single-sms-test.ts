import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, Logger } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../app.module';
import { SmsService } from '../sms/sms.service';
import { SmsProviderService } from '../sms/services/sms-provider.service';
import { SmsServiceProvider } from '../sms/dto/sms-request.dto';

/**
 * Test script to verify single SMS functionality
 * 
 * This script sends a single SMS message and logs the entire process
 */
async function main() {
  // Create a custom logger
  const logger = new Logger('SingleSmsTest');
  logger.log('Starting single SMS test...');

  // Create a test module with all the necessary components
  logger.log('Creating test module...');
  const moduleFixture: TestingModule = await Test.createTestingModule({
    imports: [AppModule],
  }).compile();

  // Create the application
  logger.log('Creating application...');
  const app = moduleFixture.createNestApplication();
  await app.init();

  try {
    // Get the services
    logger.log('Getting services...');
    const smsService = moduleFixture.get<SmsService>(SmsService);
    const smsProvider = moduleFixture.get<SmsProviderService>(SmsProviderService);

    // Create spies to track the service calls
    logger.log('Setting up spies...');
    const sendSingleSpy = jest.spyOn(smsService, 'sendSingle');
    const sendSingleSmsSpy = jest.spyOn(smsProvider, 'sendSingleSms');

    // Create a test message for TELE
    logger.log('Creating test message for TELE...');
    const telePhoneNumber = '251953960596'; // TELE phone number
    const teleMessage = {
      to: telePhoneNumber,
      message: 'Test message for TELE provider',
      sms_log_id: 1,
      sms_credential: {
        usr: 'testuser',
        pwd: 'testpass',
        from: 'SENDER',
        token: 'testtoken',
        sms_service_provider: SmsServiceProvider.TELE
      }
    };

    // Send the TELE message
    logger.log(`Sending SMS to TELE number: ${telePhoneNumber}...`);
    const teleResponse = await request(app.getHttpServer())
      .post('/sms/send/single')
      .send(teleMessage);
    
    // Log the response
    logger.log(`TELE response status: ${teleResponse.status}`);
    logger.log(`TELE response body: ${JSON.stringify(teleResponse.body)}`);

    // Verify the service was called
    logger.log(`smsService.sendSingle called: ${sendSingleSpy.mock.calls.length > 0}`);
    if (sendSingleSpy.mock.calls.length > 0) {
      logger.log(`sendSingle args: ${JSON.stringify(sendSingleSpy.mock.calls[0][0])}`);
    }

    // Wait for the message to be processed
    logger.log('Waiting for message to be processed...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Verify the provider service was called
    logger.log(`smsProvider.sendSingleSms called: ${sendSingleSmsSpy.mock.calls.length > 0}`);
    if (sendSingleSmsSpy.mock.calls.length > 0) {
      logger.log(`sendSingleSms args: ${JSON.stringify(sendSingleSmsSpy.mock.calls[0][0])}`);
    }

    // Create a test message for SAFARICOM
    logger.log('Creating test message for SAFARICOM...');
    const safaricomPhoneNumber = '251711220033'; // SAFARICOM phone number
    const safaricomMessage = {
      to: safaricomPhoneNumber,
      message: 'Test message for SAFARICOM provider',
      sms_log_id: 1,
      sms_credential: {
        usr: 'testuser',
        pwd: 'testpass',
        from: 'SENDER',
        token: 'testtoken',
        sms_service_provider: SmsServiceProvider.SAFARICOM
      }
    };

    // Reset the spies
    sendSingleSpy.mockClear();
    sendSingleSmsSpy.mockClear();

    // Send the SAFARICOM message
    logger.log(`Sending SMS to SAFARICOM number: ${safaricomPhoneNumber}...`);
    const safaricomResponse = await request(app.getHttpServer())
      .post('/sms/send/single')
      .send(safaricomMessage);
    
    // Log the response
    logger.log(`SAFARICOM response status: ${safaricomResponse.status}`);
    logger.log(`SAFARICOM response body: ${JSON.stringify(safaricomResponse.body)}`);

    // Verify the service was called
    logger.log(`smsService.sendSingle called: ${sendSingleSpy.mock.calls.length > 0}`);
    if (sendSingleSpy.mock.calls.length > 0) {
      logger.log(`sendSingle args: ${JSON.stringify(sendSingleSpy.mock.calls[0][0])}`);
    }

    // Wait for the message to be processed
    logger.log('Waiting for message to be processed...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Verify the provider service was called
    logger.log(`smsProvider.sendSingleSms called: ${sendSingleSmsSpy.mock.calls.length > 0}`);
    if (sendSingleSmsSpy.mock.calls.length > 0) {
      logger.log(`sendSingleSms args: ${JSON.stringify(sendSingleSmsSpy.mock.calls[0][0])}`);
    }

    logger.log('Test completed successfully!');
  } catch (error) {
    logger.error(`Test failed: ${error.message}`);
    logger.error(error.stack);
  } finally {
    // Clean up
    logger.log('Cleaning up...');
    await app.close();
  }
}

// Run the test
main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
