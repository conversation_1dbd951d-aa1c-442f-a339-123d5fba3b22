import { Controller, Get, HttpStatus, Res } from '@nestjs/common';
import { Response } from 'express';
import { QueueManagerService } from '../services/queue-manager.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { MetricsService } from '../../shared/services/metrics.service';

@ApiTags('queue')
@Controller('queue')
export class QueueStatusController {
  constructor(
    private readonly queueManager: QueueManagerService,
    private readonly metricsService: MetricsService,
  ) {}

  @Get('status')
  @ApiOperation({ summary: 'Get queue status' })
  @ApiResponse({
    status: 200,
    description: 'Returns detailed queue status information',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 200 },
        status: { type: 'string', example: 'up' },
        queues: {
          type: 'object',
          additionalProperties: {
            type: 'object',
            properties: {
              status: { type: 'string', example: 'up' },
              messageCount: { type: 'number', example: 42 },
              consumerCount: { type: 'number', example: 2 },
            },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 503, description: 'Service unavailable - queues are not healthy' })
  async getQueueStatus(@Res() response: Response): Promise<void> {
    try {
      const queueStatus = await this.queueManager.checkHealth();

      // Determine HTTP status code based on queue status
      const httpStatus =
        queueStatus.status === 'up' ? HttpStatus.OK : HttpStatus.SERVICE_UNAVAILABLE;

      // Return response with status code and queue status
      response.status(httpStatus).json({
        statusCode: httpStatus,
        ...queueStatus,
      });
    } catch (error) {
      // If there's an error getting queue status, return an error response
      const errorResult = {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        status: 'error',
        message: error.message || 'Unknown error',
        timestamp: new Date().toISOString(),
      };

      response.status(HttpStatus.INTERNAL_SERVER_ERROR).json(errorResult);
    }
  }

  @Get('metrics')
  @ApiOperation({ summary: 'Get queue metrics' })
  @ApiResponse({
    status: 200,
    description: 'Returns queue performance metrics',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 200 },
        messagesPublished: { type: 'number', example: 1250 },
        messagesConsumed: { type: 'number', example: 1200 },
        messagesInQueue: { type: 'object', example: { sms_single_queue: 50, sms_bulk_queue: 10 } },
        publishRate: { type: 'number', example: 42.5 },
        consumeRate: { type: 'number', example: 40.2 },
        totalMessages: { type: 'number', example: 2000 },
        successfulMessages: { type: 'number', example: 1800 },
        failedMessages: { type: 'number', example: 200 },
        retryAttempts: { type: 'number', example: 20 },
        averageProcessingTime: { type: 'number', example: 120.5 },
        queueDepths: { type: 'object', example: { sms_single_queue: 50, sms_bulk_queue: 10 } },
        timestamp: { type: 'string', example: '2023-04-30T14:53:17.000Z' },
      },
    },
  })
  async getQueueMetrics(@Res() response: Response): Promise<void> {
    try {
      // Get queue depths
      const queueStatus = await this.queueManager.checkHealth();
      const queueDepths = Object.entries(queueStatus.queues || {}).reduce<Record<string, number>>(
        (acc, [queueName, queueInfo]) => {
          acc[queueName] = (queueInfo as any).messageCount || 0;
          return acc;
        },
        {},
      );

      // Get real-time processing metrics
      const metrics = this.metricsService.getMetrics();

      response.status(HttpStatus.OK).json({
        statusCode: HttpStatus.OK,
        messagesPublished: 0, // Placeholder for future implementation
        messagesConsumed: 0, // Placeholder for future implementation
        messagesInQueue: queueDepths,
        publishRate: 0, // Placeholder for future implementation
        consumeRate: 0, // Placeholder for future implementation
        totalMessages: metrics.totalMessages,
        successfulMessages: metrics.successfulMessages,
        failedMessages: metrics.failedMessages,
        retryAttempts: metrics.retryAttempts,
        averageProcessingTime: metrics.averageProcessingTime,
        queueDepths: metrics.queueDepths,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      // If there's an error getting queue metrics, return an error response
      const errorResult = {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        status: 'error',
        message: error.message || 'Unknown error',
        timestamp: new Date().toISOString(),
      };

      response.status(HttpStatus.INTERNAL_SERVER_ERROR).json(errorResult);
    }
  }
}
