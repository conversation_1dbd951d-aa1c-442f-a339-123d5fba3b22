/**
 * Queue configuration for the SMS service (Phase 3)
 *
 * This configuration defines the RabbitMQ exchanges, queues, and bindings
 * used by the SMS service. It includes:
 *
 * - A direct exchange for routing SMS messages
 * - A dead letter exchange for failed messages
 * - Separate queues for single and bulk SMS messages
 * - A retry queue for message retries with exponential backoff
 * - A dead-letter queue for failed messages
 * - A failed SMS queue for persistent failed messages
 * - A pending SMS queue for tracking pending messages
 * - Bindings between exchanges and queues
 *
 * In Phase 3, we've simplified the queue structure by removing worker categorization
 * and the priority queue, focusing on a more streamlined approach.
 */

// src/queue/queue.config.ts
export const queueConfig = {
  /**
   * Exchange definitions
   */
  exchanges: [
    // Main SMS exchange for routing messages
    {
      name: 'sms_exchange',
      type: 'direct',
      options: {
        durable: true,
        autoDelete: false
      }
    },
    // Dead letter exchange for failed messages
    {
      name: 'dlx',
      type: 'fanout',
      options: {
        durable: true,
        autoDelete: false
      }
    },
    // Delayed message exchange for retries
    {
      name: 'sms_retry_exchange',
      type: 'direct',
      options: {
        durable: true,
        autoDelete: false
      }
    },
    // Exchange for failed SMS messages
    {
      name: 'sms_failed_exchange',
      type: 'direct',
      options: {
        durable: true,
        autoDelete: false
      }
    },
    // Exchange for pending SMS messages
    {
      name: 'sms_pending_exchange',
      type: 'direct',
      options: {
        durable: true,
        autoDelete: false
      }
    }
  ],
  /**
   * Queue definitions
   */
  queues: [
    // Queue for single SMS messages
    {
      name: 'sms_single_queue',
      options: {
        durable: true,
        autoDelete: false,
        deadLetterExchange: 'dlx',
        messageTtl: ********, // 24 hours
        arguments: {
          'x-max-priority': 10
        }
      }
    },
    // Queue for bulk SMS messages
    {
      name: 'sms_bulk_queue',
      options: {
        durable: true,
        autoDelete: false,
        deadLetterExchange: 'dlx',
        messageTtl: ********, // 24 hours
        arguments: {
          'x-max-priority': 10
        }
      }
    },
    // Queue for bulk SMS messages
    {
      name: 'sms_dlr_queue',
      options: {
        durable: true,
        autoDelete: false,
        deadLetterExchange: 'dlx',
        messageTtl: ********, // 24 hours
        arguments: {
          'x-max-priority': 10
        }
      }
    },
    // Queue for SMS retry messages
    {
      name: 'sms_retry_queue',
      options: {
        durable: true,
        autoDelete: false,
        deadLetterExchange: 'sms_exchange',
        deadLetterRoutingKey: 'single',
        messageTtl: 60000, // 1 minute
      }
    },
    // Dead letter queue for failed messages
    {
      name: 'dlx_queue',
      options: {
        durable: true,
        autoDelete: false,
        maxLength: 10000, // Limit queue size
      }
    },
    // Queue for persistent failed SMS messages
    {
      name: 'sms_failed_queue',
      options: {
        durable: true,
        autoDelete: false,
        maxLength: 50000, // Limit queue size
        arguments: {
          'x-max-priority': 5
        }
      }
    },
    // Queue for pending SMS messages
    {
      name: 'sms_pending_queue',
      options: {
        durable: true,
        autoDelete: false,
        maxLength: 100000, // Limit queue size
        arguments: {
          'x-max-priority': 10
        }
      }
    }
  ],
  /**
   * Bindings between exchanges and queues
   */
  bindings: [
    // Route 'single' messages to single queue
    {
      exchange: 'sms_exchange',
      queue: 'sms_single_queue',
      pattern: 'single'
    },
    // Route 'bulk' messages to bulk queue
    {
      exchange: 'sms_exchange',
      queue: 'sms_bulk_queue',
      pattern: 'bulk'
    },
    // Route 'bulk' messages to bulk queue
    {
      exchange: 'sms_exchange',
      queue: 'sms_dlr_queue',
      pattern: 'dlr'
    },
    // Route 'bulk-dashboard' messages to bulk queue
    {
      exchange: 'sms_exchange',
      queue: 'sms_bulk_queue',
      pattern: 'bulk-dashboard'
    },
    // Route 'bulk-api' messages to bulk queue
    {
      exchange: 'sms_exchange',
      queue: 'sms_bulk_queue',
      pattern: 'bulk-api'
    },
    // Route retry messages to retry queue
    {
      exchange: 'sms_retry_exchange',
      queue: 'sms_retry_queue',
      pattern: 'retry'
    },
    // Route all dead-lettered messages to dlx_queue
    {
      exchange: 'dlx',
      queue: 'dlx_queue',
      pattern: '#' // # = match all routing keys
    },
    // Route failed messages to failed queue
    {
      exchange: 'sms_failed_exchange',
      queue: 'sms_failed_queue',
      pattern: 'failed'
    },
    // Route pending messages to pending queue
    {
      exchange: 'sms_pending_exchange',
      queue: 'sms_pending_queue',
      pattern: 'pending'
    }
  ]
};
