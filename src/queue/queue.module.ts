import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { SmsProducer } from './producers/sms.producer';
import { SmsConsumer } from './consumers/sms.consumer';
import { RabbitMQConnectionService } from './services/rabbitmq-connection.service';
import { QueueManagerService } from './services/queue-manager.service';
import { RabbitMQHealthIndicator } from './health/rabbitmq-health.indicator';
import { QueueStatusController } from './controllers/queue-status.controller';
import { FailedSmsRetryService } from '../shared/services/failed-sms-retry.service';
import { FailedSmsPersistenceService } from '../shared/services/failed-sms-persistence.service';
import { SharedModule } from '../shared/shared.module';

/**
 * Queue module for managing RabbitMQ connections and message queues
 *
 * This module provides services for connecting to RabbitMQ, managing queues,
 * and producing/consuming messages.
 */
@Module({
  imports: [
    ConfigModule,
    forwardRef(() => SharedModule),
  ],
  controllers: [
    QueueStatusController,
  ],
  providers: [
    RabbitMQConnectionService,
    QueueManagerService,
    SmsProducer,
    SmsConsumer,
    RabbitMQHealthIndicator,
    FailedSmsPersistenceService,
    FailedSmsRetryService,
  ],
  exports: [
    RabbitMQConnectionService,
    QueueManagerService,
    SmsProducer,
    SmsConsumer,
    RabbitMQHealthIndicator,
    FailedSmsPersistenceService,
    FailedSmsRetryService,
  ],
})
export class QueueModule {}
