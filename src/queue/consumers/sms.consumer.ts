import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { QueueManagerService } from '../services/queue-manager.service';
import { SmsProducer } from '../producers/sms.producer';
import { EventEmitter } from 'events';
import { ErrorHandlerService } from '../../shared/services/error-handler.service';
import { MetricsService } from '../../shared/services/metrics.service';
import {
  FailedSmsPersistenceService,
  FailedSmsMessage,
  PendingSmsMessage,
} from '../../shared/services/failed-sms-persistence.service';
import { DLRSmsDto, SingleSmsDto } from '../../sms/dto/sms-request.dto';
import { RabbitMQMessage } from '../interfaces';
import { ErrorContext } from '../../shared/interfaces';
import axios from 'axios';

/**
 * Interface for SMS message handler
 */
export interface SmsMessageHandler {
  /**
   * Handle a single SMS message
   * @param message - The single SMS message data to handle
   * @returns Promise that resolves when the message is handled
   */
  handleSingleSms(message: SingleSmsDto): Promise<void>;

  /**
   * Handle a bulk SMS message
   * @param message - The bulk SMS message data to handle
   * @returns Promise that resolves when the message is handled
   */
  handleBulkSms(message: any): Promise<void>;
}

/**
 * Service for consuming SMS messages from RabbitMQ
 *
 * This service provides methods for consuming single and bulk SMS messages
 * from RabbitMQ queues for processing.
 */
@Injectable()
export class SmsConsumer implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(SmsConsumer.name);
  private readonly events = new EventEmitter();
  private singleConsumerTag: string | null = null;
  private bulkConsumerTag: string | null = null;
  private dlrConsumerTag: string | null = null;
  private retryConsumerTag: string | null = null;
  private messageHandler: SmsMessageHandler | null = null;
  private readonly maxRetries: number;
  private metricsInterval: NodeJS.Timeout | null = null;

  /**
   * Creates a new SmsConsumer
   *
   * @param configService - NestJS ConfigService for accessing configuration
   * @param queueManager - Queue manager service for consuming messages
   * @param smsProducer - SMS producer service for scheduling retries
   * @param errorHandler - Error handler service for classifying and handling errors
   * @param metricsService - Metrics service for tracking message processing
   */
  constructor(
    private readonly configService: ConfigService,
    private readonly queueManager: QueueManagerService,
    private readonly smsProducer: SmsProducer,
    private readonly errorHandler: ErrorHandlerService,
    private readonly metricsService: MetricsService,
    private readonly failedSmsPersistence: FailedSmsPersistenceService,
  ) {
    this.maxRetries = this.configService.get<number>('sms.retries') || 3;
    this.logger.log(`[SmsConsumer] Max SMS retries set to: ${this.maxRetries}`);
  }

  /**
   * Initialize the consumer when the module is initialized
   */
  async onModuleInit(): Promise<void> {
    this.logger.log('SMS consumer initializing...');

    // Start metrics collection
    this.startMetricsCollection();

    // Check if the queue manager is initialized
    if (this.queueManager.isInitialized()) {
      this.logger.log('Queue manager is initialized, starting consumer...');
      await this.startConsuming();
    } else {
      this.logger.warn(
        'Queue manager is not initialized, consumer will start when handler is registered',
      );
    }

    this.logger.log('SMS consumer initialized');
  }

  /**
   * Clean up resources when the module is destroyed
   */
  async onModuleDestroy(): Promise<void> {
    // Stop consuming messages
    await this.stopConsuming();

    // Stop metrics collection
    this.stopMetricsCollection();

    this.logger.log('SMS consumer destroyed');
  }

  /**
   * Start collecting metrics
   */
  private startMetricsCollection(): void {
    // Log metrics every minute
    this.metricsInterval = setInterval(() => {
      this.metricsService.logMetrics();
    }, 60000);

    // Subscribe to message events
    this.on('message_processed', (data) => {
      this.metricsService.recordMessageSuccess(data.processingTime || 0);
    });

    this.on('message_failed', (data) => {
      this.metricsService.recordMessageFailure(
        data.errorType || 'UNKNOWN',
        data.retryCount > 0,
        data.retryCount >= this.maxRetries,
      );
    });

    this.logger.log('Metrics collection started');
  }

  /**
   * Stop collecting metrics
   */
  private stopMetricsCollection(): void {
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
      this.metricsInterval = null;
    }

    this.logger.log('Metrics collection stopped');
  }

  /**
   * Register a message handler
   *
   * @param handler - The message handler
   */
  async registerHandler(handler: SmsMessageHandler): Promise<void> {
    this.messageHandler = handler;
    this.logger.log('SMS message handler registered');

    // Start consuming if the queue manager is initialized
    if (this.queueManager.isInitialized()) {
      this.logger.log(
        'Queue manager is initialized, starting consumer from handler registration...',
      );
      await this.startConsuming();
    } else {
      this.logger.warn('Queue manager is not initialized, consumer will not start yet');

      // Set up a retry mechanism to check periodically if the queue manager is initialized
      const checkInterval = setInterval(async () => {
        if (this.queueManager.isInitialized()) {
          this.logger.log('Queue manager is now initialized, starting consumer...');
          await this.startConsuming();
          clearInterval(checkInterval);
        }
      }, 5000); // Check every 5 seconds
    }
  }

  /**
   * Start consuming messages from queues
   */
  async startConsuming(): Promise<void> {
    if (!this.messageHandler) {
      this.logger.warn('Cannot start consuming: No message handler registered');
      return;
    }

    this.logger.log('Starting to consume messages from queues...');

    try {
      this.logger.log('Setting up consumer for single SMS queue...');
      // Start consuming from single SMS queue
      this.singleConsumerTag = await this.queueManager.consume(
        'sms_single_queue',
        async (message) => {
          this.logger.debug(
            `Received message from single SMS queue: ${JSON.stringify(message.properties)}`,
          );
          await this.processSingleMessage(message);
        },
        { prefetch: 10 },
      );
      this.logger.log(
        `Successfully set up consumer for single SMS queue with tag: ${this.singleConsumerTag}`,
      );

      this.logger.log('Setting up consumer for bulk SMS queue...');
      // Start consuming from bulk SMS queue
      this.bulkConsumerTag = await this.queueManager.consume(
        'sms_bulk_queue',
        async (message) => {
          this.logger.debug(
            `Received message from bulk SMS queue: ${JSON.stringify(message.properties)}`,
          );
          await this.processBulkMessage(message);
        },
        { prefetch: 5 },
      );
      this.logger.log(
        `Successfully set up consumer for bulk SMS queue with tag: ${this.bulkConsumerTag}`,
      );

      this.logger.log('Setting up consumer for dlr SMS queue...');
      // Start consuming from dlr SMS queue
      this.dlrConsumerTag = await this.queueManager.consume(
        'sms_dlr_queue',
        async (message) => {
          this.logger.debug(
            `Received message from dlr SMS queue: ${JSON.stringify(message.properties)}`,
          );
          await this.processDlrMessage(message);
        },
        { prefetch: 5 },
      );
      this.logger.log(
        `Successfully set up consumer for dlr SMS queue with tag: ${this.dlrConsumerTag}`,
      );

      this.logger.log('Setting up consumer for retry queue...');
      // Start consuming from retry queue
      this.retryConsumerTag = await this.queueManager.consume(
        'sms_retry_queue',
        async (message) => {
          this.logger.debug(
            `Received message from retry queue: ${JSON.stringify(message.properties)}`,
          );
          await this.processRetryMessage(message);
        },
        { prefetch: 10 },
      );
      this.logger.log(
        `Successfully set up consumer for retry queue with tag: ${this.retryConsumerTag}`,
      );
    } catch (err) {
      this.logger.error(`Error starting consumers: ${err.message}`);
    }
  }

  /**
   * Stop consuming messages from queues
   */
  async stopConsuming(): Promise<void> {
    try {
      // Cancel single SMS consumer
      if (this.singleConsumerTag) {
        await this.queueManager.cancelConsumer(this.singleConsumerTag);
        this.singleConsumerTag = null;
      }

      // Cancel bulk SMS consumer
      if (this.bulkConsumerTag) {
        await this.queueManager.cancelConsumer(this.bulkConsumerTag);
        this.bulkConsumerTag = null;
      }

      // Cancel dlr SMS consumer
      if (this.dlrConsumerTag) {
        await this.queueManager.cancelConsumer(this.dlrConsumerTag);
        this.dlrConsumerTag = null;
      }

      // Cancel retry consumer
      if (this.retryConsumerTag) {
        await this.queueManager.cancelConsumer(this.retryConsumerTag);
        this.retryConsumerTag = null;
      }

      this.logger.log('Stopped consuming from SMS queues (Phase 3)');
    } catch (err) {
      this.logger.error(`Failed to stop consuming from SMS queues: ${err.message}`);
    }
  }

  /**
   * Process a single SMS message
   *
   * @param message - The RabbitMQ message containing SMS data
   */
  /**
   * Extract recipient information from message content
   *
   * @param content - The message content
   * @param messageType - The type of message ('single' or 'bulk')
   * @returns The recipient information
   */
  private extractRecipient(content: any, messageType: 'single' | 'bulk' | 'dlr'): string {
    if (messageType === 'single') {
      // For single SMS, check data.to, to, data.phone, or phone
      return (
        content.data?.to ||
        (content as any).to ||
        content.data?.phone ||
        (content as any).phone ||
        'unknown'
      );
    } else {
      // For bulk SMS, check data.receivers, receivers, data.group_id, or group_id
      return (
        content.data?.receivers ||
        (content as any).receivers ||
        content.data?.group_id ||
        (content as any).group_id ||
        'unknown'
      );
    }
  }

  /**
   * Extract provider information from message content
   *
   * @param content - The message content
   * @returns The provider information
   */
  private extractProvider(content: any): string {
    // Check data.sms_credential.from, sms_credential.from, or default to 'unknown'
    return content.data?.sms_credential?.from || (content as any).sms_credential?.from || 'unknown';
  }

  /**
   * Process a single SMS message
   *
   * @param message - The RabbitMQ message containing SMS data
   */
  private async processSingleMessage(message: RabbitMQMessage): Promise<void> {
    let content =
      typeof message.content === 'string' ? JSON.parse(message.content) : message.content;
    let properties =
      typeof message.properties === 'string' ? JSON.parse(message.properties) : message.properties;
    const requestId = content.data?.requestId || 'unknown';
    const startTime = Date.now();

    this.metricsService.recordMessageReceived('single');

    this.logger.log(`Processing single SMS message ${content.data?.uniqueParam} (${requestId})`);
    this.logger.debug(`Message content: ${JSON.stringify(content)}`);
    this.logger.debug(`Message properties: ${JSON.stringify(properties)}`);

    try {
      if (!this.messageHandler) {
        this.logger.error('No message handler registered for single SMS');
        throw new Error('No message handler registered');
      }

      this.logger.log(
        `Calling message handler for single SMS message ${content.data?.uniqueParam} (${requestId})`,
      );

      // Process the message with the parsed content
      await this.messageHandler.handleSingleSms(content.data as SingleSmsDto);

      // Calculate processing time
      const processingTime = Date.now() - startTime;

      this.logger.log(
        `Successfully processed single SMS message ${content.data?.uniqueParam} (${requestId}) in ${processingTime}ms`,
      );
      this.events.emit('message_processed', {
        type: 'single',
        messageId: properties.messageId,
        processingTime,
      });
    } catch (err) {
      // Create error context
      const retryCount = properties.headers?.retryCount ?? 0;
      const messageId = properties.messageId || content.data?.messageId || 'unknown';
      const errorContext: ErrorContext = {
        messageId,
        requestId,
        messageType: 'single',
        retryCount,
        provider: this.extractProvider(content),
        recipient: this.extractRecipient(content, 'single'),
        timestamp: new Date(),
        additionalInfo: {
          correlationId: properties.correlationId,
        },
      };

      // Classify the error
      const errorType = this.errorHandler.classifyError(err, errorContext);

      // Log the error with context
      this.errorHandler.logError(err, errorType, errorContext);

      // Check if we should retry
      if (this.errorHandler.shouldRetry(errorType, retryCount, this.maxRetries)) {
        // Calculate retry delay based on error type and retry count
        const delay = this.errorHandler.calculateRetryDelay(errorType, retryCount);

        // Add retry count and error type to the message
        const retryMessage = {
          ...content,
          retryCount: retryCount + 1,
          errorType,
          messageId,
          correlationId: properties.correlationId,
        };

        await this.smsProducer.scheduleRetry(retryMessage, delay);

        this.logger.log(
          `Scheduled retry #${retryCount + 1} for message ${messageId} (${requestId}) in ${delay}ms`,
        );
      } else {
        this.logger.error(
          `Will not retry message ${messageId} (${requestId}): ${errorType} error, retry count: ${retryCount}`,
        );

        // Save failed SMS to persistence
        const failedSms: FailedSmsMessage = {
          id: messageId || `failed_${Date.now()}`,
          to: this.extractRecipient(content, 'single'),
          message: content.data?.message || 'Unknown message',
          provider: this.extractProvider(content),
          error: err.message,
          retryCount,
          maxRetries: this.maxRetries,
          timestamp: new Date().toISOString(),
          requestId,
          messageType: 'single',
          smsCredentials: content.data?.sms_credential || content.sms_credential,
          smsLogId: (content.data as any)?.sms_log_id,
        };

        await this.failedSmsPersistence.saveFailedSms(failedSms);

        this.events.emit('message_failed', {
          type: 'single',
          messageId,
          error: err.message,
          errorType,
          retryCount,
        });
      }
    }
  }

  /**
   * Process a bulk SMS message
   *
   * @param message - The RabbitMQ message containing bulk SMS data
   */
  private async processBulkMessage(message: RabbitMQMessage): Promise<void> {
    let content = message.content;
    const properties = message.properties;
    const requestId = properties.headers?.requestId || 'unknown';
    const messageCount = properties.headers?.messageCount || 0;
    const bulkType = properties.headers?.bulkType || 'TRADITIONAL';
    const startTime = Date.now();

    this.metricsService.recordMessageReceived('bulk');

    this.logger.log(
      `Processing bulk SMS message ${properties.messageId} (${requestId}) type: ${bulkType}`,
    );
    this.logger.debug(`Message content: ${JSON.stringify(content)}`);
    this.logger.debug(`Message properties: ${JSON.stringify(properties)}`);

    try {
      // Parse the message content if it's a string
      if (typeof content === 'string') {
        try {
          this.logger.debug(`Parsing bulk message content as JSON string`);
          content = JSON.parse(content);
          this.logger.debug(`Successfully parsed bulk message content: ${JSON.stringify(content)}`);
        } catch (parseError) {
          this.logger.error(`Failed to parse bulk message content: ${parseError.message}`);
          throw new Error('Invalid bulk message format');
        }
      }

      if (!this.messageHandler) {
        throw new Error('No message handler registered');
      }

      // Process the message with the parsed content
      await this.messageHandler.handleBulkSms(content.data || content);

      // Calculate processing time
      const processingTime = Date.now() - startTime;

      this.logger.log(
        `Successfully processed bulk SMS message ${properties.messageId} (${requestId}) in ${processingTime}ms`,
      );
      const resolvedMessageId =
        properties.messageId ||
        (content &&
        typeof content === 'object' &&
        'data' in content &&
        content.data &&
        typeof content.data === 'object' &&
        'messageId' in content.data
          ? content.data.messageId
          : content && typeof content === 'object' && 'messageId' in content
            ? content.messageId
            : undefined);
      const messageId = typeof resolvedMessageId === 'string' ? resolvedMessageId : 'unknown';
      this.events.emit('message_processed', {
        type: 'bulk',
        messageId,
        processingTime,
        bulkType,
      });
    } catch (err) {
      this.logger.log(
        `Sending data for Scheduled retry ${JSON.stringify(properties)} for bulk message`,
      );
      this.logger.log(`Bulk Error of ${JSON.stringify(err)} for bulk message`);
      const retryCount = properties.headers?.retryCount ?? 0;
      // const resolvedErrorMessageId =
      //   properties.messageId ||
      //   (content &&
      //   typeof content === 'object' &&
      //   'data' in content &&
      //   content.data &&
      //   typeof content.data === 'object' &&
      //   'messageId' in content.data
      //     ? content.data.messageId
      //     : content && typeof content === 'object' && 'messageId' in content
      //       ? content.messageId
      //       : undefined);
      const errorMessageId = err.messageId ?? 'unknown';
      // const errorMessageId = typeof resolvedErrorMessageId === 'string' ? resolvedErrorMessageId : 'unknown';
      const errorContext: ErrorContext = {
        messageId: errorMessageId,
        requestId,
        messageType: 'bulk',
        retryCount,
        provider: this.extractProvider(content),
        recipient: this.extractRecipient(content, 'bulk'),
        timestamp: new Date(),
        additionalInfo: {
          correlationId: properties.correlationId,
          bulkType,
          messageCount,
        },
      };

      // Classify the error
      const errorType = this.errorHandler.classifyError(err, errorContext);

      // Log the error with context
      this.errorHandler.logError(err, errorType, errorContext);

      // Check if we should retry
      if (this.errorHandler.shouldRetry(errorType, retryCount, this.maxRetries)) {
        const delay = this.errorHandler.calculateRetryDelay(errorType, retryCount);
        const retryMessage = {
          ...content,
          retryCount: retryCount + 1,
          errorType,
          messageId: errorMessageId, // <-- Ensure messageId is set at the top level
          correlationId: properties.correlationId,
        };
        // If retryMessage has a data property, also set messageId inside data
        if (retryMessage.data && typeof retryMessage.data === 'object') {
          (retryMessage.data as any).messageId = errorMessageId;
        }
        await this.smsProducer.scheduleRetry(retryMessage, delay);
        this.logger.log(
          `Scheduled retry #${retryCount + 1} for bulk message ${errorMessageId} (${requestId}) in ${delay}ms`,
        );
      } else {
        this.logger.error(
          `Will not retry bulk message ${errorMessageId} (${requestId}): ${errorType} error, retry count: ${retryCount}`,
        );

        // Save failed bulk SMS to persistence
        const failedSms: FailedSmsMessage = {
          id: errorMessageId || `failed_bulk_${Date.now()}`,
          to: this.extractRecipient(content, 'bulk'),
          message: content.data?.message || 'Unknown bulk message',
          provider: this.extractProvider(content),
          error: err.message,
          retryCount,
          maxRetries: this.maxRetries,
          timestamp: new Date().toISOString(),
          requestId,
          messageType: 'bulk',
          smsCredentials: content.data?.sms_credential || content.sms_credential,
          smsLogId: (content.data as any)?.sms_log_id,
        };

        await this.failedSmsPersistence.saveFailedSms(failedSms);

        this.events.emit('message_failed', {
          type: 'bulk',
          messageId: errorMessageId,
          error: err.message,
          errorType,
          retryCount,
          bulkType,
        });
      }
    }
  }

  // Removed processPriorityMessage method in Phase 3

  /**
   * Process a retry message
   *
   * @param message - The RabbitMQ message to process
   */
  private async processRetryMessage(message: RabbitMQMessage): Promise<void> {
    // The retry queue is just a delay mechanism
    // Messages are automatically routed back to the appropriate queue
    // based on the dead letter exchange and routing key
    // const properties = message.properties;

    let content =
      typeof message.content === 'string' ? JSON.parse(message.content) : message.content;
    const properties = message.properties;
    const retryCount = properties.headers?.retryCount || 0;
    const errorType = properties.headers?.errorType || 'UNKNOWN';
    const requestId = properties.headers?.requestId || 'unknown';
    const messageCount = properties.headers?.messageCount || 0;
    const bulkType = properties.headers?.bulkType || 'TRADITIONAL';
    const startTime = Date.now();

    this.metricsService.recordMessageReceived('bulk');

    this.logger.log(
      `Processing RETRY SMS message ${properties.messageId} (${requestId}) type: ${bulkType}`,
    );
    this.logger.debug(`Message content: ${JSON.stringify(content)}`);
    this.logger.debug(`Message properties: ${JSON.stringify(properties)}`);

    this.logger.debug(`RETRY DATAProcessed retry message ${JSON.stringify(properties)})`);

    try {
      if (!this.messageHandler) {
        this.logger.error('No message handler registered for single SMS');
        throw new Error('No message handler registered');
      }

      if (content.data.workerCategory == 'single') {
        this.logger.log(
          `Calling message handler for single SMS message ${content.data?.uniqueParam} (${requestId})`,
        );

        // Process the message with the parsed content
        await this.messageHandler.handleSingleSms(content.data as SingleSmsDto);

        // Calculate processing time
        const processingTime = Date.now() - startTime;

        this.logger.log(
          `Successfully processed single SMS message ${content.data?.uniqueParam} (${requestId}) in ${processingTime}ms`,
        );
        this.events.emit('message_processed', {
          type: 'retry',
          messageId: properties.messageId,
          processingTime,
        });
      } else if (content.data.workerCategory == 'bulk') {
        // Process the message with the parsed content
        await this.messageHandler.handleBulkSms(content.data || content);

        // Calculate processing time
        const processingTime = Date.now() - startTime;

        this.logger.log(
          `Successfully processed bulk SMS message ${properties.messageId} (${requestId}) in ${processingTime}ms`,
        );
        const resolvedMessageId =
          properties.messageId ||
          (content &&
          typeof content === 'object' &&
          'data' in content &&
          content.data &&
          typeof content.data === 'object' &&
          'messageId' in content.data
            ? content.data.messageId
            : content && typeof content === 'object' && 'messageId' in content
              ? content.messageId
              : undefined);
        const messageId = typeof resolvedMessageId === 'string' ? resolvedMessageId : 'unknown';
        this.events.emit('message_processed', {
          type: 'retry',
          messageId,
          processingTime,
          bulkType,
        });
      } else if (content.workerCategory == 'dlr') {
        // Process the message with the parsed content
        let confirm = content.query;
        let msgID = content.originalMessageId;
        const backendApiHost =
          this.configService.get<string>('api.backendRestfulApi') || 'https://api.geezsms.com/api';
        await axios.post(`${backendApiHost}/a2p/callback`, {
          success: content.status == 'ESME_ROK',
          msg: `async ${msgID}`,
          jasmin_msg_id: msgID,
          sms_log_id: content.query.sms_body_id,
        });

        // Calculate processing time
        const processingTime = Date.now() - startTime;

        this.logger.log(
          `Successfully processed bulk SMS message ${properties.messageId} (${requestId}) in ${processingTime}ms`,
        );
        const resolvedMessageId =
          properties.messageId ||
          (content &&
          typeof content === 'object' &&
          'data' in content &&
          content.data &&
          typeof content.data === 'object' &&
          'messageId' in content.data
            ? content.data.messageId
            : content && typeof content === 'object' && 'messageId' in content
              ? content.messageId
              : undefined);
        const messageId = typeof resolvedMessageId === 'string' ? resolvedMessageId : 'unknown';
        this.events.emit('message_processed', {
          type: 'dlr',
          messageId,
          processingTime,
          bulkType,
        });
      }
    } catch (err) {
      // Create error context
      const retryCount = properties.headers?.retryCount ?? 0;
      const errorMessageId = err.messageId ?? 'unknown';
      const messageId = properties.messageId || content.data?.messageId || 'unknown';
      const errorContext: ErrorContext = {
        messageId,
        requestId,
        messageType: content.data.workerCategory,
        retryCount,
        provider: this.extractProvider(content),
        recipient: this.extractRecipient(content, content.data.workerCategory),
        timestamp: new Date(),
        additionalInfo: {
          correlationId: properties.correlationId,
        },
      };

      // Classify the error
      const errorType = this.errorHandler.classifyError(err, errorContext);

      // Log the error with context
      this.errorHandler.logError(err, errorType, errorContext);

      // Calculate retry delay based on error type and retry count
      const delay = this.errorHandler.calculateRetryDelay(errorType, retryCount);

      // Add retry count and error type to the message
      const retryMessage = {
        ...content,
        retryCount: retryCount + 1,
        errorType,
        messageId,
        correlationId: properties.correlationId,
      };

      await this.smsProducer.addFailed(retryMessage, delay);

      this.logger.log(
        `Scheduled retry #${retryCount + 1} for message ${messageId} (${requestId}) in ${delay}ms`,
      );
    }

    // Update metrics for retry
    this.metricsService.recordMessageFailure(errorType, true, false);

    this.logger.debug(
      `Processed retry message ${properties.messageId} (attempt ${retryCount}, error: ${errorType})`,
    );
  }

  /**
   * Process a retry message
   *
   * @param message - The RabbitMQ message to process
   */
  private async processDlrMessage(message: RabbitMQMessage): Promise<void> {
    let content: DLRSmsDto =
      typeof message.content === 'string' ? JSON.parse(message.content).data : message.content.data;
    const properties = message.properties;
    const retryCount = properties.headers?.retryCount || 0;
    const errorType = properties.headers?.errorType || 'UNKNOWN';
    const requestId = properties.headers?.requestId || 'unknown';
    const messageCount = properties.headers?.messageCount || 0;
    const bulkType = properties.headers?.bulkType || 'TRADITIONAL';
    const startTime = Date.now();

    this.metricsService.recordMessageReceived('bulk');

    this.logger.log(
      `Processing DLR SMS message ${properties.messageId} (${requestId}) type: ${bulkType}`,
    );
    this.logger.debug(`Message content: ${JSON.stringify(content)}`);
    this.logger.debug(`Message properties: ${JSON.stringify(properties)}`);

    try {
      if (!this.messageHandler) {
        this.logger.error('No message handler registered for single SMS');
        throw new Error('No message handler registered');
      }

      // Process the message with the parsed content
      let confirm = content.query;
      let msgID = content.originalMessageId;
      let backendApiHost =
        this.configService.get<string>('api.backendRestfulApi') || 'https://api.geezsms.com/api';
      backendApiHost = `${backendApiHost}/a2p/callback`;
      backendApiHost = 'https://webhook.site/c50ac77e-2f55-4c24-9343-a78d60a8cb06';
      await axios.post(backendApiHost, {
        success: content.status == 'ESME_ROK',
        msg: `async ${msgID}`,
        jasmin_msg_id: msgID,
        sms_log_id: content.query.sms_body_id,
      });

      // Calculate processing time
      const processingTime = Date.now() - startTime;

      this.logger.log(
        `Successfully processed bulk SMS message ${properties.messageId} (${requestId}) in ${processingTime}ms`,
      );
      const resolvedMessageId =
        properties.messageId ||
        (content &&
        typeof content === 'object' &&
        'data' in content &&
        content.data &&
        typeof content.data === 'object' &&
        'messageId' in content.data
          ? content.data.messageId
          : content && typeof content === 'object' && 'messageId' in content
            ? content.messageId
            : undefined);
      const messageId = typeof resolvedMessageId === 'string' ? resolvedMessageId : 'unknown';
      this.events.emit('message_processed', {
        type: 'dlr',
        messageId,
        processingTime,
        bulkType,
      });
    } catch (err) {
      // Create error context
      const retryCount = properties.headers?.retryCount ?? 0;
      const errorMessageId = err.messageId ?? 'unknown';
      const messageId = properties.messageId || content.originalMessageId || 'unknown';
      const errorContext: ErrorContext = {
        messageId,
        requestId,
        messageType: 'dlr',
        retryCount,
        provider: this.extractProvider(content),
        recipient: this.extractRecipient(content, 'dlr'),
        timestamp: new Date(),
        additionalInfo: {
          correlationId: properties.correlationId,
        },
      };

      // Classify the error
      const errorType = this.errorHandler.classifyError(err, errorContext);

      // Check if we should retry
      if (this.errorHandler.shouldRetry(errorType, retryCount, this.maxRetries)) {
        const delay = this.errorHandler.calculateRetryDelay(errorType, retryCount);
        const retryMessage = {
          ...content,
          retryCount: retryCount + 1,
          errorType,
          messageId: errorMessageId, // <-- Ensure messageId is set at the top level
          correlationId: properties.correlationId,
        };
        // If retryMessage has a data property, also set messageId inside data
        retryMessage.originalMessageId = errorMessageId;
        await this.smsProducer.scheduleRetry(retryMessage, delay);
        this.logger.log(
          `Scheduled retry #${retryCount + 1} for bulk message ${errorMessageId} (${requestId}) in ${delay}ms`,
        );
      } else {
        this.logger.error(
          `Will not retry bulk message ${errorMessageId} (${requestId}): ${errorType} error, retry count: ${retryCount}`,
        );

        // Save failed bulk SMS to persistence
        const failedSms: FailedSmsMessage = {
          id: errorMessageId || `failed_bulk_${Date.now()}`,
          to: this.extractRecipient(content, 'dlr'),
          message: content.originalMessageId || 'Unknown bulk message',
          provider: this.extractProvider(content),
          error: err.message,
          retryCount,
          maxRetries: this.maxRetries,
          timestamp: new Date().toISOString(),
          requestId,
          messageType: 'bulk',
          smsCredentials: content.query.sms_credential,
          smsLogId: content.query.sms_body_id,
        };

        await this.failedSmsPersistence.saveFailedSms(failedSms);

        this.events.emit('message_failed', {
          type: 'dlr',
          messageId: errorMessageId,
          error: err.message,
          errorType,
          retryCount,
          bulkType,
        });
      }
    }
  }

  /**
   * Subscribe to consumer events
   *
   * @param event - The event name ('message_processed', 'message_failed')
   * @param listener - The event listener function
   */
  on(event: string, listener: (...args: any[]) => void): void {
    this.events.on(event, listener);
  }

  /**
   * Setup the consumer (backward compatibility method)
   */
  async setup(): Promise<void> {
    this.logger.log('Setting up SMS consumer');
    await this.startConsuming();
  }

  // Add a new method to handle worker results and nack on failure
  private handleWorkerResult(result: any, msg: any, channel: any) {
    if (result && result.success === false) {
      this.logger.error('Worker reported failure, nacking message to dead-letter queue.');
      channel.nack(msg, false, false); // Do not requeue, send to DLQ
    } else {
      channel.ack(msg);
    }
  }
}
