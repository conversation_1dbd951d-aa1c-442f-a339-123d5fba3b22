import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { HealthIndicator, HealthIndicatorResult, HealthCheckError } from '@nestjs/terminus';
import { RabbitMQConnectionService } from '../services/rabbitmq-connection.service';

@Injectable()
export class RabbitMQHealthIndicator extends HealthIndicator {
  constructor(
    @Inject(forwardRef(() => RabbitMQConnectionService))
    private readonly rabbitMQConnection: RabbitMQConnectionService
  ) {
    super();
  }

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      const isConnected = this.rabbitMQConnection.isConnected();

      return this.getStatus(key, isConnected, {
        connection: isConnected ? 'connected' : 'disconnected'
      });
    } catch (error) {
      // If there's an error checking the connection, return a status indicating the issue
      return this.getStatus(key, false, {
        connection: 'error',
        message: error.message || 'Unknown error'
      });
    }
  }
}
