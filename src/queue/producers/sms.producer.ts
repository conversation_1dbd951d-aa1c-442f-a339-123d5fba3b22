import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { QueueManagerService } from '../services/queue-manager.service';
import { v4 as uuidv4 } from 'uuid';

/**
 * Interface for SMS message options
 */
export interface SmsMessageOptions {
  /** Priority of the message (0-10, higher is more important) */
  priority?: number;

  /** Time-to-live in milliseconds */
  expiration?: number;

  /** Message ID for tracking */
  messageId?: string;

  /** Correlation ID for request tracing */
  correlationId?: string;

  /** Content type of the message */
  contentType?: string;

  /** Headers for the message */
  headers?: Record<string, any>;
}

/**
 * Service for producing SMS messages to RabbitMQ
 *
 * This service provides methods for sending single and bulk SMS messages
 * to RabbitMQ queues for asynchronous processing.
 */
@Injectable()
export class SmsProducer {
  private readonly logger = new Logger(SmsProducer.name);
  private readonly exchange: string;

  /**
   * Creates a new SmsProducer
   *
   * @param configService - NestJS ConfigService for accessing configuration
   * @param queueManager - Queue manager service for publishing messages
   */
  constructor(
    private readonly configService: ConfigService,
    private readonly queueManager: QueueManagerService
  ) {
    this.exchange = 'sms_exchange';
  }

  /**
   * Send a single SMS message
   *
   * @param data - SMS message data
   * @param options - Message options
   * @returns Promise that resolves when the message is published
   */
  async sendSingle(data: any, options: SmsMessageOptions = {}): Promise<boolean> {
    const messageId = options.messageId || uuidv4();
    const correlationId = options.correlationId || uuidv4();

    // In Phase 3, we always use the 'single' routing key
    const routingKey = 'single';

    // Set message properties
    const messageOptions: Record<string, any> = {
      messageId,
      correlationId,
      timestamp: Date.now(),
      contentType: options.contentType || 'application/json',
      headers: {
        ...options.headers,
        requestId: data.requestId || correlationId,
      },
    };

    // Set message priority if specified
    if (options.priority !== undefined) {
      messageOptions.priority = options.priority;
    } else if (data.priority !== undefined) {
      messageOptions.priority = data.priority;
    }

    // Set message expiration if specified
    if (options.expiration !== undefined) {
      messageOptions.expiration = Math.floor(options.expiration).toString();
    }

    this.logger.log(`Publishing single SMS message to ${this.exchange}:${routingKey} with ID ${messageId}`);

    // Publish message to RabbitMQ
    const result = await this.queueManager.publish(
      this.exchange,
      routingKey,
      data,
      messageOptions
    );

    if (result) {
      this.logger.log(`Successfully published SMS message ${messageId}`);
    } else {
      this.logger.error(`Failed to publish SMS message ${messageId}`);
    }

    return result;
  }

  /**
   * Send multiple SMS messages in bulk
   *
   * @param data - Bulk SMS message data
   * @param options - Message options
   * @returns Promise that resolves when the message is published
   */
  async sendBulk(data: any, options: SmsMessageOptions = {}): Promise<boolean> {
    const messageId = data.messageId || options.messageId || uuidv4();
    const correlationId = options.correlationId || uuidv4();

    // Determine routing key based on bulk type
    let routingKey = 'bulk';
    if (data.bulk_type === 'DASHBOARD') {
      routingKey = 'bulk-dashboard';
    } else if (data.bulk_type === 'API') {
      routingKey = 'bulk-api';
    }

    // Set message properties
    const messageOptions: Record<string, any> = {
      messageId,
      correlationId,
      timestamp: Date.now(),
      contentType: options.contentType || 'application/json',
      headers: {
        ...options.headers,
        requestId: data.requestId || correlationId,
        bulkType: data.bulk_type || 'TRADITIONAL',
      },
    };

    // Add message count if this is a traditional bulk SMS with messages array
    if (data.messages && Array.isArray(data.messages)) {
      messageOptions.headers.messageCount = data.messages.length;
    }

    // Set message priority if specified
    if (options.priority !== undefined) {
      messageOptions.priority = options.priority;
    } else if (data.priority !== undefined) {
      messageOptions.priority = data.priority;
    }

    // Set message expiration if specified
    if (options.expiration !== undefined) {
      messageOptions.expiration = Math.floor(options.expiration).toString();
    }

    this.logger.log(`Publishing bulk SMS message to ${this.exchange}:${routingKey} with ID ${messageId}`);

    // Publish message to RabbitMQ
    const result = await this.queueManager.publish(
      this.exchange,
      routingKey,
      data,
      messageOptions
    );

    if (result) {
      this.logger.log(`Successfully published bulk SMS message ${messageId} (${routingKey})`);
    } else {
      this.logger.error(`Failed to publish bulk SMS message ${messageId}`);
    }

    return result;
  }

  /**
   * Schedule a retry for a failed SMS message
   *
   * @param data - Original SMS message data
   * @param delay - Delay in milliseconds before retry
   * @param options - Message options
   * @returns Promise that resolves when the retry message is published
   */
  async scheduleRetry(data: any, delay: number = 60000, options: SmsMessageOptions = {}): Promise<boolean> {
    const messageId = data.messageId || options.messageId || uuidv4();
    const correlationId = options.correlationId || data.correlationId || uuidv4();

    // Set message properties
    const messageOptions: Record<string, any> = {
      messageId,
      correlationId,
      timestamp: Date.now(),
      contentType: options.contentType || 'application/json',
      expiration: Math.floor(delay).toString(),
      headers: {
        ...options.headers,
        requestId: data.requestId || correlationId,
        retryCount: (data.retryCount || 0) + 1,
        originalMessageId: data.messageId,
      },
    };

    // If this is a bulk message with a bulk type, preserve it
    if (data.bulk_type) {
      messageOptions.headers.bulkType = data.bulk_type;
    }

    this.logger.log(`Scheduling retry for SMS ${JSON.stringify(data)}`);
    this.logger.log(`Scheduling retry for SMS message ${data.messageId} in ${delay}ms`);

    // Publish retry message to RabbitMQ
    const result = await this.queueManager.publish(
      'sms_retry_exchange',
      'retry',
      data,
      messageOptions
    );

    if (result) {
      this.logger.log(`Successfully scheduled retry for SMS message ${data.messageId}`);
    } else {
      this.logger.error(`Failed to schedule retry for SMS message ${data.messageId}`);
    }

    return result;
  }

  /**
   * Schedule a retry for a failed SMS message
   *
   * @param data - Original SMS message data
   * @param delay - Delay in milliseconds before retry
   * @param options - Message options
   * @returns Promise that resolves when the retry message is published
   */
  async addFailed(data: any, delay: number = 60000, options: SmsMessageOptions = {}): Promise<boolean> {
    const messageId = data.messageId || options.messageId || uuidv4();
    const correlationId = options.correlationId || data.correlationId || uuidv4();

    // Set message properties
    const messageOptions: Record<string, any> = {
      messageId,
      correlationId,
      timestamp: Date.now(),
      contentType: options.contentType || 'application/json',
      expiration: Math.floor(delay).toString(),
      headers: {
        ...options.headers,
        requestId: data.requestId || correlationId,
        retryCount: (data.retryCount || 0) + 1,
        originalMessageId: data.messageId,
      },
    };

    // If this is a bulk message with a bulk type, preserve it
    if (data.bulk_type) {
      messageOptions.headers.bulkType = data.bulk_type;
    }

    this.logger.log(`Scheduling retry for SMS ${JSON.stringify(data)}`);
    this.logger.log(`Scheduling retry for SMS message ${data.messageId} in ${delay}ms`);

    // Publish retry message to RabbitMQ
    const result = await this.queueManager.publish(
      'sms_failed_exchange',
      'failed',
      data,
      messageOptions
    );

    if (result) {
      this.logger.log(`Successfully add to failed queue for SMS message ${data.messageId}`);
    } else {
      this.logger.error(`Failed to add to failed for SMS message ${data.messageId}`);
    }

    return result;
  }

  async enqueueBulkPage(payload: any) {
    // Implementation needed
  }

  async publishBulkPage(payload: any) {
    // Publish to RabbitMQ bulk queue
    await this.queueManager.publishToQueue('sms_bulk_queue', payload);
  }
}
