import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { SmsProducer, SmsMessageOptions } from './sms.producer';
import { QueueManagerService } from '../services/queue-manager.service';
import { v4 as uuidv4 } from 'uuid';

// Mock uuid
jest.mock('uuid', () => ({
  v4: jest.fn().mockReturnValue('test-uuid'),
}));

describe('SmsProducer', () => {
  let producer: SmsProducer;
  let queueManager: QueueManagerService;
  let configService: ConfigService;

  beforeEach(async () => {
    // Create a testing module with our dependencies
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SmsProducer,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: QueueManagerService,
          useValue: {
            publish: jest.fn().mockResolvedValue(true),
          },
        },
      ],
    }).compile();

    // Get the service instances
    producer = module.get<SmsProducer>(SmsProducer);
    queueManager = module.get<QueueManagerService>(QueueManagerService);
    configService = module.get<ConfigService>(ConfigService);

    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(producer).toBeDefined();
  });

  describe('sendSingle', () => {
    it('should publish a single SMS message', async () => {
      // Arrange
      const data = {
        to: '+**********',
        message: 'Test message',
        sms_log_id: 1,
        sms_credential: {
          usr: 'testuser',
          pwd: 'testpass',
          from: 'SENDER',
          token: 'testtoken',
          sms_service_provider: 'TELE',
        },
      };

      // Act
      const result = await producer.sendSingle(data);

      // Assert
      expect(result).toBe(true);
      expect(queueManager.publish).toHaveBeenCalledWith(
        'sms_exchange',
        'single',
        data,
        expect.objectContaining({
          messageId: 'test-uuid',
          correlationId: 'test-uuid',
          timestamp: expect.any(Number),
          contentType: 'application/json',
          headers: expect.objectContaining({
            requestId: 'test-uuid',
          }),
        })
      );
    });

    it('should use provided message options', async () => {
      // Arrange
      const data = {
        to: '+**********',
        message: 'Test message',
        requestId: 'custom-request-id',
        workerCategory: 'custom-category',
        priority: 5,
        sms_log_id: 1,
        sms_credential: {
          usr: 'testuser',
          pwd: 'testpass',
          from: 'SENDER',
          token: 'testtoken',
          sms_service_provider: 'TELE',
        },
      };

      const options: SmsMessageOptions = {
        messageId: 'custom-message-id',
        correlationId: 'custom-correlation-id',
        priority: 8,
        expiration: 60000,
        contentType: 'text/plain',
        headers: {
          customHeader: 'custom-value',
        },
      };

      // Act
      const result = await producer.sendSingle(data, options);

      // Assert
      expect(result).toBe(true);
      expect(queueManager.publish).toHaveBeenCalledWith(
        'sms_exchange',
        'single',
        data,
        expect.objectContaining({
          messageId: 'custom-message-id',
          correlationId: 'custom-correlation-id',
          priority: 8,
          expiration: '60000',
          contentType: 'text/plain',
          headers: expect.objectContaining({
            requestId: 'custom-request-id',
            customHeader: 'custom-value',
          }),
        })
      );
    });

    it('should handle high-priority worker category', async () => {
      // Arrange
      const data = {
        to: '+**********',
        message: 'Test message',
        workerCategory: 'high-priority',
        sms_log_id: 1,
        sms_credential: {
          usr: 'testuser',
          pwd: 'testpass',
          from: 'SENDER',
          token: 'testtoken',
          sms_service_provider: 'TELE',
        },
      };

      // Act
      const result = await producer.sendSingle(data);

      // Assert
      expect(result).toBe(true);
      expect(queueManager.publish).toHaveBeenCalledWith(
        'sms_exchange',
        'single', // In Phase 3, we always use 'single' routing key
        data,
        expect.objectContaining({
          headers: expect.objectContaining({
            requestId: 'test-uuid',
          }),
        })
      );
    });

    it('should handle publish failures', async () => {
      // Arrange
      (queueManager.publish as jest.Mock).mockResolvedValueOnce(false);
      const data = {
        to: '+**********',
        message: 'Test message',
        sms_log_id: 1,
        sms_credential: {
          usr: 'testuser',
          pwd: 'testpass',
          from: 'SENDER',
          token: 'testtoken',
          sms_service_provider: 'TELE',
        },
      };

      // Act
      const result = await producer.sendSingle(data);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('sendBulk', () => {
    it('should publish a bulk SMS message', async () => {
      // Arrange
      const data = {
        messages: [
          { to: '+**********', message: 'Test message 1' },
          { to: '+**********', message: 'Test message 2' },
        ],
      };

      // Act
      const result = await producer.sendBulk(data);

      // Assert
      expect(result).toBe(true);
      expect(queueManager.publish).toHaveBeenCalledWith(
        'sms_exchange',
        'bulk',
        data,
        expect.objectContaining({
          messageId: 'test-uuid',
          correlationId: 'test-uuid',
          timestamp: expect.any(Number),
          contentType: 'application/json',
          headers: expect.objectContaining({
            requestId: 'test-uuid',
            bulkType: 'TRADITIONAL',
            messageCount: 2,
          }),
        })
      );
    });

    it('should use provided message options', async () => {
      // Arrange
      const data = {
        messages: [
          { to: '+**********', message: 'Test message 1' },
          { to: '+**********', message: 'Test message 2' },
        ],
        requestId: 'custom-request-id',
        workerCategory: 'custom-category',
        priority: 5,
      };

      const options: SmsMessageOptions = {
        messageId: 'custom-message-id',
        correlationId: 'custom-correlation-id',
        priority: 8,
        expiration: 60000,
        contentType: 'text/plain',
        headers: {
          customHeader: 'custom-value',
        },
      };

      // Act
      const result = await producer.sendBulk(data, options);

      // Assert
      expect(result).toBe(true);
      expect(queueManager.publish).toHaveBeenCalledWith(
        'sms_exchange',
        'bulk',
        data,
        expect.objectContaining({
          messageId: 'custom-message-id',
          correlationId: 'custom-correlation-id',
          priority: 8,
          expiration: '60000',
          contentType: 'text/plain',
          headers: expect.objectContaining({
            requestId: 'custom-request-id',
            customHeader: 'custom-value',
            bulkType: 'TRADITIONAL',
            messageCount: 2,
          }),
        })
      );
    });

    it('should handle DASHBOARD bulk type', async () => {
      // Arrange
      const data = {
        messages: [
          { to: '+**********', message: 'Test message 1' },
          { to: '+**********', message: 'Test message 2' },
        ],
        bulk_type: 'DASHBOARD',
      };

      // Act
      const result = await producer.sendBulk(data);

      // Assert
      expect(result).toBe(true);
      expect(queueManager.publish).toHaveBeenCalledWith(
        'sms_exchange',
        'bulk-dashboard',
        data,
        expect.objectContaining({
          headers: expect.objectContaining({
            bulkType: 'DASHBOARD',
            messageCount: 2,
          }),
        })
      );
    });

    it('should handle API bulk type', async () => {
      // Arrange
      const data = {
        messages: [
          { to: '+**********', message: 'Test message 1' },
          { to: '+**********', message: 'Test message 2' },
        ],
        bulk_type: 'API',
      };

      // Act
      const result = await producer.sendBulk(data);

      // Assert
      expect(result).toBe(true);
      expect(queueManager.publish).toHaveBeenCalledWith(
        'sms_exchange',
        'bulk-api',
        data,
        expect.objectContaining({
          headers: expect.objectContaining({
            bulkType: 'API',
            messageCount: 2,
          }),
        })
      );
    });
  });

  describe('scheduleRetry', () => {
    it('should schedule a retry for a failed message', async () => {
      // Arrange
      const data = {
        to: '+**********',
        message: 'Test message',
        messageId: 'original-message-id',
        retryCount: 1,
      };

      // Act
      const result = await producer.scheduleRetry(data, 30000);

      // Assert
      expect(result).toBe(true);
      expect(queueManager.publish).toHaveBeenCalledWith(
        'sms_retry_exchange',
        'retry',
        data,
        expect.objectContaining({
          messageId: 'test-uuid',
          correlationId: 'test-uuid',
          expiration: '30000',
          headers: expect.objectContaining({
            retryCount: 2,
            originalMessageId: 'original-message-id',
          }),
        })
      );
    });

    it('should use default delay if not specified', async () => {
      // Arrange
      const data = {
        to: '+**********',
        message: 'Test message',
        messageId: 'original-message-id',
      };

      // Act
      const result = await producer.scheduleRetry(data);

      // Assert
      expect(result).toBe(true);
      expect(queueManager.publish).toHaveBeenCalledWith(
        'sms_retry_exchange',
        'retry',
        data,
        expect.objectContaining({
          expiration: '60000', // Default delay
        })
      );
    });
  });
});
