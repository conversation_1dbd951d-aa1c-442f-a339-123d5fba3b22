import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RabbitMQConnectionService } from './rabbitmq-connection.service';
import { queueConfig } from '../queue.config';
import { Channel } from 'amqplib';

/**
 * Interface for queue declaration options
 */
export interface QueueOptions {
  /** Queue name */
  name: string;

  /** Queue options for assertQueue */
  options?: {
    /** Whether the queue should survive broker restarts */
    durable?: boolean;

    /** Whether the queue should be deleted when the last consumer unsubscribes */
    autoDelete?: boolean;

    /** Whether the queue should be used only by one connection */
    exclusive?: boolean;

    /** Arguments for the queue declaration */
    arguments?: any;

    /** Dead letter exchange for the queue */
    deadLetterExchange?: string;

    /** Dead letter routing key for the queue */
    deadLetterRoutingKey?: string;

    /** Message TTL in milliseconds */
    messageTtl?: number;

    /** Maximum queue length */
    maxLength?: number;

    /** Maximum queue length in bytes */
    maxLengthBytes?: number;
  };
}

/**
 * Interface for exchange declaration options
 */
export interface ExchangeOptions {
  /** Exchange name */
  name: string;

  /** Exchange type (direct, fanout, topic, headers) */
  type: string;

  /** Exchange options for assertExchange */
  options?: {
    /** Whether the exchange should survive broker restarts */
    durable?: boolean;

    /** Whether the exchange should be deleted when the last binding is removed */
    autoDelete?: boolean;

    /** Whether the exchange should be used only by one connection */
    internal?: boolean;

    /** Arguments for the exchange declaration */
    arguments?: any;
  };
}

/**
 * Interface for binding options
 */
export interface BindingOptions {
  /** Exchange name */
  exchange: string;

  /** Queue name */
  queue: string;

  /** Routing pattern */
  pattern: string;

  /** Arguments for the binding */
  args?: any;
}

/**
 * Service for managing RabbitMQ queues, exchanges, and bindings
 *
 * This service provides methods for declaring queues, exchanges, and bindings,
 * as well as publishing and consuming messages.
 */
@Injectable()
export class QueueManagerService implements OnModuleInit {
  private readonly logger = new Logger(QueueManagerService.name);
  private initialized = false;

  /**
   * Creates a new QueueManagerService
   *
   * @param configService - NestJS ConfigService for accessing configuration
   * @param connectionService - RabbitMQ connection service
   */
  constructor(
    private readonly configService: ConfigService,
    private readonly connectionService: RabbitMQConnectionService,
  ) {
    // Set up connection event handlers
    this.connectionService.on('connected', () => {
      this.setupInfrastructure();
    });
  }

  /**
   * Initialize the queue manager when the module is initialized
   */
  async onModuleInit(): Promise<void> {
    this.logger.log('Initializing queue manager...');
    await this.setupInfrastructure();
  }

  /**
   * Set up the RabbitMQ infrastructure (exchanges, queues, bindings)
   */
  private async setupInfrastructure(): Promise<void> {
    const channel = this.connectionService.getChannel();
    if (!channel) {
      this.logger.warn('Cannot set up infrastructure: No RabbitMQ channel available');
      return;
    }

    try {
      // Declare exchanges
      for (const exchange of queueConfig.exchanges) {
        await this.assertExchange(channel, exchange);
      }

      // Declare queues
      for (const queue of queueConfig.queues) {
        await this.assertQueue(channel, queue);
      }

      // Create bindings
      for (const binding of queueConfig.bindings) {
        await this.bindQueue(channel, binding);
      }

      this.initialized = true;
      this.logger.log('RabbitMQ infrastructure set up successfully');
    } catch (err) {
      this.logger.error(`Failed to set up RabbitMQ infrastructure: ${err.message}`);
      throw err;
    }
  }

  /**
   * Assert that an exchange exists, creating it if it doesn't
   *
   * @param channel - RabbitMQ channel
   * @param exchange - Exchange options
   */
  private async assertExchange(channel: Channel, exchange: ExchangeOptions): Promise<void> {
    try {
      await channel.assertExchange(
        exchange.name,
        exchange.type,
        exchange.options || { durable: true }
      );
      this.logger.log(`Declared exchange: ${exchange.name} (${exchange.type})`);
    } catch (err) {
      this.logger.error(`Failed to declare exchange ${exchange.name}: ${err.message}`);
      throw err;
    }
  }

  /**
   * Assert that a queue exists, creating it if it doesn't
   *
   * @param channel - RabbitMQ channel
   * @param queue - Queue options
   */
  private async assertQueue(channel: Channel, queue: QueueOptions): Promise<void> {
    try {
      // Prepare queue options
      const options: any = queue.options || { durable: true };

      // Handle dead letter exchange if specified
      if (options.deadLetterExchange) {
        if (!options.arguments) {
          options.arguments = {};
        }
        options.arguments['x-dead-letter-exchange'] = options.deadLetterExchange;
        delete options.deadLetterExchange;

        if (options.deadLetterRoutingKey) {
          options.arguments['x-dead-letter-routing-key'] = options.deadLetterRoutingKey;
          delete options.deadLetterRoutingKey;
        }
      }

      // Handle message TTL if specified
      if (options.messageTtl) {
        if (!options.arguments) {
          options.arguments = {};
        }
        options.arguments['x-message-ttl'] = options.messageTtl;
        delete options.messageTtl;
      }

      // Handle max length if specified
      if (options.maxLength) {
        if (!options.arguments) {
          options.arguments = {};
        }
        options.arguments['x-max-length'] = options.maxLength;
        delete options.maxLength;
      }

      // Handle max length bytes if specified
      if (options.maxLengthBytes) {
        if (!options.arguments) {
          options.arguments = {};
        }
        options.arguments['x-max-length-bytes'] = options.maxLengthBytes;
        delete options.maxLengthBytes;
      }

      await channel.assertQueue(queue.name, options);
      this.logger.log(`Declared queue: ${queue.name}`);
    } catch (err) {
      this.logger.error(`Failed to declare queue ${queue.name}: ${err.message}`);
      throw err;
    }
  }

  /**
   * Bind a queue to an exchange
   *
   * @param channel - RabbitMQ channel
   * @param binding - Binding options
   */
  private async bindQueue(channel: Channel, binding: BindingOptions): Promise<void> {
    try {
      await channel.bindQueue(
        binding.queue,
        binding.exchange,
        binding.pattern,
        binding.args
      );
      this.logger.log(`Bound queue ${binding.queue} to exchange ${binding.exchange} with pattern ${binding.pattern}`);
    } catch (err) {
      this.logger.error(`Failed to bind queue ${binding.queue} to exchange ${binding.exchange}: ${err.message}`);
      throw err;
    }
  }

  /**
   * Publish a message to an exchange
   *
   * @param exchange - Exchange name
   * @param routingKey - Routing key
   * @param content - Message content
   * @param options - Publishing options
   * @returns True if the message was published, false otherwise
   */
  async publish(
    exchange: string,
    routingKey: string,
    content: any,
    options: any = {}
  ): Promise<boolean> {
    const channel = this.connectionService.getChannel();
    if (!channel) {
      this.logger.warn('Cannot publish message: No RabbitMQ channel available');
      return false;
    }

    try {
      // Convert content to Buffer if it's not already
      const buffer = Buffer.isBuffer(content)
        ? content
        : Buffer.from(typeof content === 'string' ? content : JSON.stringify(content));

      // Set default options
      const publishOptions = {
        persistent: true,  // Make message persistent
        contentType: 'application/json',
        contentEncoding: 'utf-8',
        ...options,
      };

      const result = channel.publish(exchange, routingKey, buffer, publishOptions);

      if (result) {
        this.logger.debug(`Published message to ${exchange}:${routingKey}`);
      } else {
        this.logger.warn(`Failed to publish message to ${exchange}:${routingKey} (channel write buffer full)`);
      }

      return result;
    } catch (err) {
      this.logger.error(`Error publishing message to ${exchange}:${routingKey}: ${err.message}`);
      return false;
    }
  }

  /**
   * Consume messages from a queue
   *
   * @param queue - Queue name
   * @param callback - Callback function to process messages
   * @param options - Consumption options
   * @returns Consumer tag or null if failed
   */
  async consume(
    queue: string,
    callback: (msg: any) => Promise<void>,
    options: any = {}
  ): Promise<string | null> {
    const channel = this.connectionService.getChannel();
    if (!channel) {
      this.logger.warn(`Cannot consume from queue ${queue}: No RabbitMQ channel available`);
      return null;
    }

    try {
      // Set default options
      const consumeOptions = {
        noAck: false,  // Require explicit acknowledgement
        ...options,
      };

      // Always set prefetch to 1 for all consumers to prevent message pile-up and timeouts
      if (channel && channel.prefetch) {
        await channel.prefetch(1);
      }

      // Set up consumer
      const { consumerTag } = await channel.consume(queue, async (msg: any) => {
        if (!msg) {
          this.logger.warn(`Consumer for queue ${queue} was cancelled by the server`);
          return;
        }

        try {
          // Parse message content
          const content = this.parseMessage(msg);

          // Process message
          await callback({
            content,
            properties: msg.properties,
            fields: msg.fields,
            raw: msg,
          });

          // Acknowledge message
          if (!consumeOptions.noAck) {
            channel.ack(msg);
          }
        } catch (err) {
          this.logger.error(`Error processing message from ${queue}: ${err.message}`);

          // Reject message and requeue if it's not a parsing error
          if (!consumeOptions.noAck) {
            const requeue = !(err instanceof SyntaxError);
            channel.nack(msg, false, requeue);
          }
        }
      }, consumeOptions);

      this.logger.log(`Started consuming from queue ${queue} with consumer tag ${consumerTag}`);
      return consumerTag;
    } catch (err) {
      this.logger.error(`Failed to consume from queue ${queue}: ${err.message}`);
      return null;
    }
  }

  /**
   * Parse a message from RabbitMQ
   *
   * @param msg - RabbitMQ message
   * @returns Parsed message content
   */
  private parseMessage(msg: any): any {
    const content = msg.content.toString();
    const contentType = msg.properties.contentType;

    if (contentType === 'application/json') {
      return JSON.parse(content);
    }

    return content;
  }

  /**
   * Cancel a consumer
   *
   * @param consumerTag - Consumer tag
   * @returns True if the consumer was cancelled, false otherwise
   */
  async cancelConsumer(consumerTag: string): Promise<boolean> {
    const channel = this.connectionService.getChannel();
    if (!channel) {
      this.logger.warn(`Cannot cancel consumer ${consumerTag}: No RabbitMQ channel available`);
      return false;
    }

    try {
      await channel.cancel(consumerTag);
      this.logger.log(`Cancelled consumer ${consumerTag}`);
      return true;
    } catch (err) {
      this.logger.error(`Failed to cancel consumer ${consumerTag}: ${err.message}`);
      return false;
    }
  }

  /**
   * Check if the queue manager is initialized
   * @returns True if initialized, false otherwise
   */
  isInitialized(): boolean {
    return this.initialized && this.connectionService.isConnected();
  }

  /**
   * Check the health of the queue system
   * @returns Health check result
   */
  async checkHealth(): Promise<any> {
    const channel = this.connectionService.getChannel();
    const isConnected = this.connectionService.isConnected();

    if (!isConnected || !channel) {
      return {
        status: 'down',
        reason: 'RabbitMQ connection not established',
        timestamp: new Date().toISOString()
      };
    }

    try {
      // Check queue status for all queues in the config
      const queues: Record<string, any> = {};
      let overallStatus = 'up';

      for (const queue of queueConfig.queues) {
        try {
          const queueInfo = await channel.checkQueue(queue.name);

          queues[queue.name] = {
            status: 'up',
            messageCount: queueInfo.messageCount,
            consumerCount: queueInfo.consumerCount
          };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          this.logger.error(`Error checking queue ${queue.name}: ${errorMessage}`);

          queues[queue.name] = {
            status: 'down',
            error: errorMessage
          };

          overallStatus = 'down';
        }
      }

      return {
        status: overallStatus,
        queues,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Error checking queue health: ${errorMessage}`);

      return {
        status: 'down',
        reason: errorMessage,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Publish a message directly to a queue (not via exchange)
   * @param queue - Queue name
   * @param content - Message content
   * @param options - Publishing options
   * @returns True if the message was published, false otherwise
   */
  async publishToQueue(
    queue: string,
    content: any,
    options: any = {}
  ): Promise<boolean> {
    const channel = this.connectionService.getChannel();
    if (!channel) {
      this.logger.warn('Cannot publish message: No RabbitMQ channel available');
      return false;
    }
    try {
      const buffer = Buffer.isBuffer(content)
        ? content
        : Buffer.from(typeof content === 'string' ? content : JSON.stringify(content));
      const publishOptions = {
        persistent: true,
        contentType: 'application/json',
        contentEncoding: 'utf-8',
        ...options,
      };
      const result = channel.sendToQueue(queue, buffer, publishOptions);
      if (result) {
        this.logger.debug(`Published message to queue ${queue}`);
      } else {
        this.logger.warn(`Failed to publish message to queue ${queue} (channel write buffer full)`);
      }
      return result;
    } catch (err) {
      this.logger.error(`Error publishing message to queue ${queue}: ${err.message}`);
      return false;
    }
  }
}
