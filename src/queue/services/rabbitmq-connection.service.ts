import { Injectable, Logger, OnModuleD<PERSON>roy, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as amqplib from 'amqplib';

// Define interfaces for better type safety with any to avoid TypeScript errors
type Connection = any;
type Channel = any;
import { EventEmitter } from 'events';

/**
 * Interface for RabbitMQ connection options
 */
export interface RabbitMQConnectionOptions {
  /** RabbitMQ server URL */
  url: string;

  /** Socket options for the connection */
  socketOptions?: any;

  /** Reconnection strategy options */
  reconnect?: {
    /** Whether to automatically reconnect */
    enabled: boolean;

    /** Initial delay before reconnection attempt (ms) */
    initialDelay: number;

    /** Maximum delay between reconnection attempts (ms) */
    maxDelay: number;

    /** Backoff factor for reconnection delay */
    backoffFactor: number;

    /** Maximum number of reconnection attempts (0 = unlimited) */
    maxAttempts: number;
  };
}

/**
 * Default connection options
 */
const DEFAULT_OPTIONS: RabbitMQConnectionOptions = {
  url: 'amqp://localhost:5672',
  reconnect: {
    enabled: true,
    initialDelay: 1000,
    maxDelay: 30000,
    backoffFactor: 2,
    maxAttempts: 0, // 0 = unlimited
  },
};

/**
 * Service for managing RabbitMQ connections
 *
 * This service provides a robust connection to RabbitMQ with automatic
 * reconnection, connection event handling, and channel management.
 */
@Injectable()
export class RabbitMQConnectionService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RabbitMQConnectionService.name);
  private readonly options: RabbitMQConnectionOptions;
  private connection: Connection | null = null;
  private channel: Channel | null = null;
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private isShuttingDown = false;
  private readonly events = new EventEmitter();

  /**
   * Creates a new RabbitMQConnectionService
   *
   * @param configService - NestJS ConfigService for accessing configuration
   */
  constructor(private readonly configService: ConfigService) {
    // Load connection options from configuration
    this.options = {
      url: this.configService.get<string>('rabbitmq.url') || DEFAULT_OPTIONS.url,
      reconnect: {
        enabled: true,
        initialDelay: this.configService.get<number>('rabbitmq.reconnect.initialDelay') ||
                      DEFAULT_OPTIONS.reconnect!.initialDelay,
        maxDelay: this.configService.get<number>('rabbitmq.reconnect.maxDelay') ||
                  DEFAULT_OPTIONS.reconnect!.maxDelay,
        backoffFactor: this.configService.get<number>('rabbitmq.reconnect.backoffFactor') ||
                       DEFAULT_OPTIONS.reconnect!.backoffFactor,
        maxAttempts: this.configService.get<number>('rabbitmq.reconnect.maxAttempts') ||
                     DEFAULT_OPTIONS.reconnect!.maxAttempts,
      },
      socketOptions: {
        heartbeat: 60, // Heartbeat every 60 seconds
        timeout: 30000, // Connection timeout of 30 seconds
        reconnectTimeInSeconds: 3, // Reconnect every 5 seconds
      },
    };
  }

  /**
   * Initialize the RabbitMQ connection when the module is initialized
   */
  async onModuleInit(): Promise<void> {
    this.logger.log('Initializing RabbitMQ connection...');
    try {
      await this.connect();
    } catch (error) {
      // Log the error but don't rethrow it to prevent the application from crashing
      this.logger.error(`Failed to initialize RabbitMQ connection: ${error.message}`);
      // The reconnect logic in connect() will handle reconnection attempts
    }
  }

  /**
   * Clean up the RabbitMQ connection when the module is destroyed
   */
  async onModuleDestroy(): Promise<void> {
    this.logger.log('Closing RabbitMQ connection...');
    this.isShuttingDown = true;
    await this.disconnect();
  }

  /**
   * Get the current connection
   * @returns The RabbitMQ connection or null if not connected
   */
  getConnection(): Connection | null {
    return this.connection;
  }

  /**
   * Get the current channel
   * @returns The RabbitMQ channel or null if not connected
   */
  getChannel(): Channel | null {
    return this.channel;
  }

  /**
   * Check if the connection is established
   * @returns True if connected, false otherwise
   */
  isConnected(): boolean {
    return this.connection !== null && this.channel !== null;
  }

  /**
   * Subscribe to connection events
   * @param event - The event name ('connected', 'disconnected', 'error')
   * @param listener - The event listener function
   */
  on(event: string, listener: (...args: any[]) => void): void {
    this.events.on(event, listener);
  }

  /**
   * Connect to RabbitMQ
   * @returns A promise that resolves when connected
   */
  async connect(): Promise<void> {
    if (this.isConnected()) {
      this.logger.debug('Already connected to RabbitMQ');
      return;
    }

    try {
      this.logger.log(`Connecting to RabbitMQ at ${this.options.url}...`);

      // Create connection with socket options
      this.connection = await amqplib.connect(this.options.url, this.options.socketOptions);

      // Set up connection event handlers
      if (this.connection) {
        this.connection.on('error', (err: Error) => {
          this.logger.error(`RabbitMQ connection error: ${err.message}`);
          this.events.emit('error', err);
        });

        this.connection.on('close', () => {
          this.logger.warn('RabbitMQ connection closed');
          this.channel = null;
          this.connection = null;
          this.events.emit('disconnected');

          if (!this.isShuttingDown && this.options.reconnect?.enabled) {
            this.scheduleReconnect();
          }
        });

        // Create channel
        await this.createChannel();

        // Reset reconnect attempts on successful connection
        this.reconnectAttempts = 0;
        this.logger.log('Successfully connected to RabbitMQ');
        this.events.emit('connected');
      }
    } catch (err) {
      this.logger.error(`Failed to connect to RabbitMQ: ${err.message}`);
      this.events.emit('error', err);

      if (!this.isShuttingDown && this.options.reconnect?.enabled) {
        this.scheduleReconnect();
      } else {
        this.logger.warn('RabbitMQ reconnection is disabled. The application will continue without RabbitMQ.');
      }
    }
  }

  /**
   * Create a new channel
   */
  private async createChannel(): Promise<void> {
    if (!this.connection) {
      throw new Error('Cannot create channel: No connection available');
    }

    try {
      this.channel = await this.connection.createChannel();
      
      // Set up channel event handlers
      this.channel.on('error', (err: Error) => {
        this.logger.error(`RabbitMQ channel error: ${err.message}`);
        this.events.emit('error', err);
      });

      this.channel.on('close', () => {
        this.logger.warn('RabbitMQ channel closed');
        this.channel = null;

        // Try to create a new channel if the connection is still open
        if (this.connection && !this.isShuttingDown) {
          setTimeout(() => this.createChannel(), 5000);
        }
      });

      // Set prefetch count to 1 to ensure fair distribution
      await this.channel.prefetch(1);
      
      this.logger.log('Successfully created RabbitMQ channel');
    } catch (err) {
      this.logger.error(`Failed to create RabbitMQ channel: ${err.message}`);
      throw err;
    }
  }

  /**
   * Schedule a reconnection attempt with exponential backoff
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    const delay = Math.min(
      this.options.reconnect!.initialDelay * Math.pow(this.options.reconnect!.backoffFactor, this.reconnectAttempts),
      this.options.reconnect!.maxDelay
    );

    this.logger.log(`Scheduling reconnection attempt ${this.reconnectAttempts + 1} in ${delay}ms`);

    this.reconnectTimer = setTimeout(async () => {
      if (this.isShuttingDown) {
        return;
      }

      this.reconnectAttempts++;
      try {
        await this.connect();
      } catch (err) {
        this.logger.error(`Reconnection attempt ${this.reconnectAttempts} failed: ${err.message}`);
        if (this.options.reconnect!.maxAttempts === 0 || this.reconnectAttempts < this.options.reconnect!.maxAttempts) {
          this.scheduleReconnect();
        } else {
          this.logger.error('Maximum reconnection attempts reached');
        }
      }
    }, delay);
  }

  /**
   * Disconnect from RabbitMQ
   * @returns A promise that resolves when disconnected
   */
  async disconnect(): Promise<void> {
    // Clear any pending reconnect timer
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    // Close channel if open
    if (this.channel) {
      try {
        await this.channel.close();
        this.logger.log('Closed RabbitMQ channel');
      } catch (err) {
        this.logger.error(`Error closing RabbitMQ channel: ${err.message}`);
      } finally {
        this.channel = null;
      }
    }

    // Close connection if open
    if (this.connection) {
      try {
        await this.connection.close();
        this.logger.log('Closed RabbitMQ connection');
      } catch (err) {
        this.logger.error(`Error closing RabbitMQ connection: ${err.message}`);
      } finally {
        this.connection = null;
      }
    }

    this.events.emit('disconnected');
  }
}
