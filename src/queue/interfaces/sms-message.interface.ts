/**
 * Interface for SMS message credentials
 */
export interface SmsCredential {
  /**
   * SMS service provider (TELE, SAFARICOM, UNKNOWN)
   */
  sms_service_provider?: string;

  /**
   * Username for authentication
   */
  usr: string;

  /**
   * Password for authentication
   */
  pwd: string;

  /**
   * Sender ID or from number
   */
  from: string;

  /**
   * Authentication token
   */
  token: string;

  /**
   * Reverse flag
   */
  reverse?: boolean;

  /**
   * Async flag
   */
  async?: boolean;

  /**
   * SDP (Service Delivery Platform)
   */
  sdp?: string;
}

/**
 * Interface for single SMS message data
 */
export interface SingleSmsMessageData {
  /**
   * SMS credentials
   */
  sms_credential: SmsCredential;

  /**
   * SMS message content
   */
  message: string;

  /**
   * Phone number to send the SMS to
   */
  to: string;

  /**
   * Worker category for routing to specific workers
   */
  workerCategory?: string;

  /**
   * Numeric value for dynamic worker assignment
   */
  workerAssignmentValue?: number;

  /**
   * Unique request ID for tracking
   */
  requestId?: string;

  /**
   * Unique parameter to distinguish SMS messages
   */
  uniqueParam?: string;

  /**
   * SMS log ID
   */
  sms_log_id?: number;
}

/**
 * Interface for bulk SMS message data
 */
export interface BulkSmsMessageData {
  /**
   * SMS credentials
   */
  sms_credential: SmsCredential;

  /**
   * User ID
   */
  user_id: number;

  /**
   * Billing ID
   */
  billing_id: number;

  /**
   * Job name
   */
  job_name: string;

  /**
   * Job unique ID
   */
  job_unique_id: string;

  /**
   * SMS body ID
   */
  sms_body_id: number;

  /**
   * Type of bulk SMS (DASHBOARD, API, TRADITIONAL)
   */
  bulk_type: string;

  /**
   * Receivers (contact_group_id for DASHBOARD, bulk_job_id for API)
   */
  receivers: string;

  /**
   * SMS message content
   */
  message: string;

  /**
   * Unique request ID for tracking
   */
  requestId?: string;

  /**
   * Worker category for routing to specific workers
   */
  workerCategory?: string;

  /**
   * Numeric value for dynamic worker assignment
   */
  workerAssignmentValue?: number;
}

/**
 * Interface for RabbitMQ message content
 */
export interface RabbitMQMessageContent {
  /**
   * Message pattern (single, bulk, etc.)
   */
  pattern: string;

  /**
   * Message data (SingleSmsMessageData or BulkSmsMessageData)
   */
  data: SingleSmsMessageData | BulkSmsMessageData;

  /**
   * SMS credentials (for error handling)
   */
  sms_credential?: SmsCredential;

  /**
   * Phone number to send the SMS to (for error handling)
   */
  to?: string;

  /**
   * Receivers (for error handling)
   */
  receivers?: string;
}

/**
 * Interface for RabbitMQ message properties
 */
export interface RabbitMQMessageProperties {
  /**
   * Message headers
   */
  headers: {
    /**
     * Request ID
     */
    requestId?: string;

    /**
     * Retry count
     */
    retryCount?: number;

    /**
     * Error type
     */
    errorType?: string;

    /**
     * Message count (for bulk messages)
     */
    messageCount?: number;

    /**
     * Bulk type (for bulk messages)
     */
    bulkType?: string;
  };

  /**
   * Message ID
   */
  messageId?: string;

  /**
   * Correlation ID
   */
  correlationId?: string;

  /**
   * Delivery mode (1 = non-persistent, 2 = persistent)
   */
  deliveryMode?: number;
}

/**
 * Interface for RabbitMQ message
 */
export interface RabbitMQMessage {
  /**
   * Message content
   */
  content: RabbitMQMessageContent;

  /**
   * Message properties
   */
  properties: RabbitMQMessageProperties;
}
