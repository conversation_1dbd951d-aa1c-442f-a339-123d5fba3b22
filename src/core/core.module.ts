import { Module, Global } from '@nestjs/common';
import { SharedModule } from '../shared/shared.module';
import { WorkerManagerService } from '../worker/services/worker-manager.service';
import { SmsProviderService } from '../sms/services/sms-provider.service';

@Global()
@Module({
  imports: [
    SharedModule,
  ],
  providers: [
    WorkerManagerService,
    SmsProviderService,
  ],
  exports: [
    WorkerManagerService,
    SmsProviderService,
  ],
})
export class CoreModule {}
