import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { SmsController } from '../sms/sms.controller';
import { SmsService } from '../sms/sms.service';
import { SmsProviderService } from '../sms/services/sms-provider.service';
import { v4 as uuidv4 } from 'uuid';
import { ConfigService } from '@nestjs/config';

/**
 * API test for the SMS endpoints
 *
 * This test verifies that the SMS API endpoints work correctly.
 */
describe('SMS API', () => {
  let app: INestApplication;
  let smsService: SmsService;

  beforeAll(async () => {
    // Create a mock SmsService
    const mockSmsService = {
      sendSingle: jest.fn().mockImplementation(async (data) => ({
        statusCode: 202,
        message: 'SMS has been queued for delivery',
        requestId: data.requestId || 'test-request-id',
      })),
      sendUnifiedBulk: jest.fn().mockImplementation(async (data) => ({
        statusCode: 202,
        message: `Bulk SMS for contact group ${data.receivers} has been queued for delivery`,
        requestId: data.requestId || 'test-request-id',
        job_unique_id: data.job_unique_id,
        bulk_type: data.bulk_type,
      })),
    };

    // Create a mock SmsProviderService
    const mockSmsProviderService = {
      sendSingleSms: jest.fn().mockImplementation(async (data) => ({
        status: 'sent',
        provider: 'test',
        to: data.to,
        messageId: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
        timestamp: new Date().toISOString(),
        requestId: data.requestId || 'unknown',
        workerId: 'test-worker',
        workerCategory: data.workerCategory || 'default',
        processingTime: 10,
        messageType: 'single',
      })),
      sendBulkSms: jest.fn().mockImplementation(async (data) => ({
        status: 'queued',
        requestId: data.requestId || 'unknown',
        bulkType: data.bulk_type || 'TRADITIONAL',
        workerId: 'test-worker',
        timestamp: new Date().toISOString(),
      })),
    };

    // Create a testing module with just the controller and mocked services
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [SmsController],
      providers: [
        {
          provide: SmsService,
          useValue: mockSmsService,
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key: string) => {
              if (key === 'sms.sender') return 'SENDER';
              if (key === 'sms.password') return 'testpass';
              if (key === 'sms.token') return 'testtoken';
              if (key === 'sms.username') return 'testuser';
            }),
          },
        },
        {
          provide: SmsProviderService,
          useValue: mockSmsProviderService,
        },
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Get the services
    smsService = moduleFixture.get<SmsService>(SmsService);
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('/sms/send/single (POST)', () => {
    it('should send a single SMS message', () => {
      // Mock the smsService.sendSingle method
      const sendSingleSpy = jest.spyOn(smsService, 'sendSingle');

      // Create a test message
      const singleSmsData = {
        to: '251953960596',
        message: 'Test message',
        sms_credential: {
          usr: 'testuser',
          pwd: 'testpass',
          from: 'SENDER',
          token: 'testtoken'
        }
      };

      // Send the request
      return request(app.getHttpServer())
        .post('/sms/send/single')
        .send(singleSmsData)
        .expect(202)
        .expect((res: any) => {
          expect(res.body).toHaveProperty('statusCode');
          expect(res.body.statusCode).toBe(202);
          expect(res.body).toHaveProperty('message');
          expect(res.body.message).toBe('SMS has been queued for delivery');
          expect(sendSingleSpy).toHaveBeenCalled();
        });
    });
  });

  describe('/sms/send/bulk (POST)', () => {
    it('should send a bulk SMS message', () => {
      // Mock the smsService.sendUnifiedBulk method
      const sendUnifiedBulkSpy = jest.spyOn(smsService, 'sendUnifiedBulk');

      // Create a test message
      const bulkSmsData = {
        message: 'Bulk test message',
        receivers: 'contact_group_123',
        bulk_type: 'DASHBOARD',
        user_id: 12345,
        billing_id: 67890,
        job_name: 'Test Bulk SMS',
        job_unique_id: `test-${uuidv4()}`,
        sms_body_id: 456,
        sms_credential: {
          usr: 'testuser',
          pwd: 'testpass',
          from: 'SENDER',
          token: 'testtoken',
          sms_service_provider: 'TELE',
        },
      };

      // Send the request
      return request(app.getHttpServer())
        .post('/sms/send/bulk')
        .send(bulkSmsData)
        .expect(202)
        .expect((res: any) => {
          expect(res.body).toHaveProperty('statusCode');
          expect(res.body.statusCode).toBe(202);
          expect(res.body).toHaveProperty('message');
          expect(res.body.message).toBe('Bulk SMS for contact group contact_group_123 has been queued for delivery');
          expect(sendUnifiedBulkSpy).toHaveBeenCalled();
        });
    });
  });
});
