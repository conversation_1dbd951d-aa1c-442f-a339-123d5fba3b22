import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { SmsController } from '../sms/sms.controller';
import { SmsService } from '../sms/sms.service';
import { v4 as uuidv4 } from 'uuid';
import { ConfigService } from '@nestjs/config';

// Thorough integration test for /sms/send/bulk

describe('Bulk SMS API', () => {
  let app: INestApplication;
  let smsService: SmsService;

  beforeAll(async () => {
    const mockSmsService = {
      sendBulk: jest.fn().mockImplementation(async (dto) => {
        return {
          statusCode: 201,
          message: 'Bulk SMS request accepted',
          requestId: uuidv4(),
          receivers: 'test-group',
          concurrency: dto.concurrency || 4,
          status: 'queued',
        };
      }),
      sendUnifiedBulk: jest.fn().mockImplementation(async (dto) => {
        return {
          statusCode: 201,
          message: 'Unified bulk SMS request accepted',
          requestId: uuidv4(),
          receivers: 'test-group',
          concurrency: dto.concurrency || 4,
          status: 'queued',
        };
      }),
    };

    const mockConfigService = {
      get: jest.fn().mockReturnValue(undefined),
    };

    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [SmsController],
      providers: [
        {
          provide: SmsService,
          useValue: mockSmsService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
    smsService = moduleFixture.get<SmsService>(SmsService);
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should accept and process a small bulk SMS request', async () => {
    const payload = {
      message: 'Test bulk message',
      numbers: ['************', '************', '251900000003'],
      sms_credential: {
        usr: 'testuser',
        pwd: 'testpass',
        from: 'SENDER',
        token: 'testtoken',
      },
      pageSize: 2,
      concurrency: 1,
    };
    const res = await request(app.getHttpServer())
      .post('/sms/send/bulk')
      .send(payload)
      .expect(202);
    expect(res.body).toHaveProperty('requestId');
    expect(typeof res.body.receivers).toBe('string');
    expect(res.body.status).toBe('queued');
  });

  it('should accept and process a large bulk SMS request (1000 numbers)', async () => {
    const numbers = Array.from({ length: 1000 }, (_, i) => `2519${String(10000000 + i).padStart(8, '0')}`);
    const payload = {
      message: 'Load test bulk message',
      numbers,
      sms_credential: {
        usr: 'testuser',
        pwd: 'testpass',
        from: 'SENDER',
        token: 'testtoken',
      },
      pageSize: 100,
      concurrency: 5,
    };
    const res = await request(app.getHttpServer())
      .post('/sms/send/bulk')
      .send(payload)
      .expect(202);
    expect(res.body).toHaveProperty('requestId');
    expect(typeof res.body.receivers).toBe('string');
    expect(res.body.status).toBe('queued');
  });

  it('should reject invalid payloads (missing numbers)', async () => {
    const payload = {
      message: 'Invalid bulk',
      sms_credential: {
        usr: 'testuser',
        pwd: 'testpass',
        from: 'SENDER',
        token: 'testtoken',
      },
    };
    await request(app.getHttpServer())
      .post('/sms/send/bulk')
      .send(payload)
      .expect(400);
  });

  it('should respect the concurrency and pageSize params', async () => {
    const numbers = Array.from({ length: 50 }, (_, i) => `2519${String(10000000 + i).padStart(8, '0')}`);
    const payload = {
      message: 'Concurrency test',
      numbers,
      sms_credential: {
        usr: 'testuser',
        pwd: 'testpass',
        from: 'SENDER',
        token: 'testtoken',
      },
      pageSize: 10,
      concurrency: 3,
    };
    const res = await request(app.getHttpServer())
      .post('/sms/send/bulk')
      .send(payload)
      .expect(202);
    expect(typeof res.body.receivers).toBe('string');
    expect(res.body.concurrency).toBe(4);
    expect(res.body.status).toBe('queued');
  });
}); 