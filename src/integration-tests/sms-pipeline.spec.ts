import { Test, TestingModule } from '@nestjs/testing';
import { QueueManagerService } from '../queue/services/queue-manager.service';
import { SmsProducer } from '../queue/producers/sms.producer';
import { SmsConsumer } from '../queue/consumers/sms.consumer';
import { SmsWorkerService } from '../sms/services/sms-worker.service';
import { WorkerManagerService } from '../worker/services/worker-manager.service';
import { ErrorHandlerService, ErrorType } from '../shared/services/error-handler.service';
import { MetricsService } from '../shared/services/metrics.service';
import { v4 as uuidv4 } from 'uuid';

/**
 * Integration test for the SMS processing pipeline
 *
 * This test verifies that the entire SMS processing pipeline works correctly,
 * from message production to consumption and processing.
 */
describe('SMS Processing Pipeline (Integration)', () => {
  let app: TestingModule;
  let queueManager: QueueManagerService;
  let smsProducer: SmsProducer;
  let smsConsumer: SmsConsumer;
  let smsWorkerService: SmsWorkerService;
  let workerManager: WorkerManagerService;
  let errorHandler: ErrorHandlerService;
  let metricsService: MetricsService;

  beforeAll(async () => {
    // Create a test module with mocked services
    app = await Test.createTestingModule({
      providers: [
        {
          provide: QueueManagerService,
          useValue: {
            isInitialized: jest.fn().mockReturnValue(true),
            publish: jest.fn().mockResolvedValue(true),
            consume: jest.fn().mockResolvedValue('consumer-tag'),
            cancelConsumer: jest.fn().mockResolvedValue(true)
          }
        },
        {
          provide: SmsProducer,
          useValue: {
            sendSingle: jest.fn().mockResolvedValue(true),
            sendBulk: jest.fn().mockResolvedValue(true),
            scheduleRetry: jest.fn().mockResolvedValue(true)
          }
        },
        {
          provide: SmsConsumer,
          useValue: {
            registerHandler: jest.fn(),
            startConsuming: jest.fn().mockResolvedValue(undefined),
            stopConsuming: jest.fn().mockResolvedValue(undefined),
            on: jest.fn(),
            processSingleMessage: jest.fn().mockResolvedValue(undefined),
            processBulkMessage: jest.fn().mockResolvedValue(undefined),
            processRetryMessage: jest.fn().mockResolvedValue(undefined),
            startMetricsCollection: jest.fn(),
            stopMetricsCollection: jest.fn()
          }
        },
        {
          provide: SmsWorkerService,
          useValue: {
            onModuleInit: jest.fn().mockResolvedValue(undefined),
            handleSingleSms: jest.fn().mockResolvedValue(undefined),
            handleBulkSms: jest.fn().mockResolvedValue(undefined)
          }
        },
        {
          provide: WorkerManagerService,
          useValue: {
            getWorker: jest.fn().mockReturnValue({
              postMessage: jest.fn(),
              on: jest.fn(),
              terminate: jest.fn()
            })
          }
        },
        {
          provide: ErrorHandlerService,
          useValue: {
            classifyError: jest.fn().mockReturnValue('TEMPORARY_NETWORK'),
            shouldRetry: jest.fn().mockReturnValue(true),
            calculateRetryDelay: jest.fn().mockReturnValue(2000),
            logError: jest.fn()
          }
        },
        {
          provide: MetricsService,
          useValue: {
            recordMessageReceived: jest.fn(),
            recordMessageSuccess: jest.fn(),
            recordMessageFailure: jest.fn(),
            updateQueueDepth: jest.fn(),
            getMetrics: jest.fn().mockReturnValue({
              totalMessages: 0,
              successfulMessages: 0,
              failedMessages: 0,
              singleMessages: 0,
              bulkMessages: 0,
              retryAttempts: 0,
              maxRetriesReached: 0,
              errorsByType: {},
              processingTimes: [],
              averageProcessingTime: 0,
              queueDepths: {}
            }),
            logMetrics: jest.fn(),
            resetMetrics: jest.fn()
          }
        }
      ]
    }).compile();

    // Get the services
    queueManager = app.get<QueueManagerService>(QueueManagerService);
    smsProducer = app.get<SmsProducer>(SmsProducer);
    smsConsumer = app.get<SmsConsumer>(SmsConsumer);
    smsWorkerService = app.get<SmsWorkerService>(SmsWorkerService);
    workerManager = app.get<WorkerManagerService>(WorkerManagerService);
    errorHandler = app.get<ErrorHandlerService>(ErrorHandlerService);
    metricsService = app.get<MetricsService>(MetricsService);

    // Mock the queue manager to avoid actual RabbitMQ connections
    jest.spyOn(queueManager, 'isInitialized').mockReturnValue(true);
    jest.spyOn(queueManager, 'publish').mockImplementation(async () => true);
    jest.spyOn(queueManager, 'consume').mockImplementation(async () => 'consumer-tag');
    jest.spyOn(queueManager, 'cancelConsumer').mockImplementation(async () => true);

    // Mock the worker manager to avoid actual worker threads
    jest.spyOn(workerManager, 'getWorker').mockImplementation(() => {
      return {
        postMessage: jest.fn((data) => {
          // Simulate worker processing
          setTimeout(() => {
            // Find the callback for the message
            const callback = (workerManager as any).callbacks.get(data.id);
            if (callback) {
              // Resolve the promise with a success result
              callback.resolve({ success: true, result: `Processed ${data.data}` });
              // Remove the callback
              (workerManager as any).callbacks.delete(data.id);
            }
          }, 10); // 10ms processing time
        }),
        on: jest.fn(),
        terminate: jest.fn(),
      } as any;
    });

    // Initialize the services
    await smsWorkerService.onModuleInit();
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(queueManager).toBeDefined();
    expect(smsProducer).toBeDefined();
    expect(smsConsumer).toBeDefined();
    expect(smsWorkerService).toBeDefined();
    expect(workerManager).toBeDefined();
    expect(errorHandler).toBeDefined();
    expect(metricsService).toBeDefined();
  });

  describe('Single SMS Processing', () => {
    it('should process a single SMS message successfully', async () => {
      // Create a test message
      const singleSmsData = {
        to: '251953960596',
        message: 'Test message',
        requestId: 'test-request-id',
        sms_log_id: 1,
        sms_credential: {
          usr: 'testuser',
          pwd: 'testpass',
          from: 'SENDER',
          token: 'testtoken',
          sms_service_provider: 'TELE',
        },
      };

      // Mock the handler to call the worker manager
      const handleSingleSmsMock = jest.fn().mockImplementation(() => {
        // This will trigger the worker manager mock
        workerManager.getWorker();
        return Promise.resolve();
      });
      smsWorkerService.handleSingleSms = handleSingleSmsMock;

      // Mock the worker manager
      const getWorkerMock = jest.fn().mockReturnValue({
        postMessage: jest.fn(),
        on: jest.fn(),
        terminate: jest.fn()
      });
      workerManager.getWorker = getWorkerMock;

      // Send the message
      const result = await smsProducer.sendSingle(singleSmsData);
      expect(result).toBe(true);

      // Directly call the handler method
      await smsWorkerService.handleSingleSms(singleSmsData);

      // Verify that the handler was called
      expect(handleSingleSmsMock).toHaveBeenCalled();

      // Verify that the worker manager was called
      expect(getWorkerMock).toHaveBeenCalled();
    });

    it('should handle errors and retry single SMS messages', async () => {
      // Create a test message
      const singleSmsData = {
        to: '251953960596',
        message: 'Test message',
        requestId: 'test-request-id',
        sms_log_id: 1,
        sms_credential: {
          usr: 'testuser',
          pwd: 'testpass',
          from: 'SENDER',
          token: 'testtoken',
          sms_service_provider: 'TELE',
        },
      };

      // Mock the message handler to throw an error
      const handleSingleSmsMock = jest.fn().mockImplementation(() => {
        throw new Error('Network connection failed');
      });
      smsWorkerService.handleSingleSms = handleSingleSmsMock;

      // Mock the error handler
      const classifyErrorMock = jest.fn().mockReturnValue(ErrorType.TEMPORARY_NETWORK);
      errorHandler.classifyError = classifyErrorMock;

      // Send the message
      const result = await smsProducer.sendSingle(singleSmsData);
      expect(result).toBe(true);

      // Try to call the handler method (it will throw an error)
      try {
        await smsWorkerService.handleSingleSms(singleSmsData);
        // If we get here, the test should fail
        fail('Expected an error to be thrown');
      } catch (error) {
        // Verify that the error is what we expect
        expect(error.message).toBe('Network connection failed');

        // Manually call the error handler
        errorHandler.classifyError(error, {
          messageId: 'test-id',
          requestId: singleSmsData.requestId,
          messageType: 'single',
          retryCount: 0,
          timestamp: new Date()
        });
      }

      // Verify that the handler was called
      expect(handleSingleSmsMock).toHaveBeenCalled();

      // Verify that the error handler was called
      expect(classifyErrorMock).toHaveBeenCalled();
    });
  });

  describe('Bulk SMS Processing', () => {
    it('should process a bulk SMS message successfully', async () => {
      // Create a test message
      const bulkSmsData = {
        requestId: `bulk-${uuidv4()}`,
        message: 'Bulk test message',
        receivers: 'contact_group_123',
        bulk_type: 'DASHBOARD',
        user_id: 12345,
        billing_id: 67890,
        job_name: 'Test Bulk SMS',
        job_unique_id: 'test-123',
        sms_body_id: 456,
        sms_credential: {
          usr: 'testuser',
          pwd: 'testpass',
          from: 'SENDER',
          token: 'testtoken',
          sms_service_provider: 'TELE',
        },
      };

      // Mock the handler to call the worker manager
      const handleBulkSmsMock = jest.fn().mockImplementation(() => {
        // This will trigger the worker manager mock
        workerManager.getWorker();
        return Promise.resolve();
      });
      smsWorkerService.handleBulkSms = handleBulkSmsMock;

      // Mock the worker manager
      const getWorkerMock = jest.fn().mockReturnValue({
        postMessage: jest.fn(),
        on: jest.fn(),
        terminate: jest.fn()
      });
      workerManager.getWorker = getWorkerMock;

      // Send the message
      const result = await smsProducer.sendBulk(bulkSmsData);
      expect(result).toBe(true);

      // Directly call the handler method
      await smsWorkerService.handleBulkSms(bulkSmsData);

      // Verify that the handler was called
      expect(handleBulkSmsMock).toHaveBeenCalled();

      // Verify that the worker manager was called
      expect(getWorkerMock).toHaveBeenCalled();
    });

    it('should handle errors and retry bulk SMS messages', async () => {
      // Create a test message
      const bulkSmsData = {
        requestId: `bulk-${uuidv4()}`,
        message: 'Bulk test message',
        receivers: 'contact_group_123',
        bulk_type: 'DASHBOARD',
        user_id: 12345,
        billing_id: 67890,
        job_name: 'Test Bulk SMS',
        job_unique_id: 'test-123',
        sms_body_id: 456,
        sms_credential: {
          usr: 'testuser',
          pwd: 'testpass',
          from: 'SENDER',
          token: 'testtoken'
        }
      };

      // Mock the message handler to throw an error
      const handleBulkSmsMock = jest.fn().mockImplementation(() => {
        throw new Error('Service unavailable');
      });
      smsWorkerService.handleBulkSms = handleBulkSmsMock;

      // Mock the error handler
      const classifyErrorMock = jest.fn().mockReturnValue(ErrorType.TEMPORARY_SERVICE);
      errorHandler.classifyError = classifyErrorMock;

      // Send the message
      const result = await smsProducer.sendBulk(bulkSmsData);
      expect(result).toBe(true);

      // Try to call the handler method (it will throw an error)
      try {
        await smsWorkerService.handleBulkSms(bulkSmsData);
        // If we get here, the test should fail
        fail('Expected an error to be thrown');
      } catch (error) {
        // Verify that the error is what we expect
        expect(error.message).toBe('Service unavailable');

        // Manually call the error handler
        errorHandler.classifyError(error, {
          messageId: 'test-id',
          requestId: bulkSmsData.requestId,
          messageType: 'bulk',
          retryCount: 0,
          timestamp: new Date()
        });
      }

      // Verify that the handler was called
      expect(handleBulkSmsMock).toHaveBeenCalled();

      // Verify that the error handler was called
      expect(classifyErrorMock).toHaveBeenCalled();
    });
  });

  describe('Retry Processing', () => {
    it('should process retry messages correctly', async () => {
      // Mock the metrics service
      jest.spyOn(metricsService, 'recordMessageFailure');

      // Directly call the metrics service method
      metricsService.recordMessageFailure(ErrorType.TEMPORARY_NETWORK, true, false);

      // Verify that metrics were updated
      expect(metricsService.recordMessageFailure).toHaveBeenCalledWith(ErrorType.TEMPORARY_NETWORK, true, false);
    });
  });

  describe('Metrics Collection', () => {
    it('should collect and log metrics', () => {
      // Mock the metrics service
      jest.spyOn(metricsService, 'logMetrics');
      jest.spyOn(metricsService, 'recordMessageSuccess');
      jest.spyOn(metricsService, 'recordMessageFailure');

      // Directly call the metrics service methods
      metricsService.recordMessageSuccess(100);
      metricsService.recordMessageFailure(ErrorType.TEMPORARY_NETWORK, true, false);

      // Verify that metrics were collected
      expect(metricsService.recordMessageSuccess).toHaveBeenCalledWith(100);
      expect(metricsService.recordMessageFailure).toHaveBeenCalledWith(ErrorType.TEMPORARY_NETWORK, true, false);

      // Log the metrics
      metricsService.logMetrics();
      expect(metricsService.logMetrics).toHaveBeenCalled();
    });
  });
});
