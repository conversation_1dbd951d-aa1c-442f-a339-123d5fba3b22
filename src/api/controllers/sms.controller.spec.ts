import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  SingleSmsDto,
  BulkSmsDto,
  BulkSmsType,
  SmsServiceProvider,
} from '../../sms/dto/sms-request.dto';
import { SmsController } from '../../sms/sms.controller';
import { SmsService } from '../../sms/sms.service';

// Mock the phoneType function
jest.mock('../../shared/utils/sms.utils', () => ({
  phoneType: jest.fn().mockReturnValue('TELE'),
  SmsUtils: {
    generateMessageId: jest.fn().mockReturnValue('test-message-id'),
    truncateMessage: jest.fn().mockImplementation((msg) => msg),
  },
}));

/**
 * Unit tests for the SmsController
 */
describe('SmsController', () => {
  let controller: SmsController;
  let smsService: SmsService;

  // Mock the logger to avoid console output during tests
  const mockLogger = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    verbose: jest.fn(),
  };

  beforeEach(async () => {
    // Create a testing module with our dependencies
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SmsController],
      providers: [
        {
          provide: SmsService,
          useValue: {
            sendSingle: jest.fn().mockResolvedValue({
              statusCode: 202,
              message: 'SMS has been queued for delivery',
              requestId: 'test-request-id',
            }),
            sendBulk: jest.fn().mockResolvedValue({
              statusCode: 202,
              message: 'SMS messages have been queued for delivery',
              requestId: 'test-request-id',
            }),
            sendUnifiedBulk: jest.fn().mockResolvedValue({
              statusCode: 202,
              message: 'Unified bulk SMS messages have been queued for delivery',
              requestId: 'test-request-id',
            }),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue(4), // Default worker count
          },
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    // Get the controller and service instances
    controller = module.get<SmsController>(SmsController);
    smsService = module.get<SmsService>(SmsService);

    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('sendSingle', () => {
    it('should call smsService.sendSingle with the correct data', async () => {
      // Arrange
      const dto: SingleSmsDto = {
        to: '+**********',
        message: 'Test message',
        sms_log_id: 1,
        sms_credential: {
          usr: 'testuser',
          pwd: 'testpass',
          from: 'SENDER',
          token: 'testtoken',
          sdp: 'string',
          sms_service_provider: 'TELE',
        },
      };

      // Act
      const result = await controller.sendSingle(dto);

      // Assert
      expect(smsService.sendSingle).toHaveBeenCalledTimes(1);
      expect(smsService.sendSingle).toHaveBeenCalledWith(
        expect.objectContaining({
          to: dto.to,
          message: dto.message,
          sms_credential: expect.objectContaining({
            usr: 'testuser',
            pwd: 'testpass',
            from: 'SENDER',
            token: 'testtoken',
            sms_service_provider: expect.any(String), // This is added by the controller
          }),
          requestId: expect.any(String),
          uniqueParam: expect.any(String),
          workerCategory: 'default',
          workerAssignmentValue: expect.any(Number),
        }),
      );
      expect(result).toEqual({
        statusCode: 202,
        message: 'SMS has been queued for delivery',
        requestId: expect.any(String),
      });
    });

    it('should use provided requestId and workerCategory', async () => {
      // Arrange
      const dto: SingleSmsDto = {
        to: '+**********',
        message: 'Test message',
        requestId: 'test-request-id',
        workerCategory: 'high-priority',
        workerAssignmentValue: 12345,
        sms_log_id: 1,
        sms_credential: {
          usr: 'testuser',
          pwd: 'testpass',
          from: 'SENDER',
          token: 'testtoken',
          sms_service_provider: 'TELE',
        },
      };

      // Act
      const result = await controller.sendSingle(dto);

      // Assert
      expect(smsService.sendSingle).toHaveBeenCalledWith(
        expect.objectContaining({
          to: dto.to,
          message: dto.message,
          requestId: 'test-request-id',
          workerCategory: 'high-priority',
          workerAssignmentValue: 12345,
          sms_credential: expect.objectContaining({
            usr: 'testuser',
            pwd: 'testpass',
            from: 'SENDER',
            token: 'testtoken',
            sms_service_provider: expect.any(String), // This is added by the controller
          }),
        }),
      );
      expect(result).toEqual({
        statusCode: 202,
        message: 'SMS has been queued for delivery',
        requestId: 'test-request-id',
      });
    });
  });

  describe('sendBulk', () => {
    it('should call smsService.sendUnifiedBulk with the correct data for DASHBOARD type', async () => {
      // Arrange
      const dto: BulkSmsDto = {
        message: 'Bulk test message',
        receivers: 'contact_group_123',
        bulk_type: BulkSmsType.DASHBOARD,
        user_id: 12345,
        billing_id: 67890,
        job_name: 'Test Bulk SMS',
        job_unique_id: 'test-123',
        sms_body_id: 456,
        sms_credential: {
          usr: 'testuser',
          pwd: 'testpass',
          from: 'SENDER',
          token: 'testtoken',
          sms_service_provider: 'TELE',
        },
      };

      // Act
      const result = await controller.sendBulk(dto);

      // Assert
      expect(smsService.sendUnifiedBulk).toHaveBeenCalledTimes(1);
      expect(smsService.sendUnifiedBulk).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Bulk test message',
          receivers: 'contact_group_123',
          bulk_type: BulkSmsType.DASHBOARD,
          user_id: 12345,
          billing_id: 67890,
          job_name: 'Test Bulk SMS',
          job_unique_id: 'test-123',
          sms_body_id: 456,
          sms_credential: expect.objectContaining({
            usr: 'testuser',
            pwd: 'testpass',
            from: 'SENDER',
            token: 'testtoken',
          }),
        }),
      );
      expect(result).toEqual({
        statusCode: 202,
        message: 'Unified bulk SMS messages have been queued for delivery',
        requestId: 'test-request-id',
      });
    });

    it('should call smsService.sendBulk with the correct data for traditional bulk', async () => {
      // Arrange
      const dto = {
        message: 'Bulk test message',
        receivers: 'contact_group_123',
        user_id: 12345,
        billing_id: 67890,
        job_name: 'Test Bulk SMS',
        sms_credential: {
          usr: 'testuser',
          pwd: 'testpass',
          from: 'SENDER',
          token: 'testtoken',
          sms_service_provider: 'TELE',
        },
      };

      // Act
      const result = await controller.sendBulk(dto);

      // Assert
      expect(smsService.sendBulk).toHaveBeenCalledTimes(1);
      expect(smsService.sendBulk).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Bulk test message',
          receivers: 'contact_group_123',
          user_id: 12345,
          billing_id: 67890,
          job_name: 'Test Bulk SMS',
          bulk_type: 'TRADITIONAL',
          sms_credential: expect.objectContaining({
            usr: 'testuser',
            pwd: 'testpass',
            from: 'SENDER',
            token: 'testtoken',
          }),
          requestId: expect.any(String),
          workerCategory: 'bulk',
          workerAssignmentValue: expect.any(Number),
        }),
      );
      expect(result).toEqual({
        statusCode: 202,
        message: 'SMS messages have been queued for delivery',
        requestId: 'test-request-id',
      });
    });
  });
});
