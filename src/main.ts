// src/main.ts
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe, Logger, VersioningType } from '@nestjs/common';
import helmet from 'helmet';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
// We'll use the LoggerService in a future phase

async function bootstrap() {
  // 1. Create app
  const app = await NestFactory.create(AppModule);

  // 2. Get configuration
  const config = app.get(ConfigService);
  const nodeEnv = config.get<string>('NODE_ENV');
  const port = config.get<number>('PORT', 3000);

  // 3. Security Middleware
  app.use(helmet());
  app.enableCors({
    origin: config.get<string>('CORS_ORIGINS', '*').split(','),
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: true,
  });

  // 4. Global Pipes and Filters
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: { enableImplicitConversion: true },
    }),
  );

  // 5. API Versioning
  app.enableVersioning({
    type: VersioningType.URI,
    prefix: 'api/v',
    defaultVersion: '1',
  });

  // 6. Swagger Documentation (Dev only)
  // if (nodeEnv === 'development') {
  // }
  const swaggerConfig = new DocumentBuilder()
    .setTitle('SMS Service API')
    .setDescription('Handles SMS sending operations')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, swaggerConfig);
  SwaggerModule.setup('api/docs', app, document);

  // 7. Start Application
  console.log(`Starting HTTP server on port ${port}...`);
  await app.listen(port, '0.0.0.0');

  // 9. Log startup information
  const logger = app.get(Logger);
  logger.log(`Application running in ${nodeEnv} mode`);
  logger.log(`HTTP server listening on port ${port}`);
  // These will be used in future phases when we implement the queue module
  // logger.log(`Microservices connected to RabbitMQ at ${rabbitUrl}`);
  // logger.log(`SMS queues: single=${singleQueue}, bulk=${bulkQueue}`);
  // if (nodeEnv === 'development') {
  // }
  logger.log(`Swagger docs available at /api/docs`);
  logger.log(`Health check available at /api/v1/health`);
}

bootstrap();
