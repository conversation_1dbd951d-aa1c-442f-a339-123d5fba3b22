import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@nestjs/common';
import { APP_INTERCEPTOR } from '@nestjs/core';

// Import modular architecture modules
import { SharedModule } from './shared/shared.module';
import { ApiModule } from './api/api.module';
import { QueueModule } from './queue/queue.module';
import { WorkerModule } from './worker/worker.module';
import { SmsModule } from './sms/sms.module';
import { CoreModule } from './core/core.module';
import { ApiTokenInterceptor } from './shared/interceptors';

@Module({
  imports: [
    // Core modules
    SharedModule,
    CoreModule, // Import CoreModule first to resolve circular dependencies
    ApiModule,
    QueueModule,
    WorkerModule,
    SmsModule,
  ],
  controllers: [],
  providers: [
    Logger,
    // Apply the API token interceptor globally
    {
      provide: APP_INTERCEPTOR,
      useClass: ApiTokenInterceptor,
    },
  ],
})
export class AppModule {}
