// sms/sms.module.ts

import { Module, forwardRef } from '@nestjs/common';
import { SharedModule } from '../shared/shared.module';
import { QueueModule } from '../queue/queue.module';
import { WorkerModule } from '../worker/worker.module';

// Import existing components to maintain compatibility during transition
import { SmsController } from './sms.controller';
import { SmsService } from './sms.service';
import { SmsProviderService } from './services/sms-provider.service';
import { SmsWorkerService } from './services/sms-worker.service';
import { SmsWorkerController } from './controllers/sms-worker.controller';

// Import new architecture components
import { JasminProvider } from './providers/jasmin.provider';
import { TemplateService } from './templates/template.service';

// These imports will be removed in future phases
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [
    forwardRef(() => SharedModule),
    forwardRef(() => QueueModule),
    forwardRef(() => WorkerModule),
    // The following imports will be refactored in future phases
    ConfigModule,
    HttpModule,
    ClientsModule.registerAsync([
      {
        name: 'SMS_SINGLE_SERVICE',
        imports: [ConfigModule], // Ensure ConfigModule is imported here
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => {
          const rabbitMqUrl = configService.get<string>('rabbitmq.url') || 'amqp://localhost:5672';
          const singleQueue = configService.get<string>('sms.queues.single') || 'sms_single_queue';
          return {
            transport: Transport.RMQ,
            options: {
              urls: [rabbitMqUrl],
              queue: singleQueue,
              queueOptions: {
                durable: true,
                autoDelete: false,
                deadLetterExchange: 'dlx',
                messageTtl: 86400000, // 24 hours
                arguments: {
                  'x-max-priority': 10
                }
              },
            },
          };
        },
      },
      {
        name: 'SMS_BULK_SERVICE',
        imports: [ConfigModule], // Ensure ConfigModule is imported here
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => {
          const rabbitMqUrl = configService.get<string>('rabbitmq.url') || 'amqp://localhost:5672';
          const bulkQueue = configService.get<string>('sms.queues.bulk') || 'sms_bulk_queue';
          return {
            transport: Transport.RMQ,
            options: {
              urls: [rabbitMqUrl],
              queue: bulkQueue,
              queueOptions: {
                durable: true,
                autoDelete: false,
                deadLetterExchange: 'dlx',
                messageTtl: 86400000, // 24 hours
                arguments: {
                  'x-max-priority': 10
                }
              },
            },
          };
        },
      },
      {
        name: 'SMS_FAILED_SERVICE',
        imports: [ConfigModule], // Ensure ConfigModule is imported here
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => {
          const rabbitMqUrl = configService.get<string>('rabbitmq.url') || 'amqp://localhost:5672';
          const failedQueue = configService.get<string>('sms.queues.failed') || 'sms_failed_queue';
          return {
            transport: Transport.RMQ,
            options: {
              urls: [rabbitMqUrl],
              queue: failedQueue,
              queueOptions: {
                durable: true,
                autoDelete: false,
                deadLetterExchange: 'dlx',
                messageTtl: 86400000, // 24 hours
                arguments: {
                  'x-max-priority': 10
                }
              },
            },
          };
        },
      },
      {
        name: 'SMS_DLR_SERVICE',
        imports: [ConfigModule], // Ensure ConfigModule is imported here
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => {
          const rabbitMqUrl = configService.get<string>('rabbitmq.url') || 'amqp://localhost:5672';
          const dlrQueue = configService.get<string>('sms.queues.dlr') || 'sms_dlr_queue';
          return {
            transport: Transport.RMQ,
            options: {
              urls: [rabbitMqUrl],
              queue: dlrQueue,
              queueOptions: {
                durable: true,
                autoDelete: false,
                deadLetterExchange: 'dlx',
                messageTtl: 86400000, // 24 hours
                arguments: {
                  'x-max-priority': 10
                }
              },
            },
          };
        },
      },
    ]),
  ],
  controllers: [SmsController, SmsWorkerController],
  providers: [
    // Legacy providers
    SmsService,
    SmsWorkerService,
    SmsProviderService,
    // SmsProviderService is now provided by CoreModule

    // New architecture providers
    JasminProvider,
    TemplateService,
  ],
  exports: [
    // Legacy exports
    SmsService,
    SmsWorkerService,
    // SmsProviderService is now provided by CoreModule

    // New architecture exports
    JasminProvider,
    TemplateService,
  ],
})
export class SmsModule {}
