import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BulkSmsDto, SingleSmsDto, SmsRequiredDto } from '../dto/sms-request.dto';
import { WorkerManagerService } from '../../worker/services/worker-manager.service';

@Injectable()
export class SmsProviderService {
  private readonly logger = new Logger(SmsProviderService.name);
  private readonly retries: number;

  constructor(
    private configService: ConfigService,
    @Inject(forwardRef(() => WorkerManagerService))
    private workerManager: WorkerManagerService,
  ) {
    this.retries = configService.get<number>('sms.retries') || 3;
    this.logger.log(`[SmsProviderService] SMS retries set to: ${this.retries}`);
  }

  /**
   * Send a single SMS message
   *
   * @param data - The SMS message data
   * @returns Promise that resolves with the result of the SMS send operation
   */
  async sendSingleSms(data: SingleSmsDto) {
    let lastError: Error = new Error('Unknown error');
    const requestId = data.requestId || 'unknown';

    this.logger.debug(
      `[${requestId}] SMS service provider: ${data.sms_credential?.sms_service_provider || ''}`,
    );

    for (let attempt = 0; attempt < this.retries; attempt++) {
      const provider =
        attempt === 0
          ? this.configService.get<string>('sms.providers.primary') || 'default'
          : this.configService.get<string>('sms.providers.fallback') || 'none';

      this.logger.debug(`[${requestId}] SMS DTO: ${JSON.stringify({ provider, ...data })}`);

      try {
        this.logger.debug(
          `[${requestId}] Attempt ${attempt + 1} using ${provider} for SMS to ${data.to} (Service Provider: ${data.sms_credential?.sms_service_provider || ''})`,
        );
        if (attempt != 0) {
          const workersLength = (this.workerManager as any).singleWorkers.length;
          data.workerAssignmentValue = Math.min(
            Math.floor(Math.random() * workersLength),
            workersLength,
          );
        }
        const result = await this.sendViaProvider(
          {
            attempt: attempt + 1,
            messageType: 'single',
            requestId,
            ...data,
          },
          // data.sms_credential,
          // data.to,
          // data.message,
          // data.sms_log_id,
          // attempt + 1,
          // data.workerCategory,
          // data.workerAssignmentValue,
          // 'single',
          // requestId,
        );
        this.logger.debug(`[${requestId}] SMS to ${data.to} sent successfully via ${provider}`);
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        this.logger.warn(
          `[${requestId}] Attempt ${attempt + 1} failed for SMS to ${data.to}: ${lastError.message}`,
        );
        // Exponential backoff with jitter to prevent thundering herd
        const backoffTime = Math.min(1000 * Math.pow(2, attempt) + Math.random() * 100, 5000);
        await new Promise((resolve) => setTimeout(resolve, backoffTime));
      }
    }
    this.logger.error(`[${requestId}] All ${this.retries} attempts failed for SMS to ${data.to}`);
    throw lastError;
  }

  /**
   * Send a bulk SMS message
   *
   * @param data - The bulk SMS message data
   * @returns Promise that resolves with the result of the bulk SMS send operation
   */
  async sendBulkSms(data: BulkSmsDto) {
    let lastError: Error = new Error('Unknown error');
    const requestId = data.requestId || 'unknown';

    for (let attempt = 0; attempt < this.retries; attempt++) {
      const provider =
        attempt === 0
          ? this.configService.get<string>('sms.providers.primary') || 'default'
          : this.configService.get<string>('sms.providers.fallback') || 'none';

      this.logger.debug(`[${requestId}] SMS DTO: ${JSON.stringify({ provider, ...data })}`);

      try {
        this.logger.debug(
          `[${requestId}] Attempt ${attempt + 1} using ${provider} for SMS to ${data.receivers} (Service Provider: ${data.sms_credential?.sms_service_provider || ''})`,
        );
        if (attempt != 0) {
          const workersLength = (this.workerManager as any).singleWorkers.length;
          data.workerAssignmentValue = Math.min(
            Math.floor(Math.random() * workersLength),
            workersLength,
          );
        }
        const result = await this.sendViaProvider({
          attempt: attempt + 1,
          messageType: 'bulk',
          requestId,
          to: data.receivers,
          ...data,
        });
        this.logger.debug(
          `[${requestId}] SMS to ${data.receivers} sent successfully via ${provider}`,
        );
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        this.logger.warn(
          `[${requestId}] Attempt ${attempt + 1} failed for SMS to ${data.receivers}: ${lastError.message}`,
        );
        // Exponential backoff with jitter to prevent thundering herd
        const backoffTime = Math.min(1000 * Math.pow(2, attempt) + Math.random() * 100, 5000);
        await new Promise((resolve) => setTimeout(resolve, backoffTime));
      }
    }
    this.logger.error(
      `[${requestId}] All ${this.retries} attempts failed for SMS to ${data.receivers}`,
    );
    throw lastError;
  }

  private async sendViaProvider(data: any) {
    //   sms_credentials: SmsRequiredDto,
    //   to: string,
    //   message: string,
    //   sms_body_id: number | string,
    //   attempt: number,
    //   workerCategory: 'single' | 'bulk',
    //   workerAssignmentValue: number = 1,
    //   messageType: string = 'single',
    //   requestId?: string,
    //   user_id?: number,
    //   bulk_type?: string
    // ) {
    // Get a worker for this category from the worker manager
    const worker = this.workerManager.getWorkerForCategory(
      data.workerCategory,
      data.workerAssignmentValue,
    );
    if (!worker) {
      this.logger.error(
        `[${data.requestId}] No worker available for category ${data.workerCategory}`,
      );
      throw new Error('No worker available');
    }

    // Get the worker index for logging
    const workerIndex = data.workerAssignmentValue >= 0 ? data.workerAssignmentValue : -1;
    const workerId = workerIndex >= 0 ? `worker-${workerIndex}` : 'unknown';

    this.logger.log(
      `[${data.requestId}] Assigned ${workerId} for ${data.messageType} SMS to ${data.to} with category ${data.workerCategory || 'default'} and assignment value ${data.workerAssignmentValue || 'none'}`,
    );

    // Create a unique message ID
    // const messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
    data.messageId = data.requestId; // for worker
    // If data is wrapped (pattern/data), set messageId at the top level
    if (data.pattern && data.data) {
      data.messageId = data.requestId; // top-level for wrapper
      data.data.messageId = data.requestId; // also inside data for worker
    }

    // Add delay when sending SMS
    // Delay for 1–3 seconds before sending
    // const delay = Math.floor(Math.random() * 2000) + 1000; // 1000–3000 ms
    // await new Promise((resolve) => setTimeout(resolve, delay));

    return new Promise((resolve, reject) => {
      let settled = false;
      const timeout = setTimeout(() => {
        if (!settled) {
          settled = true;
          worker.removeListener('message', messageHandler);
          reject(new Error('Worker response timeout'));
        }
      }, 60000); // 60 seconds

      // Set up a one-time message handler for this request
      const messageHandler = (response: any) => {
        if (response.requestId === data.requestId) {
          // If it's a log message, just ignore it
          // If it's a heartbeat, ignore it
          if (response.type === 'log' || response.type === 'heartbeat') return;

          // For result messages, process them
          // Remove the listener once we get our response
          worker.removeListener('message', messageHandler);
          clearTimeout(timeout);
          settled = true;

          if (response.success) {
            const result = {
              status: 'sent',
              messageId: response.data?.messageId || response.messageId || data.requestId,
              timestamp: new Date().toISOString(),
              workerId: response.data?.workerId || response.workerId || workerId,
              workerCategory:
                response.data?.workerCategory || response.workerCategory || data.workerCategory,
              processingTime: response.data?.processingTime || response.processingTime,
              ...data,
            };

            this.logger.log(
              `[${data.requestId}] ${data.messageType} SMS to ${data.to} processed successfully by ${workerId} with messageId: ${result.messageId}`,
            );
            resolve(result);
          } else {
            const errorMsg = response.error || 'Unknown error';
            const errorMessageId = response.data?.messageId || response.messageId || data.requestId;
            this.logger.error(
              `[${data.requestId}] ${data.messageType} SMS to ${data.to} failed: ${errorMsg} (messageId: ${errorMessageId})`,
            );
            const errorObj = new Error(errorMsg);
            (errorObj as any).messageId = errorMessageId;
            reject(errorObj);
          }

          // if (response.type === 'result' || response.success !== undefined) {
          // }
          // // For bulk result messages
          // if (response.type === 'bulk_result' || response.success !== undefined) {
          //   // Remove the listener once we get our response
          //   worker.removeListener('message', messageHandler);

          //   if (response.success) {
          //     const result = {
          //       status: 'sent',
          //       messageId: response.data?.messageId || response.messageId || messageId,
          //       timestamp: new Date().toISOString(),
          //       workerId: response.data?.workerId || response.workerId || workerId,
          //       workerCategory:
          //         response.data?.workerCategory || response.workerCategory || data.workerCategory,
          //       processingTime: response.data?.processingTime || response.processingTime,
          //       ...data,
          //     };

          //     this.logger.log(
          //       `[${data.requestId}] ${data.messageType} SMS to ${data.to} processed successfully by ${workerId} with messageId: ${result.messageId}`,
          //     );
          //     resolve(result);
          //   } else {
          //     this.logger.error(
          //       `[${data.requestId}] ${data.messageType} SMS to ${data.to} failed: ${response.error || 'Unknown error'}`,
          //     );
          //     reject(new Error(response.error || 'Unknown error'));
          //   }
          // }
        }
      };

      // Add the message handler
      worker.on('message', messageHandler);

      // Send the message to the worker with enhanced data
      worker.postMessage({
        id: data.requestId,
        data: {
          messageId: data.requestId,
          timestamp: new Date().toISOString(),
          workerId,
          ...data,
        },
        // data: {
        //   sms_credentials,
        //   to,
        //   message,
        //   attempt,
        //   data.requestId,
        //   sms_body_id,
        //   assignmentValue: workerAssignmentValue,
        //   messageType,
        //   timestamp: new Date().toISOString(),
        //   workerId,
        //   user_id,
        //   bulk_type,
        // },
      });
    });
  }
}
