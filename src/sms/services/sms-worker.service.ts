import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { EventPattern } from '@nestjs/microservices';
import { SingleSmsDto, BulkSmsDto } from '../dto/sms-request.dto';
import { SmsProviderService } from './sms-provider.service';
import { SmsConsumer } from '../../queue/consumers/sms.consumer';
import { SmsMessageHandler } from '../../queue/consumers/sms.consumer';

@Injectable()
export class SmsWorkerService implements OnModuleInit, SmsMessageHandler {
  private readonly logger = new Logger(SmsWorkerService.name);
  private readonly workerId = process.env.WORKER_ID || 'local';

  constructor(
    private readonly smsProvider: SmsProviderService,
    private readonly smsConsumer: SmsConsumer,
  ) {
    this.logger.log(`SMS Worker Service initialized with ID: ${this.workerId}`);
  }

  async onModuleInit() {
    // Register this service as a message handler with the SMS consumer
    this.logger.log('Registering as SMS message handler...');
    await this.smsConsumer.registerHandler(this);
    this.logger.log('Successfully registered as SMS message handler');
  }

  /**
   * Handle a single SMS message
   *
   * This method is called by both the EventPattern decorator (for backward compatibility)
   * and the SmsConsumer when a message is received from the queue.
   *
   * @param message - The single SMS message data
   */
  @EventPattern('single')
  async handleSingleSms(message: SingleSmsDto): Promise<void> {
    const data = message;
    const requestId = data.requestId || 'unknown';
    const startTime = Date.now();

    this.logger.log(`[${requestId}] Worker ${this.workerId} processing single SMS to ${data.to}`);

    try {
      await this.smsProvider.sendSingleSms(data);
      const processingTime = Date.now() - startTime;

      this.logger.log(
        `[${requestId}] Worker ${this.workerId} successfully processed SMS to ${data.to} in ${processingTime}ms`,
      );
    } catch (error) {
      this.logger.error(
        `[${requestId}] Worker ${this.workerId} failed to process SMS to ${data.to}: ${error.message}`,
        error.stack,
      );

      // Re-throw the error so the consumer can handle it (e.g., for retries)
      throw error;
    }
  }

  /**
   * Handle a bulk SMS message
   *
   * This method is called by both the EventPattern decorator (for backward compatibility)
   * and the SmsConsumer when a message is received from the queue.
   *
   * @param message - The bulk SMS message data
   */
  @EventPattern('bulk')
  async handleBulkSms(message: BulkSmsDto): Promise<void> {
    // Legacy bulk handling
    const data = message;
    const requestId = data.requestId || 'unknown';
    const startTime = Date.now();
    this.logger.log(
      `[${requestId}] Worker ${this.workerId} processing bulk SMS with unified format`,
    );
    try {
      await this.smsProvider.sendBulkSms(data);
      const processingTime = Date.now() - startTime;
      this.logger.log(
        `[${requestId}] Worker ${this.workerId} sent bulk SMS batch in ${processingTime}ms`,
      );
    } catch (error) {
      this.logger.error(
        `[${requestId}] Worker ${this.workerId} failed to process bulk SMS: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
