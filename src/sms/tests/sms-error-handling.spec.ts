import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { SmsProviderService } from '../services/sms-provider.service';
import { WorkerManagerService } from '../../worker/services/worker-manager.service';
import { SingleSmsDto, SmsServiceProvider } from '../dto/sms-request.dto';
import { Logger } from '@nestjs/common';

describe('SmsProviderService - Error Handling', () => {
  let smsProviderService: SmsProviderService;
  let workerManagerService: WorkerManagerService;

  beforeEach(async () => {
    // Create a mock module
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SmsProviderService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key) => {
              if (key === 'sms.retries') return 3;
              if (key === 'sms.providers.primary') return 'default';
              if (key === 'sms.providers.fallback') return 'none';
              return null;
            }),
          },
        },
        {
          provide: WorkerManagerService,
          useValue: {
            getWorkerForCategory: jest.fn().mockImplementation(() => {
              throw new Error('Worker manager error');
            }),
          },
        },
        {
          provide: Logger,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
            verbose: jest.fn(),
          },
        },
      ],
    }).compile();

    smsProviderService = module.get<SmsProviderService>(SmsProviderService);
    workerManagerService = module.get<WorkerManagerService>(WorkerManagerService);
  });

  it('should handle errors when sending SMS', async () => {
    // Create a test message
    const singleSmsData: SingleSmsDto = {
      to: '+**********',
      message: 'Test message',
      sms_log_id: 1,
      sms_credential: {
        usr: 'testuser',
        pwd: 'testpass',
        from: 'SENDER',
        token: 'testtoken',
        sms_service_provider: SmsServiceProvider.TELE,
      },
      requestId: 'test-request-id',
    };

    // Attempt to send the SMS, which should fail
    await expect(smsProviderService.sendSingleSms(singleSmsData)).rejects.toThrow('Worker manager error');
  });
});
