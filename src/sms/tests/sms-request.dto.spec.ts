import { validate } from 'class-validator';
import { SingleSmsDto } from '../dto/sms-request.dto';

describe('SingleSmsDto', () => {
  it('should validate a valid phone number starting with 2519', async () => {
    // Arrange
    const dto = new SingleSmsDto();
    dto.to = '251953960596';
    dto.message = 'Test message';
    dto.sms_log_id = 1;
    dto.sms_credential = {
      usr: 'testuser',
      pwd: 'testpass',
      from: 'SENDER',
      token: 'testtoken',
      sms_service_provider: 'TELE',
    };

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should validate a valid phone number starting with 2517', async () => {
    // Arrange
    const dto = new SingleSmsDto();
    dto.to = '251711220033';
    dto.message = 'Test message';
    dto.sms_log_id = 1;
    dto.sms_credential = {
      usr: 'testuser',
      pwd: 'testpass',
      from: 'SENDER',
      token: 'testtoken',
      sms_service_provider: 'SAFARICOM',
    };

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('should reject an invalid phone number format', async () => {
    // Arrange
    const dto = new SingleSmsDto();
    dto.to = '+251953960596'; // Invalid format with + prefix
    dto.message = 'Test message';
    dto.sms_log_id = 1;
    dto.sms_credential = {
      usr: 'testuser',
      pwd: 'testpass',
      from: 'SENDER',
      token: 'testtoken',
      sms_service_provider: 'TELE',
    };

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toBeDefined();
    if (errors[0].constraints) {
      expect(errors[0].constraints).toHaveProperty('matches');
      expect(errors[0].constraints.matches).toContain('2519XXXXXXXX or 2517XXXXXXXX');
    }
  });

  it('should reject a phone number with wrong country code', async () => {
    // Arrange
    const dto = new SingleSmsDto();
    dto.to = '123456789012'; // Wrong country code
    dto.message = 'Test message';
    dto.sms_credential = {
      usr: 'testuser',
      pwd: 'testpass',
      from: 'SENDER',
      token: 'testtoken',
      sms_service_provider: 'TELE',
    };

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toBeDefined();
    if (errors[0].constraints) {
      expect(errors[0].constraints).toHaveProperty('matches');
      expect(errors[0].constraints.matches).toContain('2519XXXXXXXX or 2517XXXXXXXX');
    }
  });

  it('should reject a phone number that is too short', async () => {
    // Arrange
    const dto = new SingleSmsDto();
    dto.to = '25195396'; // Too short
    dto.message = 'Test message';
    dto.sms_credential = {
      usr: 'testuser',
      pwd: 'testpass',
      from: 'SENDER',
      token: 'testtoken',
      sms_service_provider: 'TELE',
    };

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toBeDefined();
    if (errors[0].constraints) {
      expect(errors[0].constraints).toHaveProperty('matches');
      expect(errors[0].constraints.matches).toContain('2519XXXXXXXX or 2517XXXXXXXX');
    }
  });

  it('should reject a phone number with wrong prefix (not 2519 or 2517)', async () => {
    // Arrange
    const dto = new SingleSmsDto();
    dto.to = '251812345678'; // Wrong prefix (2518 instead of 2519 or 2517)
    dto.message = 'Test message';
    dto.sms_credential = {
      usr: 'testuser',
      pwd: 'testpass',
      from: 'SENDER',
      token: 'testtoken',
      sms_service_provider: 'TELE',
    };

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toBeDefined();
    if (errors[0].constraints) {
      expect(errors[0].constraints).toHaveProperty('matches');
      expect(errors[0].constraints.matches).toContain('2519XXXXXXXX or 2517XXXXXXXX');
    }
  });
});
