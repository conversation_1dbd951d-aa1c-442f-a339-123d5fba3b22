import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsBoolean, IsOptional, IsNotEmpty, IsNumber } from 'class-validator';

export type ApiResponse = Record<string, any>;
export interface SmsRequest {
  msg: string;
  phone: string;
  usr: string;
  pwd: string;
  from: string;
  type: string;
  async: boolean;
  sms_log_id?: string;
  requestId: string;
}

export class SendSmsDto {
  @ApiProperty({ description: 'Authentication token' })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({ description: 'Phone number to send the SMS to' })
  @IsString()
  @IsNotEmpty()
  phone: string;

  @ApiProperty({ description: 'SMS message content' })
  @IsString()
  @IsNotEmpty()
  msg: string;

  @ApiPropertyOptional({ description: 'Username', default: 'bas' })
  @IsString()
  @IsOptional()
  usr?: string;

  @ApiPropertyOptional({ description: 'Password', default: 'toor' })
  @IsString()
  @IsOptional()
  pwd?: string;

  @ApiPropertyOptional({ description: 'Sender ID', default: '9481' })
  @IsString()
  @IsOptional()
  from?: string;

  @ApiPropertyOptional({
    description: 'Whether to process asynchronously',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  async?: boolean;

  @ApiPropertyOptional({
    description: 'SMS log ID (required if async is true)',
  })
  @IsString()
  @IsOptional()
  sms_log_id?: string;

  @ApiPropertyOptional({
    description: 'Whether to use reverse channel',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  reverse?: boolean;

  @ApiPropertyOptional({
    description: 'Whether to disable retry',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  noRetry?: boolean;

  @ApiProperty({
    description: 'Unique request ID for tracking',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: false,
  })
  @IsString()
  @IsOptional()
  requestId?: string;
}

export class SendBulkDto {
  @ApiProperty({ description: 'Authentication token' })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({ description: 'Bulk ID' })
  @IsString()
  @IsNotEmpty()
  bid: string;

  @ApiPropertyOptional({ description: 'Username', default: 'bas' })
  @IsString()
  @IsOptional()
  usr?: string;

  @ApiPropertyOptional({ description: 'Password', default: 'toor' })
  @IsString()
  @IsOptional()
  pwd?: string;

  @ApiPropertyOptional({ description: 'Sender ID', default: '9481' })
  @IsString()
  @IsOptional()
  from?: string;

  @ApiPropertyOptional({
    description: 'Whether to process asynchronously',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  async?: boolean;

  @ApiPropertyOptional({
    description: 'Whether this is a test',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  isTest?: boolean;

  @ApiPropertyOptional({
    description: 'Whether to use reverse channel',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  reverse?: boolean;
}

export class SendBulkLiteDto {
  @ApiProperty({ description: 'Authentication token' })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({ description: 'Content ID' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiPropertyOptional({ description: 'Username', default: 'bas' })
  @IsString()
  @IsOptional()
  usr?: string;

  @ApiPropertyOptional({ description: 'Password', default: 'toor' })
  @IsString()
  @IsOptional()
  pwd?: string;

  @ApiPropertyOptional({ description: 'Sender ID', default: '9481' })
  @IsString()
  @IsOptional()
  from?: string;

  @ApiPropertyOptional({
    description: 'Whether to use reverse channel',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  reverse?: boolean;
}

export class SendConsoleBulkDto {
  @ApiProperty({ description: 'Authentication token' })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({ description: 'Group ID' })
  @IsString()
  @IsNotEmpty()
  group_id: string;

  @ApiProperty({ description: 'SMS message content' })
  @IsString()
  @IsNotEmpty()
  msg: string;

  @ApiProperty({ description: 'Job name' })
  @IsString()
  @IsNotEmpty()
  job_name: string;

  @ApiProperty({ description: 'Billing ID' })
  @IsString()
  @IsNotEmpty()
  billing_id: string;

  @ApiProperty({ description: 'SMS body ID' })
  @IsString()
  @IsNotEmpty()
  sms_body_id: string;

  @ApiProperty({ description: 'User ID' })
  @IsString()
  @IsNotEmpty()
  user_id: string;

  @ApiPropertyOptional({ description: 'Username', default: 'bas' })
  @IsString()
  @IsOptional()
  usr?: string;

  @ApiPropertyOptional({ description: 'Password', default: 'toor' })
  @IsString()
  @IsOptional()
  pwd?: string;

  @ApiPropertyOptional({ description: 'Sender ID', default: '9481' })
  @IsString()
  @IsOptional()
  from?: string;

  @ApiPropertyOptional({ description: 'SDP flag', default: false })
  @IsBoolean()
  @IsOptional()
  sdp?: boolean;

  @ApiPropertyOptional({
    description: 'Whether to use reverse channel',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  reverse?: boolean;

  @IsNumber()
  @IsOptional()
  progress?: number;
}

export class SendConsoleApiBulkDto {
  @ApiProperty({ description: 'Authentication token' })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({ description: 'Bulk API ID' })
  @IsString()
  @IsNotEmpty()
  bulk_api_id: string;

  @ApiProperty({ description: 'Job ID' })
  @IsString()
  @IsNotEmpty()
  jobid: string;

  @ApiPropertyOptional({ description: 'SMS message content' })
  @IsString()
  @IsOptional()
  msg?: string;

  @ApiPropertyOptional({ description: 'Username', default: 'bas' })
  @IsString()
  @IsOptional()
  usr?: string;

  @ApiPropertyOptional({ description: 'Password', default: 'toor' })
  @IsString()
  @IsOptional()
  pwd?: string;

  @ApiPropertyOptional({ description: 'Sender ID', default: '9481' })
  @IsString()
  @IsOptional()
  from?: string;

  @ApiPropertyOptional({
    description: 'Whether to use reverse channel',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  reverse?: boolean;

  @ApiPropertyOptional({
    description: 'Whether to process asynchronously',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  async?: boolean;
}

export class RetryBulkDto {
  @ApiProperty({ description: 'Authentication token' })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({ description: 'Bulk ID' })
  @IsString()
  @IsNotEmpty()
  bid: string;

  @ApiPropertyOptional({
    description: 'Whether to use reverse channel',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  reverse?: boolean;
}

export class CancelBulkDto {
  @ApiProperty({ description: 'Authentication token' })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({ description: 'Bulk ID' })
  @IsString()
  @IsNotEmpty()
  bid: string;

  @ApiPropertyOptional({
    description: 'Whether to use reverse channel',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  reverse?: boolean;
}

export class CheckActiveDto {
  @ApiProperty({ description: 'Authentication token' })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({ description: 'Content ID' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiPropertyOptional({ description: 'Username', default: 'bas' })
  @IsString()
  @IsOptional()
  usr?: string;

  @ApiPropertyOptional({ description: 'Password', default: 'toor' })
  @IsString()
  @IsOptional()
  pwd?: string;

  @ApiPropertyOptional({ description: 'Sender ID', default: '9481' })
  @IsString()
  @IsOptional()
  from?: string;

  @ApiPropertyOptional({
    description: 'Whether to use reverse channel',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  reverse?: boolean;
}
