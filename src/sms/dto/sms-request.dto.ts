// sms/dto/send-sms.dto.ts
// This file contains the DTOs for sending SMS messages.

import {
  IsString,
  IsNotEmpty,
  IsArray,
  ArrayNotEmpty,
  ValidateNested,
  IsOptional,
  IsIn,
  IsNumber,
  IsObject,
  Matches,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum } from 'class-validator';

export enum SmsType {
  SINGLE = 'SINGLE',
  BULK = 'BULK',
}

export enum BulkSmsType {
  DASHBOARD = 'DASHBOARD',
  API = 'API',
}

export enum SmsServiceProvider {
  TELE = 'TELE',
  SAFARICOM = 'SAFARICOM',
}

export class SmsRequiredDto {
  @ApiProperty({
    description: 'Type of SMS service provider',
    example: 'TELE',
    enum: [SmsServiceProvider.TELE, SmsServiceProvider.SAFARICOM],
  })
  @IsEnum(SmsServiceProvider)
  @IsOptional()
  sms_service_provider?: string;

  @ApiProperty({
    description: 'Username for authentication',
    example: 'apiuser',
  })
  @IsString()
  @IsNotEmpty()
  usr: string;

  @ApiProperty({
    description: 'Password for authentication',
    example: 'apipassword',
  })
  @IsString()
  @IsNotEmpty()
  pwd: string;

  @ApiProperty({
    description: 'Sender ID or from number',
    example: 'COMPANY',
  })
  @IsString()
  @IsNotEmpty()
  from: string;

  @ApiProperty({
    description: 'SDP (Service Delivery Platform)',
    example: 'platform1',
    required: false,
  })
  @IsString()
  @IsOptional()
  sdp?: boolean;

  @ApiProperty({
    description: 'Authentication token',
    example: 'abc123xyz',
  })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({
    description: 'Reverse flag',
    example: false,
    default: false,
  })
  @IsOptional()
  reverse?: boolean;

  @ApiProperty({
    description: 'Async flag',
    example: true,
    default: true,
  })
  @IsOptional()
  async?: boolean;

  @ApiProperty({
    description: 'Unique parameter to distinguish SMS messages',
    example: 'unique-id-123',
    required: false,
  })
  @IsString()
  @IsOptional()
  uniqueParam?: string;
}

export class SingleSmsDto {
  @ApiProperty({
    description: 'Object of SMS credentials',
    type: [SmsRequiredDto],
    example: [
      {
        msg: 'Your verification code is 123456',
        uniqueParam: 'unique-id-123',
      },
      {
        msg: 'Your order #12345 has been shipped',
        uniqueParam: 'unique-id-456',
      },
    ],
  })
  @IsObject()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => SmsRequiredDto)
  sms_credential: SmsRequiredDto;

  @ApiProperty({
    description: 'SMS message content',
    example: 'Your verification code is 123456',
  })
  @IsString()
  @IsNotEmpty()
  message: string;

  @ApiProperty({
    description: 'Phone number to send the SMS to (must be in format 2519XXXXXXXX or 2517XXXXXXXX)',
    example: '************',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^251(9|7)[0-9]{8}$/, {
    message:
      'Phone number must be in the format 2519XXXXXXXX or 2517XXXXXXXX (e.g., ************ or ************)',
  })
  to: string;

  @ApiProperty({
    description: 'Worker category for routing to specific workers',
    example: 'single',
    required: false,
    enum: ['single', 'bulk'],
  })
  @IsString()
  @IsOptional()
  @IsIn(['single', 'bulk'])
  workerCategory: 'single' | 'bulk';

  @ApiProperty({
    description: 'Numeric value for dynamic worker assignment (used with modulo operation)',
    example: 12345,
    required: false,
    type: Number,
  })
  @IsNumber()
  @IsOptional()
  workerAssignmentValue?: number;

  @ApiProperty({
    description: 'SMS log ID',
    example: 12345,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  sms_log_id: number | string;

  @ApiProperty({
    description: 'Unique request ID for tracking',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsString()
  @IsOptional()
  requestId?: string;

  @ApiProperty({
    description: 'Unique parameter to distinguish SMS messages',
    example: 'unique-id-123',
    required: false,
  })
  @IsString()
  @IsOptional()
  uniqueParam?: string;
}

export class BulkSmsDto {
  @ApiProperty({
    description: 'Object of SMS credentials',
    type: [SmsRequiredDto],
    example: [
      {
        msg: 'Your verification code is 123456',
        uniqueParam: 'unique-id-123',
      },
      {
        msg: 'Your order #12345 has been shipped',
        uniqueParam: 'unique-id-456',
      },
    ],
  })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => SmsRequiredDto)
  sms_credential: SmsRequiredDto;

  @ApiProperty({
    description: 'User ID',
    example: 12345,
  })
  @IsNumber()
  @IsNotEmpty()
  user_id: number;

  @ApiProperty({
    description: 'Billing ID',
    example: 67890,
  })
  @IsNumber()
  @IsNotEmpty()
  billing_id: number;

  @ApiProperty({
    description: 'Job name',
    example: 'Marketing Campaign',
  })
  @IsString()
  @IsNotEmpty()
  job_name: string;

  @ApiProperty({
    description: 'Job unique ID',
    example: 'asdf-a42f',
  })
  @IsString()
  @IsNotEmpty()
  job_unique_id: string;

  @ApiProperty({
    description: 'SMS body ID',
    example: 456,
  })
  @IsNumber()
  @IsNotEmpty()
  sms_body_id: number;

  @ApiProperty({
    description: 'Type of bulk SMS',
    example: 'DASHBOARD',
    enum: [BulkSmsType.DASHBOARD, BulkSmsType.API],
  })
  @IsEnum(BulkSmsType)
  @IsNotEmpty()
  bulk_type: BulkSmsType;

  @ApiProperty({
    description: 'Receivers (contact_group_id for DASHBOARD, bulk_job_id for API)',
    example: 'contact_group_id or bulk_job_id',
  })
  @IsString()
  @IsNotEmpty()
  receivers: string;

  @ApiProperty({
    description: 'SMS message content',
    example: 'This is a bulk message for all recipients',
  })
  @IsString()
  @IsNotEmpty()
  message: string;

  @ApiProperty({
    description: 'Unique request ID for tracking',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsString()
  @IsOptional()
  requestId?: string;

  @ApiProperty({
    description: 'Worker category for routing to specific workers',
    example: 'bulk',
    required: false,
    enum: ['single', 'bulk'],
  })
  @IsString()
  @IsOptional()
  @IsIn(['single', 'bulk'])
  workerCategory: 'single' | 'bulk';

  @ApiProperty({
    description: 'Numeric value for dynamic worker assignment (used with modulo operation)',
    example: 12345,
    required: false,
    type: Number,
  })
  @IsNumber()
  @IsOptional()
  workerAssignmentValue?: number;
}

export class ExtraDto {
  @ApiProperty({ description: 'The bid associated with the message.', example: '12345' })
  @IsString()
  @IsNotEmpty()
  bid: string;

  @ApiProperty({ description: 'The recipient of the message.', example: '************' })
  @IsString()
  @IsNotEmpty()
  to: string;

  @ApiProperty({ description: 'The ID of the SMS body.', example: 'sms_body_abc' })
  @IsString()
  @IsOptional()
  sms_body_id?: string;

  @ApiProperty({
    description: 'Object of SMS credentials',
    type: [SmsRequiredDto],
    example: [
      {
        msg: 'Your verification code is 123456',
        uniqueParam: 'unique-id-123',
      },
      {
        msg: 'Your order #12345 has been shipped',
        uniqueParam: 'unique-id-456',
      },
    ],
  })
  @IsObject()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => SmsRequiredDto)
  sms_credential: SmsRequiredDto;

  // @ApiProperty({ description: 'The ID of the job.', example: 'job_xyz' })
  // @IsString()
  // @IsOptional()
  // jobId?: string;

  // @ApiProperty({ description: 'The ID of the message group.', example: 'group_123' })
  // @IsString()
  // @IsNotEmpty()
  // group_id: string;

  @ApiProperty({ description: 'The message content.', example: 'Hello, this is a test message.' })
  @IsString()
  @IsNotEmpty()
  msg: string;

  @ApiProperty({ description: 'The billing ID.', example: 'billing_def' })
  @IsString()
  @IsNotEmpty()
  billing_id: string;

  @ApiProperty({ description: 'The ID of the user who sent the message.', example: 'user_789' })
  @IsString()
  @IsNotEmpty()
  user_id: string;
}

export class DLRSmsDto {
  @ApiProperty({
    description: 'Object of SMS credentials',
    type: [ExtraDto],
    example: [
      {
        msg: 'Your verification code is 123456',
        uniqueParam: 'unique-id-123',
      },
      {
        msg: 'Your order #12345 has been shipped',
        uniqueParam: 'unique-id-456',
      },
    ],
  })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => ExtraDto)
  query: ExtraDto;

  @ApiProperty({ description: 'The delivery status of the message.', example: 'DELIVERED' })
  @IsString()
  @IsNotEmpty()
  status: string;

  @ApiProperty({
    description: 'Worker category for routing to specific workers',
    example: 'dlr',
    required: false,
    enum: ['dlr'],
  })
  @IsString()
  @IsOptional()
  @IsIn(['dlr'])
  workerCategory: 'dlr';

  @ApiProperty({
    description: 'The original message ID from the SMS gateway.',
    example: 'msg_id_12345',
  })
  @IsString()
  @IsNotEmpty()
  originalMessageId: string;
}

export class BulkSmsRequestDto {
  message: string;
  numbers: string[];
  sms_credential: {
    usr: string;
    pwd: string;
    from: string;
    token: string;
  };
  pageSize?: number; // Optional, default 100
  concurrency?: number; // Optional, default 4
}

export class BulkSmsResponseDto {
  jobId: string;
  total: number;
  pageSize: number;
  totalPages: number;
  concurrency: number;
  status: 'queued' | 'processing' | 'completed' | 'failed';
}
