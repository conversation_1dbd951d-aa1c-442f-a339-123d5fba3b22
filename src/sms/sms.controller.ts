// sms/sms.controller.ts

import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Post,
  Logger,
  BadRequestException,
  HttpException,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import {
  BulkSmsDto,
  SingleSmsDto,
  BulkSmsType,
  SmsServiceProvider,
  DLRSmsDto,
} from './dto/sms-request.dto';
import { SmsService } from './sms.service';
import { v4 as uuidv4 } from 'uuid';
import { phoneType } from '../shared/utils';
import {
  CancelBulkDto,
  CheckActiveDto,
  RetryBulkDto,
  SendConsoleApiBulkDto,
  SendConsoleBulkDto,
  SendSmsDto,
  SmsRequest,
} from './dto/sms-request-v1.dto';
import { ConfigService } from '@nestjs/config';

@ApiTags('SMS')
@Controller('sms')
export class SmsController {
  private readonly logger = new Logger(SmsController.name);
  private singleWorkersCount: number = 1;
  private bulkWorkersCount: number = 1;

  constructor(
    private readonly smsService: SmsService,
    private configService: ConfigService,
  ) {
    this.singleWorkersCount = this.configService.get<number>('sms.workers.single_count') || 1;
    this.bulkWorkersCount = this.configService.get<number>('sms.workers.bulk_count') || 1;
  }

  @Post('send/single')
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiOperation({ summary: 'Send a single SMS message' })
  @ApiResponse({ status: HttpStatus.ACCEPTED, description: 'SMS has been queued for delivery' })
  sendSingle(@Body() dto: SingleSmsDto) {
    const smsServiceProvider = phoneType(dto.to);
    if (!smsServiceProvider) {
      return {
        statusCode: 400,
        message: 'The phone number is invalid',
        requestId: dto.requestId,
      };
    }

    // Add phone number service provider
    dto.sms_credential.sms_service_provider = smsServiceProvider;

    // Add optional fields if they were provided
    if (dto.requestId) {
      dto.requestId = dto.requestId;
    } else {
      dto.requestId = uuidv4();
    }

    if (dto.uniqueParam) {
      dto.uniqueParam = dto.uniqueParam;
    } else {
      dto.uniqueParam = `unique-${uuidv4()}`;
    }

    if (dto.workerCategory) {
      dto.workerCategory = dto.workerCategory;
    } else {
      dto.workerCategory = 'single';
    }

    if (dto.workerAssignmentValue !== undefined) {
      dto.workerAssignmentValue = dto.workerAssignmentValue;
    } else {
      // Extract numeric part of the phone number and use it as assignment value
      // const numericPart = dto.to.replace(/\D/g, '');
      // dto.workerAssignmentValue = numericPart ? parseInt(numericPart, 10) : Date.now();

      // Generate a random number
      // ensuring a uniform distribution across all workers.
      const selectedWorkerIndex = Math.floor(Math.random() * this.singleWorkersCount);
      dto.workerAssignmentValue = selectedWorkerIndex;
    }

    this.logger.log(
      `[${dto.requestId}] Received SMS request to ${dto.to} with category ${dto.workerCategory} and assignment value ${dto.workerAssignmentValue}`,
    );
    return this.smsService.sendSingle(dto);
  }

  @Post('send/bulk')
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiOperation({
    summary: 'Send bulk SMS messages (supports both traditional and unified formats)',
  })
  @ApiResponse({
    status: HttpStatus.ACCEPTED,
    description: 'SMS messages have been queued for delivery',
  })
  sendBulk(@Body() dto: any) {
    // Validation: require at least numbers or receivers
    if (!dto.numbers && !dto.receivers) {
      throw new BadRequestException("'numbers' or 'receivers' field is required for bulk SMS");
    }

    // Check if this is a unified bulk SMS request (has bulk_type field)
    if (dto.bulk_type) {
      // Handle as a unified bulk SMS
      const bulkSmsData: Record<string, any> = {
        sms_credential: {
          usr: dto.sms_credential?.usr,
          pwd: dto.sms_credential?.pwd,
          from: dto.sms_credential?.from,
          token: dto.sms_credential?.token,
          reverse: dto.sms_credential?.reverse || false,
          sdp: dto.sms_credential?.sdp,
        },
        user_id: dto.user_id,
        billing_id: dto.billing_id,
        job_name: dto.job_name,
        job_unique_id: dto.job_unique_id,
        sms_body_id: dto.sms_body_id,
        bulk_type: dto.bulk_type,
        receivers: dto.receivers,
        message: dto.message,
      };

      // Add optional fields if they were provided
      if (dto.requestId) {
        bulkSmsData.requestId = dto.requestId;
      } else {
        bulkSmsData.requestId = uuidv4();
      }

      if (dto.workerCategory) {
        bulkSmsData.workerCategory = dto.workerCategory;
      } else {
        bulkSmsData.workerCategory = 'bulk';
      }

      if (dto.workerAssignmentValue !== undefined) {
        bulkSmsData.workerAssignmentValue = dto.workerAssignmentValue;
      } else {
        bulkSmsData.workerAssignmentValue = Math.floor(Math.random() * this.bulkWorkersCount);
      }

      // Log based on the bulk type
      if (dto.bulk_type === BulkSmsType.DASHBOARD) {
        this.logger.log(
          `[${bulkSmsData.requestId}] Received DASHBOARD bulk SMS request for job ${dto.job_name} with contact group ${dto.receivers}`,
        );
      } else if (dto.bulk_type === BulkSmsType.API) {
        this.logger.log(
          `[${bulkSmsData.requestId}] Received API bulk SMS request for job ${dto.job_name} with bulk job ID ${dto.receivers}`,
        );
      }

      // Send to the bulk service method
      return this.smsService.sendUnifiedBulk(bulkSmsData as BulkSmsDto);
    } else {
      // Handle as a traditional bulk SMS with multiple messages
      const bulkSmsData: Record<string, any> = {
        sms_credential: {
          usr: dto.sms_credential?.usr,
          pwd: dto.sms_credential?.pwd,
          from: dto.sms_credential?.from,
          token: dto.sms_credential?.token,
          reverse: dto.sms_credential?.reverse || false,
          sdp: dto.sms_credential?.sdp,
        },
        user_id: dto.user_id,
        billing_id: dto.billing_id,
        job_name: dto.job_name,
        receivers: dto.receivers || dto.group_id,
        message: dto.message || dto.msg,
        sms_body_id: dto.sms_body_id,
        bulk_type: 'TRADITIONAL',
      };

      // Add optional fields if they were provided
      if (dto.requestId) {
        bulkSmsData.requestId = dto.requestId;
      } else {
        bulkSmsData.requestId = uuidv4();
      }

      if (dto.workerCategory) {
        bulkSmsData.workerCategory = dto.workerCategory;
      } else {
        bulkSmsData.workerCategory = 'bulk';
      }

      if (dto.workerAssignmentValue !== undefined) {
        bulkSmsData.workerAssignmentValue = dto.workerAssignmentValue;
      } else {
        bulkSmsData.workerAssignmentValue = Date.now();
      }

      this.logger.log(
        `[${bulkSmsData.requestId}] Received traditional bulk SMS request for job ${dto.job_name}`,
      );
      return this.smsService.sendBulk(bulkSmsData as BulkSmsDto);
    }
  }

  @Post('send')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Send a single SMS message' })
  @ApiBody({ type: SendSmsDto })
  @ApiResponse({ status: 200, description: 'SMS sent successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async sendSmsv1(@Body() body: SendSmsDto): Promise<Record<string, any> | void> {
    // For async requests, require sms_log_id
    if (body.async && !body.sms_log_id) {
      throw new BadRequestException("'sms_log_id' argument is required if async is true");
    }

    try {
      let dto: SingleSmsDto = {
        sms_credential: {
          sms_service_provider: SmsServiceProvider.TELE,
          // Set default values if not provided
          usr: body.usr || 'bas',
          pwd: body.pwd || 'toor',
          from: body.from || '9481',
          token: '',
          sdp: false,
          async: body.async ?? false,
        },
        message: body.msg,
        to: body.phone,
        workerCategory: 'single',
        workerAssignmentValue: 0,
        sms_log_id: body.sms_log_id ?? '',
        requestId: uuidv4(),
        uniqueParam: '',
      };
      const smsServiceProvider = phoneType(dto.to);
      if (!smsServiceProvider) {
        return {
          statusCode: 400,
          message: 'The phone number is invalid',
          requestId: dto.requestId,
        };
      }

      // Add phone number service provider
      dto.sms_credential.sms_service_provider = smsServiceProvider;

      dto.uniqueParam = `unique-${uuidv4()}`;
      dto.workerCategory = 'single';
      // // Extract numeric part of the phone number and use it as assignment value
      // const numericPart = dto.to.replace(/\D/g, '');
      // dto.workerAssignmentValue = numericPart ? parseInt(numericPart, 10) : Date.now();

      // Generate a random number
      // ensuring a uniform distribution across all workers.
      dto.workerAssignmentValue = Math.floor(Math.random() * this.singleWorkersCount);

      this.logger.log(
        `[${dto.requestId}] Received SMS request to ${dto.to} with category ${dto.workerCategory} and assignment value ${dto.workerAssignmentValue}`,
      );
      return this.smsService.sendSingle(dto);
    } catch (error: unknown) {
      console.error('Error in sendSms controller:', error);
      const errorMessage =
        error instanceof Error && typeof (error as any).response?.data === 'string'
          ? (error as any).response.data
          : 'Failed to send SMS';
      throw new HttpException(
        {
          error: true,
          errorMessage,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('sendbulk/console')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Send console bulk SMS messages' })
  @ApiBody({ type: SendConsoleBulkDto })
  @ApiResponse({
    status: 200,
    description: 'Console bulk SMS sent successfully',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async sendConsoleBulk(@Body() body: SendConsoleBulkDto) {
    try {
      // Validate required fields
      if (!body.group_id) {
        return {
          error: true,
          msg: "'group_id' argument is required",
        };
      }

      if (!body.msg) {
        return {
          error: true,
          msg: "'msg' argument is required",
        };
      }

      if (!body.job_name) {
        return {
          error: true,
          msg: "'job_name' argument is required",
        };
      }

      if (!body.billing_id) {
        return {
          error: true,
          msg: "'billing_id' argument is required",
        };
      }

      if (!body.sms_body_id) {
        return {
          error: true,
          msg: "'sms_body_id' argument is required",
        };
      }

      if (!body.user_id) {
        return {
          error: true,
          msg: "'user_id' argument is required",
        };
      }

      // Set default values
      const usr = body.usr || 'bas';
      const pwd = body.pwd || 'toor';
      const from = body.from || '9481';
      const progress = body.progress ?? 0; // Default progress

      // Prepare request object
      const requestID = uuidv4();

      // Worker assignment
      const bulkWorkerStartIndex = this.singleWorkersCount;
      const randomIndexForBulkGroup = Math.floor(Math.random() * this.bulkWorkersCount); // This will be 0 or 1
      // The final assignment value is the start index plus the random index within the bulk group
      const workerAssignmentValue = bulkWorkerStartIndex + randomIndexForBulkGroup;

      // Payload for bulk
      const payload: BulkSmsDto = {
        sms_credential: {
          usr: usr,
          pwd: pwd,
          from: from,
          sdp: body.sdp,
          token: body.token,
          reverse: body.reverse,
        },
        user_id: parseInt(body.user_id),
        billing_id: parseInt(body.billing_id),
        job_name: body.job_name,
        job_unique_id: body.sms_body_id,
        sms_body_id: parseInt(body.sms_body_id),
        bulk_type: BulkSmsType.DASHBOARD,
        receivers: body.group_id,
        message: body.msg,
        requestId: requestID,
        workerCategory: 'bulk',
        workerAssignmentValue,
      };

      // Send console bulk SMS
      return await this.smsService.sendBulk(payload);
    } catch (error: any) {
      console.error('Error in sending dashboard bulk controller:', error);
      const errorMessage = error.response?.data || 'Failed to send console bulk SMS';
      throw new HttpException(errorMessage, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('sendbulk/console/api')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Send console API bulk SMS messages' })
  @ApiBody({ type: SendConsoleApiBulkDto })
  @ApiResponse({
    status: 200,
    description: 'Console API bulk SMS sent successfully',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async sendConsoleAPIBulk(@Body() body: SendConsoleApiBulkDto) {
    try {
      // Set default values
      const usr = body.usr || 'bas';
      const pwd = body.pwd || 'toor';
      const from = body.from || '9481';

      // Prepare request object
      const requestID = uuidv4();
      const request = {
        bulk_api_id: body.bulk_api_id,
        msg: body.msg,
        type: 'api-bulk',
        usr,
        pwd,
        from,
        jobid: body.jobid,
        requestID,
        async: body.async,
      };

      // Send console API bulk SMS
      // return await this.smsService.sendConsoleApiBulk(request);
    } catch (error: any) {
      console.error('Error in sendConsoleAPIBulk controller:', error);
      const errorMessage = error.response?.data || 'Failed to send console API bulk SMS';
      throw new HttpException(errorMessage, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('retrybulk')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Retry bulk SMS messages' })
  @ApiBody({ type: RetryBulkDto })
  @ApiResponse({
    status: 200,
    description: 'Bulk SMS retry initiated successfully',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async retryBulk(@Body() body: RetryBulkDto) {
    try {
      // Prepare request object
      const requestID = uuidv4();
      const request = {
        type: 'retrybulk',
        bid: body.bid,
        requestID,
      };

      // Retry bulk SMS
      return await this.smsService.retryBulk(request);
    } catch (error: any) {
      console.error('Error in retryBulk controller:', error);
      const errorMessage = error.response?.data || 'Failed to retry bulk SMS';
      throw new HttpException(errorMessage, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('cancelbulk')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Cancel bulk SMS messages' })
  @ApiBody({ type: CancelBulkDto })
  @ApiResponse({
    status: 200,
    description: 'Bulk SMS cancellation initiated successfully',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async cancelBulk(@Body() body: CancelBulkDto) {
    try {
      // Prepare request object
      const requestID = uuidv4();
      const request = {
        type: 'cancelbulk',
        bid: body.bid,
        requestID,
      };

      // Cancel bulk SMS
      return await this.smsService.cancelBulk(request);
    } catch (error: any) {
      console.error('Error in cancelBulk controller:', error);
      const errorMessage = error.response?.data || 'Failed to cancel bulk SMS';
      throw new HttpException(errorMessage, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('check/active')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Check active status' })
  @ApiBody({ type: CheckActiveDto })
  @ApiResponse({
    status: 200,
    description: 'Active status checked successfully',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async checkActive(@Body() body: CheckActiveDto) {
    try {
      // Set default values
      const usr = body.usr || 'bas';
      const pwd = body.pwd || 'toor';
      const from = body.from || '9481';

      // Prepare request object
      const requestID = uuidv4();
      const request = {
        id: body.id,
        type: 'lbulk',
        usr,
        pwd,
        from,
        requestID,
      };

      // Check active
      return await this.smsService.checkActive(request);
    } catch (error: any) {
      console.error('Error in checkActive controller:', error);
      const errorMessage = error.response?.data || 'Failed to check active';
      throw new HttpException(errorMessage, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('send/success/:sid')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Check active status' })
  @ApiBody({ type: CheckActiveDto })
  @ApiResponse({
    status: 200,
    description: 'Active status checked successfully',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async successDlrReceiver(@Body() body: any) {
    this.logger.log(`[SUCCESS_DLR] received success dlr: ${JSON.stringify(body)}`);
    try {
      const dlr: DLRSmsDto = {
        query: body.query,
        status: body.status,
        originalMessageId: body.originalMessageId,
        workerCategory: 'dlr'
      };
      return await this.smsService.successfulDLR(dlr);
    } catch (error: any) {
      this.logger.log(`[SUCCESS_DLR] Error in sending success dlr: ${error}`);
      const errorMessage = error.response?.data || 'Failed to cancel bulk SMS';
      throw new HttpException(
        {
          error: true,
          errorMessage,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
