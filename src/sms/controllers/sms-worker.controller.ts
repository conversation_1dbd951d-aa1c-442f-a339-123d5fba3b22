import { <PERSON>, Logger } from '@nestjs/common';
import { EventPattern } from '@nestjs/microservices';
import { SingleSmsDto, BulkSmsDto } from '../dto/sms-request.dto';
import { SmsProviderService } from '../services/sms-provider.service';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

// Define interface for SMS processing result
interface SmsResult {
  messageId: string;
  processingTime?: number;
  messageHash?: number;
  assignmentValue?: number;
  workerAssignment?: number;
  workerId?: string;
  workerCategory?: string;
}

@Controller()
export class SmsWorkerController {
  private readonly logger = new Logger(SmsWorkerController.name);

  constructor(
    private readonly smsProvider: SmsProviderService,
    private configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}

  @EventPattern('single')
  async handleSingleSms(data: SingleSmsDto) {
    const requestId = data.requestId || 'unknown';
    this.logger.log(`[${requestId}] Processing single SMS to ${data.to}`);
    try {
      const result = (await this.smsProvider.sendSingleSms(data)) as SmsResult;
      this.logger.log(
        `[${requestId}] Successfully processed SMS to ${data.to} with messageId: ${result.messageId}`,
      );
      this.logger.debug(
        `[${requestId}] Processing time: ${result.processingTime}ms, Hash: ${result.messageHash}, Worker: ${result.workerId || 'unknown'}, Category: ${result.workerCategory || 'default'}`,
      );
    } catch (error) {
      this.logger.error(`[${requestId}] Failed to process single SMS to ${data.to}`, error.stack);
    }
  }

  @EventPattern('bulk')
  async handleBulkSms(data: BulkSmsDto) {
    const requestId = data.requestId || 'unknown';
    this.logger.log(`[${requestId}] Processing bulk SMS of ${data.receivers}`);

    let successCount = 0;
    let failureCount = 0;

    try {
      const url = this.configService.get<string>('backendhost.url') || 'none'; // Replace with your backend URL
      const response = await firstValueFrom(this.httpService.get(url));

      // const result = (await this.smsProvider.sendSingleSms(msg)) as SmsResult;
      // this.logger.log(
      //   `[${requestId}] Successfully processed bulk SMS item to ${msg.to} with messageId: ${result.messageId}`,
      // );
      successCount++;
    } catch (error) {
      this.logger.error(
        `[${requestId}] Failed to process bulk SMS item to ${data.receivers}`,
        error.stack,
      );
      failureCount++;
    }

    this.logger.log(
      `[${requestId}] Completed processing bulk SMS batch. Success: ${successCount}, Failures: ${failureCount}`,
    );
  }
}
