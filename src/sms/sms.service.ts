// sms/sms.service.ts

import { HttpException, HttpStatus, Inject, Injectable, Logger } from '@nestjs/common';
import { ClientProxy, ClientProxyFactory, Transport } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';
import {
  BulkSmsDto,
  BulkSmsType,
  SingleSmsDto,
  BulkSmsRequestDto,
  BulkSmsResponseDto,
  DLRSmsDto,
} from './dto/sms-request.dto';
import { SmsRequest } from './dto/sms-request-v1.dto';
import axios from 'axios';
import { ConfigService } from '@nestjs/config';
import { v4 as uuidv4 } from 'uuid';
import { SmsProducer } from '../queue/producers/sms.producer';

@Injectable()
export class SmsService {
  private singleClient: ClientProxy;
  private bulkClient: ClientProxy;
  private failedClient: ClientProxy;
  private dlrClient: ClientProxy;
  private readonly logger = new Logger(SmsService.name);
  private rabbitMqUrl: string;

  constructor(
    @Inject('SMS_SINGLE_SERVICE') singleClient: ClientProxy,
    @Inject('SMS_BULK_SERVICE') bulkClient: ClientProxy,
    @Inject('SMS_DLR_SERVICE') dlrClient: ClientProxy,
    private configService: ConfigService,
    private readonly smsProducer: SmsProducer,
  ) {
    this.singleClient = singleClient;
    this.bulkClient = bulkClient;
    this.dlrClient = dlrClient;
    this.rabbitMqUrl = this.configService.get<string>('rabbitmq.url') || 'amqp://localhost:5672';
  }

  private reinitSingleClient() {
    this.logger.warn('Reinitializing SMS_SINGLE_SERVICE RabbitMQ client...');
    const singleQueue = this.configService.get<string>('sms.queues.single') || 'sms_single_queue';
    this.singleClient = ClientProxyFactory.create({
      transport: Transport.RMQ,
      options: {
        urls: [this.rabbitMqUrl], // Use your actual connection string
        queue: singleQueue,
        queueOptions: { durable: true },
      },
    });
  }

  private reinitBulkClient() {
    this.logger.warn('Reinitializing SMS_BULK_SERVICE RabbitMQ client...');
    const bulkQueue = this.configService.get<string>('sms.queues.bulk') || 'sms_bulk_queue';
    this.bulkClient = ClientProxyFactory.create({
      transport: Transport.RMQ,
      options: {
        urls: [this.rabbitMqUrl], // Use your actual connection string
        queue: bulkQueue,
        queueOptions: { durable: true },
      },
    });
  }

  private reinitDlrClient() {
    this.logger.warn('Reinitializing SMS_DLR_SERVICE RabbitMQ client...');
    const dlrQueue = this.configService.get<string>('sms.queues.dlr') || 'sms_dlr_queue';
    this.dlrClient = ClientProxyFactory.create({
      transport: Transport.RMQ,
      options: {
        urls: [this.rabbitMqUrl], // Use your actual connection string
        queue: dlrQueue,
        queueOptions: { durable: true },
      },
    });
  }

  // Sanitize request for logging
  private sanitizeRequest(request: any): any {
    if (!request) return {};

    const sanitized = { ...request };
    if (sanitized.pwd) {
      sanitized.pwd = '***REDACTED***';
    }

    return sanitized;
  }

  async sendSingle(dto: SingleSmsDto) {
    try {
      // send to the single queue
      await firstValueFrom(this.singleClient.emit('single', dto));
      return {
        statusCode: 202,
        error: false,
        // message: 'SMS_SUCCESSFULLY_SENT',
        msg: 'SMS_SUCCESSFULLY_SENT',
        requestId: dto.requestId,
        raw: JSON.stringify({ msg: 'SMS_SUCCESSFULLY_SENT', requestId: dto.requestId }),
      };
    } catch (error: any) {
      this.logger.error('Error sending single SMS', error.stack, 'SMS', {
        requestId: dto.requestId,
        error: error.message,
      });
      if (error.message?.includes('channel closed')) {
        // You may want to re-instantiate the client or throw a specific error
        this.reinitSingleClient();
      }
      throw new HttpException('Failed to queue SMS for delivery', HttpStatus.SERVICE_UNAVAILABLE);
    }
  }

  async sendBulk(dto: BulkSmsDto) {
    try {
      // send to the bulk queue
      await firstValueFrom(this.bulkClient.emit('bulk', dto));
      return {
        statusCode: 202, // HTTP 202 Accepted
        message: `${dto.receivers} SMS messages have been queued for delivery`,
        requestId: dto.requestId,
        uniqueParams: dto.job_unique_id,
      };
    } catch (error: any) {
      this.logger.error('Error sending bulk SMS', error.stack, 'SMS', {
        requestId: dto.requestId,
        error: error.message,
      });
      if (error.message?.includes('channel closed')) {
        this.reinitBulkClient();
      }
      throw new HttpException(
        'Failed to queue bulk SMS for delivery',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  async sendUnifiedBulk(dto: BulkSmsDto) {
    // Handle different bulk types in a single method
    if (dto.bulk_type === BulkSmsType.DASHBOARD) {
      // Send to the bulk queue with dashboard type
      await firstValueFrom(this.bulkClient.emit('bulk-dashboard', dto));
      return {
        statusCode: 202, // HTTP 202 Accepted
        message: `Bulk SMS for contact group ${dto.receivers} has been queued for delivery`,
        requestId: dto.requestId,
        job_unique_id: dto.job_unique_id,
        bulk_type: dto.bulk_type,
      };
    } else if (dto.bulk_type === BulkSmsType.API) {
      // Send to the bulk queue with API type
      await firstValueFrom(this.bulkClient.emit('bulk-api', dto));
      return {
        statusCode: 202, // HTTP 202 Accepted
        message: `Bulk SMS for job ID ${dto.receivers} has been queued for delivery`,
        requestId: dto.requestId,
        job_unique_id: dto.job_unique_id,
        bulk_type: dto.bulk_type,
      };
    } else {
      throw new Error(`Unsupported bulk type: ${dto.bulk_type}`);
    }
  }

  async retryBulk(request: any) {
    try {
      this.logger.log('Retrying bulk SMS', 'SMS', {
        request: { bid: request.bid },
      });

      const response = await axios.post('http://127.0.0.1:8002/api/retrybulk', {
        bid: request.bid,
      });

      this.logger.log('Bulk SMS retry sent', 'SMS', {
        bid: request.bid,
        response: response.data,
      });

      return response.data;
    } catch (error: any) {
      this.logger.error('Error retrying bulk SMS', error.stack, 'SMS', {
        bid: request.bid,
        error: error.message,
        response: error.response?.data,
      });

      throw new HttpException(
        error.response?.data || 'Failed to retry bulk SMS',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async cancelBulk(request: any) {
    try {
      this.logger.log('Canceling bulk SMS', 'SMS', {
        request: { bid: request.bid },
      });

      const response = await axios.post('http://127.0.0.1:8002/api/cancelbulk', {
        bid: request.bid,
      });

      this.logger.log('Bulk SMS cancel sent', 'SMS', {
        bid: request.bid,
        response: response.data,
      });

      return response.data;
    } catch (error: any) {
      this.logger.error('Error canceling bulk SMS', error.stack, 'SMS', {
        bid: request.bid,
        error: error.message,
        response: error.response?.data,
      });

      throw new HttpException(
        error.response?.data || 'Failed to cancel bulk SMS',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async checkActive(request: any) {
    try {
      this.logger.log('Checking active bulk SMS', 'SMS', {
        request: this.sanitizeRequest(request),
      });

      // Get content from next service
      const lbulkcontent = await axios.get(
        `https://api.geezsms.com/api/sms/bulk/content/${request.id}`,
      );
      const lcontent = JSON.parse(lbulkcontent.data.data.content);

      // Send request to jasmine service
      const response = await axios.post('http://127.0.0.1:8002/api/sendbulklite', {
        content: lcontent,
        usr: request.usr,
        pwd: request.pwd,
        from: request.from,
      });

      this.logger.log('Active check completed', 'SMS', {
        requestId: request.id,
        response: response.data,
      });

      return response.data;
    } catch (error: any) {
      this.logger.error('Error checking active', error.stack, 'SMS', {
        requestId: request.id,
        error: error.message,
        response: error.response?.data,
      });

      throw new HttpException(
        error.response?.data || 'Failed to check active',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async successfulDLR(dto: DLRSmsDto) {
    this.logger.log('Sending success DLR', 'SMS', {
      request: { bid: dto },
    });
    try {
      // send to the bulk queue
      await firstValueFrom(this.dlrClient.emit('dlr', dto));
      return {
        statusCode: 202, // HTTP 202 Accepted
        message: `DLR have been queued for delivery`,
        requestId: dto.query.bid,
      };
    } catch (error: any) {
      this.logger.error('Error sending bulk SMS', error.stack, 'SMS', {
        requestId: dto.query.bid,
        error: error.message,
      });
      if (error.message?.includes('channel closed')) {
        this.reinitDlrClient();
      }
      throw new HttpException(
        'Failed to queue bulk SMS for delivery',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  async sendBulkSms(
    message: string,
    numbers: string[],
    sms_credential: any,
    pageSize = 100,
    concurrency = 1,
  ) {
    const jobId = uuidv4();
    const total = numbers.length;
    const pages = Math.ceil(total / pageSize);
    for (let i = 0; i < pages; i++) {
      const pageNumbers = numbers.slice(i * pageSize, (i + 1) * pageSize);
      const payload = {
        jobId,
        page: i + 1,
        totalPages: pages,
        message,
        numbers: pageNumbers,
        sms_credential,
        pageSize,
        concurrency,
        timestamp: Date.now(),
      };
      await this.smsProducer.publishBulkPage(payload);
    }
    return { jobId, total, pages, pageSize };
  }
}
