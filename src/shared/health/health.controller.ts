import { <PERSON>, Get, HttpStatus, Res } from '@nestjs/common';
import { HealthCheck, HealthCheckService, DiskHealthIndicator, MemoryHealthIndicator } from '@nestjs/terminus';
import { RabbitMQHealthIndicator } from '../../queue/health/rabbitmq-health.indicator';
import { WorkerHealthIndicator } from '../../worker/health/worker-health.indicator';
import { Response } from 'express';
import * as os from 'os';

@Controller('health')
export class HealthController {
  constructor(
    private readonly health: HealthCheckService,
    private readonly disk: DiskHealthIndicator,
    private readonly memory: MemoryHealthIndicator,
    private readonly rabbitMQ: RabbitMQHealthIndicator,
    private readonly worker: WorkerHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  async check(@Res() response: Response): Promise<void> {
    try {
      const healthResult = await this.health.check([
        // Basic system health checks
        async () => {
          try {
            // Automatically determine the correct path based on the operating system
            const isWindows = os.platform() === 'win32';
            const diskPath = isWindows ? 'D:\\' : '/';

            return await this.disk.checkStorage('disk', {
              path: diskPath,
              thresholdPercent: 0.95
            });
          } catch (error) {
            return {
              disk: {
                status: 'down',
                error: error.message
              }
            };
          }
        },
        async () => {
          try {
            return await this.memory.checkHeap('memory_heap', 300 * 1024 * 1024); // 300MB
          } catch (error) {
            return {
              memory_heap: {
                status: 'down',
                error: error.message
              }
            };
          }
        },
        async () => {
          try {
            return await this.memory.checkRSS('memory_rss', 300 * 1024 * 1024); // 300MB
          } catch (error) {
            return {
              memory_rss: {
                status: 'down',
                error: error.message
              }
            };
          }
        },

        // Application-specific health checks
        async () => await this.rabbitMQ.isHealthy('rabbitmq'),
        async () => await this.worker.checkHealth('workers'),
      ]);

      // Determine HTTP status code based on health check result
      // Only consider the application-specific checks (RabbitMQ and workers) for determining the HTTP status
      const criticalServicesUp =
        healthResult.info?.rabbitmq?.status === 'up' &&
        healthResult.info?.workers?.status === 'up';

      const httpStatus = criticalServicesUp ? HttpStatus.OK : HttpStatus.SERVICE_UNAVAILABLE;

      // Return response with status code and health check result
      response.status(httpStatus).json({
        statusCode: httpStatus,
        ...healthResult
      });
    } catch (error) {
      // If there's an error in the health check, return a partial result with error status
      const errorResult = {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        status: 'error',
        info: {
          error: {
            status: 'down',
            message: error.message || 'Unknown error'
          }
        },
        details: {},
        error: {
          message: error.message || 'Unknown error'
        }
      };

      response.status(HttpStatus.INTERNAL_SERVER_ERROR).json(errorResult);
    }
  }
}
