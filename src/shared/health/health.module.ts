import { Module, forwardRef } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { HealthController } from './health.controller';
import { QueueModule } from '../../queue/queue.module';
import { WorkerModule } from '../../worker/worker.module';

@Module({
  imports: [
    TerminusModule,
    forwardRef(() => QueueModule),
    forwardRef(() => WorkerModule),
  ],
  controllers: [HealthController],
  providers: [],
  exports: [],
})
export class HealthModule {}
