import { Logger } from '@nestjs/common';
import { SmsServiceProvider } from '../../sms/dto/sms-request.dto';
import iconv from 'iconv-lite';

/**
 * Utility class for SMS-related operations
 */
export class SmsUtils {
  private static readonly logger = new Logger(SmsUtils.name);

  /**
   * Truncate a message to a specified length with ellipsis
   *
   * @param message - The message to truncate
   * @param maxLength - The maximum length of the message (default: 50)
   * @returns The truncated message
   */
  static truncateMessage(message: string | undefined, maxLength: number = 50): string {
    if (!message) {
      return '(empty message)';
    }

    if (message.length <= maxLength) {
      return message;
    }

    return `${message.substring(0, maxLength - 3)}...`;
  }

  /**
   * Extract message content from data object
   * Handles different field names used in different parts of the system
   *
   * @param data - The data object containing the message
   * @returns The message content
   */
  static extractMessageContent(data: any): string {
    // Check different possible field names for message content
    const messageContent = data.message || data.msg || data.content || data.text || '';
    return messageContent;
  }

  /**
   * Generate a unique message ID
   *
   * @returns A unique message ID
   */
  static generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  /**
   * Calculate a hash for a message
   * Useful for message deduplication and tracking
   *
   * @param message - The message to hash
   * @returns A numeric hash of the message
   */
  static hashMessage(message: string | undefined): number {
    if (!message) {
      return 0;
    }

    let hash = 0;
    for (let i = 0; i < message.length; i++) {
      const char = message.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return hash;
  }

  /**
   * Log message details at different log levels
   *
   * @param logger - The logger instance to use
   * @param message - The message to log
   * @param requestId - The request ID for correlation
   * @param level - The log level (default: 'log')
   */
  static logMessageDetails(
    logger: Logger,
    message: string | undefined,
    requestId: string = 'unknown',
    level: 'log' | 'debug' | 'error' | 'warn' = 'log',
  ): void {
    const truncatedMessage = this.truncateMessage(message);
    const hash = this.hashMessage(message);
    const length = message?.length || 0;

    const logMessage = `[${requestId}] Message details: content="${truncatedMessage}", length=${length}, hash=${hash}`;

    switch (level) {
      case 'debug':
        logger.debug(logMessage);
        break;
      case 'error':
        logger.error(logMessage);
        break;
      case 'warn':
        logger.warn(logMessage);
        break;
      default:
        logger.log(logMessage);
    }
  }

  /**
   * Validate a phone number format
   *
   * @param phoneNumber - The phone number to validate
   * @returns True if the phone number is valid, false otherwise
   */
  static isValidPhoneNumber(phoneNumber: string): boolean {
    // Basic validation - can be enhanced with more sophisticated rules
    if (!phoneNumber) {
      return false;
    }

    // Remove spaces, dashes, and parentheses
    const cleanedNumber = phoneNumber.replace(/[\s\-\(\)]/g, '');

    // Check if it starts with + and has 8-15 digits
    const phoneRegex = /^\+?[0-9]{8,15}$/;
    return phoneRegex.test(cleanedNumber);
  }

  /**
   * Calculate exponential backoff time with jitter for retries
   *
   * @param attempt - The current attempt number (0-based)
   * @param baseMs - The base time in milliseconds (default: 1000)
   * @param maxMs - The maximum time in milliseconds (default: 30000)
   * @returns The backoff time in milliseconds
   */
  static calculateBackoffTime(
    attempt: number,
    baseMs: number = 1000,
    maxMs: number = 30000,
  ): number {
    // Exponential backoff with jitter to prevent thundering herd
    const jitter = Math.random() * 100;
    const backoffTime = Math.min(baseMs * Math.pow(2, attempt) + jitter, maxMs);
    return backoffTime;
  }
}

/**
 * Determine the phone type based on the phone number prefix
 *
 * @param phoneNumber - The phone number to check
 * @returns The phone type (SmsServiceProvider.TELE, SmsServiceProvider.SAFARICOM, or 'UNKNOWN')
 */
export function phoneType(phoneNumber: string | number): string {
  const phoneNumberString = String(phoneNumber);

  if (phoneNumberString.startsWith('2519')) {
    return SmsServiceProvider.TELE;
  } else if (phoneNumberString.startsWith('2517')) {
    return SmsServiceProvider.SAFARICOM;
  }

  return '';
}

/**
 * Convert a string to a UTF-16BE encoded string as a hex string.
 *
 * This is used to convert Unicode strings to a format that can be sent over SMS.
 *
 * @param {string} msg - The string to convert
 * @returns {string} The UTF-16BE encoded string as a hex string
 */
export function convertString(msg: string) {
  // Convert the string to a UTF-16BE encoded Buffer
  const utf16beEncoded = iconv.encode(msg, 'UTF-16BE');

  // Convert the Buffer to a hex string
  const hexString = utf16beEncoded.toString('hex').toUpperCase();

  return hexString;
}
