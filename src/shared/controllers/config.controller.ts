import { Controller, Get, HttpStatus, Res, UseGuards } from '@nestjs/common';
import { Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ApiTokenGuard } from '../guards';

@ApiTags('config')
@Controller('config')
@UseGuards(ApiTokenGuard)
export class ConfigController {
  constructor(private readonly configService: ConfigService) {}

  @Get()
  @ApiOperation({ summary: 'Get configuration' })
  @ApiResponse({
    status: 200,
    description: 'Returns configuration information',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - API token is missing or invalid',
  })
  async getConfig(@Res() response: Response): Promise<void> {
    try {
      const config = {
        rabbitmq: {
          url: this.configService.get<string>('rabbitmq.url'),
        },
        sms: {
          queues: {
            single: this.configService.get<string>('sms.queues.single'),
            bulk: this.configService.get<string>('sms.queues.bulk'),
          },
          providers: {
            primary: this.configService.get<string>('sms.providers.primary'),
            fallback: this.configService.get<string>('sms.providers.fallback'),
          },
          workers: {
            count: this.configService.get<number>('sms.workers.count'),
          },
        },
      };

      response.status(HttpStatus.OK).json({
        statusCode: HttpStatus.OK,
        config,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      const errorResult = {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        status: 'error',
        message: error.message || 'Unknown error',
        timestamp: new Date().toISOString()
      };

      response.status(HttpStatus.INTERNAL_SERVER_ERROR).json(errorResult);
    }
  }
}
