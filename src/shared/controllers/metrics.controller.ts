import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { MetricsService } from '../services/metrics.service';

/**
 * Controller for metrics endpoints
 */
@ApiTags('metrics')
@Controller('metrics')
export class MetricsController {
  constructor(private readonly metricsService: MetricsService) {}

  /**
   * Get all metrics
   */
  @Get()
  @ApiOperation({ summary: 'Get all metrics' })
  @ApiResponse({ status: 200, description: 'Return all metrics' })
  getAllMetrics() {
    // Log metrics with HTTP metrics included
    this.metricsService.logMetrics(true);

    // Return all metrics
    return this.metricsService.getMetrics();
  }

  /**
   * Get SMS metrics
   */
  @Get('sms')
  @ApiOperation({ summary: 'Get SMS metrics' })
  @ApiResponse({ status: 200, description: 'Return SMS metrics' })
  getSmsMetrics() {
    const metrics = this.metricsService.getMetrics();

    // Return only SMS metrics (exclude HTTP metrics)
    const { http, ...smsMetrics } = metrics;
    return smsMetrics;
  }

  /**
   * Get HTTP metrics
   */
  @Get('http')
  @ApiOperation({ summary: 'Get HTTP metrics' })
  @ApiResponse({ status: 200, description: 'Return HTTP metrics' })
  getHttpMetrics() {
    const metrics = this.metricsService.getMetrics();

    // Return only HTTP metrics
    return metrics.http || {};
  }
}
