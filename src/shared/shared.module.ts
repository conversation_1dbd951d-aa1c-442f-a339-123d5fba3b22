import {
  Module,
  Global,
  MiddlewareConsumer,
  NestModule,
  Logger,
  RequestMethod,
} from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TerminusModule } from '@nestjs/terminus';
import { LoggerService } from './logger/logger.service';
import { HttpExceptionFilter } from './exceptions/http-exception.filter';
import { HealthModule } from './health/health.module';
import { ErrorHandlerService } from './services/error-handler.service';
import { MetricsService } from './services/metrics.service';
import { ConfigController } from './controllers/config.controller';
import { MetricsController } from './controllers/metrics.controller';
import {
  RequestLoggerMiddleware,
  PerformanceTrackerMiddleware,
  RateLimiterMiddleware,
  AuthTokenMiddleware,
} from './middleware';
import { ApiTokenGuard } from './guards';

@Global()
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: [`.env.${process.env.NODE_ENV || 'development'}`, '.env'],
      load: [() => import('./config/configuration').then((config) => config.default())],
    }),
    TerminusModule,
    HealthModule,
  ],
  controllers: [ConfigController, MetricsController],
  providers: [
    LoggerService,
    ErrorHandlerService,
    MetricsService,
    RequestLoggerMiddleware,
    PerformanceTrackerMiddleware,
    RateLimiterMiddleware,
    AuthTokenMiddleware,
    ApiTokenGuard,
    {
      provide: 'APP_FILTER',
      useClass: HttpExceptionFilter,
    },
  ],
  exports: [
    ConfigModule,
    TerminusModule,
    LoggerService,
    ErrorHandlerService,
    MetricsService,
    ApiTokenGuard,
  ],
})
export class SharedModule implements NestModule {
  constructor(private readonly configService: ConfigService) {}

  /**
   * Configure middleware for the application
   * @param consumer The middleware consumer
   */
  configure(consumer: MiddlewareConsumer) {
    const logger = new Logger('SharedModule');

    // Apply request logger middleware to all routes
    consumer.apply(RequestLoggerMiddleware).forRoutes('*');

    // Apply performance tracking middleware to all routes
    consumer.apply(PerformanceTrackerMiddleware).forRoutes('*');

    // Apply API token authentication middleware
    const authToken = this.configService.get<string>('api.auth.token');
    if (authToken) {
      logger.log('Enabling API token authentication');
      const path = 'api/v1/';
      consumer
        .apply(AuthTokenMiddleware)
        // .exclude('api/v1/health') // Don't require auth for health checks
        .exclude(
          { path: 'api/docs', method: RequestMethod.GET }, // Swagger UI
          { path: `${path}health`, method: RequestMethod.GET },
          { path: `${path}sms/send/success/:sid`, method: RequestMethod.POST },
        )
        .forRoutes('*');
    } else {
      logger.warn(
        'API token authentication is DISABLED. Set API_AUTH_TOKEN in your environment variables for better security.',
      );
    }

    // Apply rate limiting based on configuration
    const rateLimitEnabled = this.configService.get<boolean>('api.throttle.enabled', false);
    const rateLimitValue = this.configService.get<number>('api.throttle.limit', 100);

    if (rateLimitEnabled) {
      logger.log(`Enabling rate limiting with limit of ${rateLimitValue} requests per minute`);

      consumer
        .apply(RateLimiterMiddleware)
        .exclude('api/v1/health') // Don't rate limit health checks
        .forRoutes('*');
    }
  }
}
