import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';

export interface FailedSmsMessage {
  id: string;
  to: string;
  message: string;
  provider: string;
  error: string;
  retryCount: number;
  maxRetries: number;
  timestamp: string;
  requestId: string;
  messageType: 'single' | 'bulk';
  smsCredentials: any;
  smsLogId?: string;
}

export interface PendingSmsMessage {
  id: string;
  to: string;
  message: string;
  provider: string;
  timestamp: string;
  requestId: string;
  messageType: 'single' | 'bulk';
  smsCredentials: any;
  smsLogId?: string;
  queueName: string;
}

@Injectable()
export class FailedSmsPersistenceService {
  private readonly logger = new Logger(FailedSmsPersistenceService.name);
  private readonly failedSmsPath: string;
  private readonly pendingSmsPath: string;
  private readonly persistenceEnabled: boolean;

  constructor(private readonly configService: ConfigService) {
    this.persistenceEnabled = this.configService.get<boolean>('persistence.enabled', true);
    this.failedSmsPath = this.configService.get<string>('persistence.failedSmsPath', './data/failed_sms');
    this.pendingSmsPath = this.configService.get<string>('persistence.pendingSmsPath', './data/pending_sms');
    
    this.ensureDirectoriesExist();
  }

  private ensureDirectoriesExist(): void {
    if (!this.persistenceEnabled) return;

    try {
      if (!fs.existsSync(this.failedSmsPath)) {
        fs.mkdirSync(this.failedSmsPath, { recursive: true });
        this.logger.log(`Created failed SMS directory: ${this.failedSmsPath}`);
      }

      if (!fs.existsSync(this.pendingSmsPath)) {
        fs.mkdirSync(this.pendingSmsPath, { recursive: true });
        this.logger.log(`Created pending SMS directory: ${this.pendingSmsPath}`);
      }
    } catch (error) {
      this.logger.error(`Failed to create persistence directories: ${error.message}`);
    }
  }

  /**
   * Save a failed SMS message to persistent storage
   */
  async saveFailedSms(message: FailedSmsMessage): Promise<void> {
    if (!this.persistenceEnabled) return;

    try {
      const fileName = `${message.id}_${Date.now()}.json`;
      const filePath = path.join(this.failedSmsPath, fileName);
      
      const data = JSON.stringify(message, null, 2);
      await fs.promises.writeFile(filePath, data, 'utf8');
      
      this.logger.debug(`Saved failed SMS to: ${filePath}`);
    } catch (error) {
      this.logger.error(`Failed to save failed SMS: ${error.message}`);
    }
  }

  /**
   * Save a pending SMS message to persistent storage
   */
  async savePendingSms(message: PendingSmsMessage): Promise<void> {
    if (!this.persistenceEnabled) return;

    try {
      const fileName = `${message.id}_${Date.now()}.json`;
      const filePath = path.join(this.pendingSmsPath, fileName);
      
      const data = JSON.stringify(message, null, 2);
      await fs.promises.writeFile(filePath, data, 'utf8');
      
      this.logger.debug(`Saved pending SMS to: ${filePath}`);
    } catch (error) {
      this.logger.error(`Failed to save pending SMS: ${error.message}`);
    }
  }

  /**
   * Load all failed SMS messages from persistent storage
   */
  async loadFailedSms(): Promise<FailedSmsMessage[]> {
    if (!this.persistenceEnabled) return [];

    try {
      const files = await fs.promises.readdir(this.failedSmsPath);
      const failedMessages: FailedSmsMessage[] = [];

      for (const file of files) {
        if (file.endsWith('.json')) {
          try {
            const filePath = path.join(this.failedSmsPath, file);
            const data = await fs.promises.readFile(filePath, 'utf8');
            const message = JSON.parse(data) as FailedSmsMessage;
            failedMessages.push(message);
          } catch (error) {
            this.logger.warn(`Failed to load failed SMS file ${file}: ${error.message}`);
          }
        }
      }

      this.logger.log(`Loaded ${failedMessages.length} failed SMS messages from persistence`);
      return failedMessages;
    } catch (error) {
      this.logger.error(`Failed to load failed SMS messages: ${error.message}`);
      return [];
    }
  }

  /**
   * Load all pending SMS messages from persistent storage
   */
  async loadPendingSms(): Promise<PendingSmsMessage[]> {
    if (!this.persistenceEnabled) return [];

    try {
      const files = await fs.promises.readdir(this.pendingSmsPath);
      const pendingMessages: PendingSmsMessage[] = [];

      for (const file of files) {
        if (file.endsWith('.json')) {
          try {
            const filePath = path.join(this.pendingSmsPath, file);
            const data = await fs.promises.readFile(filePath, 'utf8');
            const message = JSON.parse(data) as PendingSmsMessage;
            pendingMessages.push(message);
          } catch (error) {
            this.logger.warn(`Failed to load pending SMS file ${file}: ${error.message}`);
          }
        }
      }

      this.logger.log(`Loaded ${pendingMessages.length} pending SMS messages from persistence`);
      return pendingMessages;
    } catch (error) {
      this.logger.error(`Failed to load pending SMS messages: ${error.message}`);
      return [];
    }
  }

  /**
   * Remove a failed SMS message from persistent storage
   */
  async removeFailedSms(messageId: string): Promise<void> {
    if (!this.persistenceEnabled) return;

    try {
      const files = await fs.promises.readdir(this.failedSmsPath);
      
      for (const file of files) {
        if (file.startsWith(messageId) && file.endsWith('.json')) {
          const filePath = path.join(this.failedSmsPath, file);
          await fs.promises.unlink(filePath);
          this.logger.debug(`Removed failed SMS file: ${filePath}`);
          break;
        }
      }
    } catch (error) {
      this.logger.error(`Failed to remove failed SMS: ${error.message}`);
    }
  }

  /**
   * Remove a pending SMS message from persistent storage
   */
  async removePendingSms(messageId: string): Promise<void> {
    if (!this.persistenceEnabled) return;

    try {
      const files = await fs.promises.readdir(this.pendingSmsPath);
      
      for (const file of files) {
        if (file.startsWith(messageId) && file.endsWith('.json')) {
          const filePath = path.join(this.pendingSmsPath, file);
          await fs.promises.unlink(filePath);
          this.logger.debug(`Removed pending SMS file: ${filePath}`);
          break;
        }
      }
    } catch (error) {
      this.logger.error(`Failed to remove pending SMS: ${error.message}`);
    }
  }

  /**
   * Get statistics about failed and pending SMS messages
   */
  async getPersistenceStats(): Promise<{
    failedCount: number;
    pendingCount: number;
    totalSize: number;
  }> {
    if (!this.persistenceEnabled) {
      return { failedCount: 0, pendingCount: 0, totalSize: 0 };
    }

    try {
      const failedFiles = await fs.promises.readdir(this.failedSmsPath);
      const pendingFiles = await fs.promises.readdir(this.pendingSmsPath);
      
      const failedCount = failedFiles.filter(f => f.endsWith('.json')).length;
      const pendingCount = pendingFiles.filter(f => f.endsWith('.json')).length;

      // Calculate total size
      let totalSize = 0;
      
      for (const file of [...failedFiles, ...pendingFiles]) {
        if (file.endsWith('.json')) {
          const filePath = path.join(
            file.includes('failed') ? this.failedSmsPath : this.pendingSmsPath, 
            file
          );
          try {
            const stats = await fs.promises.stat(filePath);
            totalSize += stats.size;
          } catch (error) {
            // Ignore errors for individual files
          }
        }
      }

      return { failedCount, pendingCount, totalSize };
    } catch (error) {
      this.logger.error(`Failed to get persistence stats: ${error.message}`);
      return { failedCount: 0, pendingCount: 0, totalSize: 0 };
    }
  }

  /**
   * Clear all persisted messages (use with caution)
   */
  async clearAllPersistedMessages(): Promise<void> {
    if (!this.persistenceEnabled) return;

    try {
      const failedFiles = await fs.promises.readdir(this.failedSmsPath);
      const pendingFiles = await fs.promises.readdir(this.pendingSmsPath);

      // Remove failed SMS files
      for (const file of failedFiles) {
        if (file.endsWith('.json')) {
          await fs.promises.unlink(path.join(this.failedSmsPath, file));
        }
      }

      // Remove pending SMS files
      for (const file of pendingFiles) {
        if (file.endsWith('.json')) {
          await fs.promises.unlink(path.join(this.pendingSmsPath, file));
        }
      }

      this.logger.log('Cleared all persisted messages');
    } catch (error) {
      this.logger.error(`Failed to clear persisted messages: ${error.message}`);
    }
  }
} 