import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { FailedSmsPersistenceService, FailedSmsMessage } from './failed-sms-persistence.service';
import { SmsProducer } from '../../queue/producers/sms.producer';

@Injectable()
export class FailedSmsRetryService implements OnModuleInit {
  private readonly logger = new Logger(FailedSmsRetryService.name);
  private readonly maxRetries: number;
  private readonly retryDelayMs: number;
  private readonly backoffMultiplier: number;

  constructor(
    private readonly configService: ConfigService,
    private readonly failedSmsPersistence: FailedSmsPersistenceService,
    private readonly smsProducer: SmsProducer,
  ) {
    this.maxRetries = this.configService.get<number>('sms.retries', 3);
    this.retryDelayMs = this.configService.get<number>('SMS_RETRY_DELAY_MS', 1000);
    this.backoffMultiplier = this.configService.get<number>('SMS_BACKOFF_MULTIPLIER', 2);
  }

  async onModuleInit(): Promise<void> {
    // Load and retry failed SMS messages on startup
    await this.retryFailedSmsOnStartup();
  }

  /**
   * Retry failed SMS messages on server startup
   */
  private async retryFailedSmsOnStartup(): Promise<void> {
    try {
      this.logger.log('Loading failed SMS messages for retry on startup...');
      
      const failedMessages = await this.failedSmsPersistence.loadFailedSms();
      
      if (failedMessages.length === 0) {
        this.logger.log('No failed SMS messages found to retry');
        return;
      }

      this.logger.log(`Found ${failedMessages.length} failed SMS messages to retry`);

      let retryCount = 0;
      let skipCount = 0;

      for (const failedMessage of failedMessages) {
        try {
          // Check if we should retry this message
          if (failedMessage.retryCount >= this.maxRetries) {
            this.logger.warn(
              `Skipping message ${failedMessage.id}: max retries (${this.maxRetries}) reached`,
            );
            skipCount++;
            continue;
          }

          // Calculate retry delay with exponential backoff
          const delay = this.calculateRetryDelay(failedMessage.retryCount);
          
          // Prepare message for retry
          const retryMessage = {
            data: {
              to: failedMessage.to,
              message: failedMessage.message,
              sms_credential: failedMessage.smsCredentials,
              sms_log_id: failedMessage.smsLogId,
              requestId: failedMessage.requestId,
            },
            retryCount: failedMessage.retryCount + 1,
            errorType: 'RETRY_ON_STARTUP',
            messageId: failedMessage.id,
            timestamp: new Date().toISOString(),
          };

          // Schedule retry
          await this.smsProducer.scheduleRetry(retryMessage, delay);
          
          // Remove from persistence since it's now in the retry queue
          await this.failedSmsPersistence.removeFailedSms(failedMessage.id);
          
          retryCount++;
          
          this.logger.log(
            `Scheduled retry for message ${failedMessage.id} (attempt ${failedMessage.retryCount + 1}) in ${delay}ms`,
          );
        } catch (error) {
          this.logger.error(
            `Failed to schedule retry for message ${failedMessage.id}: ${error.message}`,
          );
        }
      }

      this.logger.log(
        `Startup retry complete: ${retryCount} messages scheduled for retry, ${skipCount} messages skipped`,
      );
    } catch (error) {
      this.logger.error(`Failed to retry failed SMS messages on startup: ${error.message}`);
    }
  }

  /**
   * Manually retry a specific failed SMS message
   */
  async retryFailedSms(messageId: string): Promise<boolean> {
    try {
      const failedMessages = await this.failedSmsPersistence.loadFailedSms();
      const failedMessage = failedMessages.find(msg => msg.id === messageId);
      
      if (!failedMessage) {
        this.logger.warn(`Failed SMS message ${messageId} not found`);
        return false;
      }

      if (failedMessage.retryCount >= this.maxRetries) {
        this.logger.warn(
          `Cannot retry message ${messageId}: max retries (${this.maxRetries}) reached`,
        );
        return false;
      }

      // Calculate retry delay
      const delay = this.calculateRetryDelay(failedMessage.retryCount);
      
      // Prepare message for retry
      const retryMessage = {
        data: {
          to: failedMessage.to,
          message: failedMessage.message,
          sms_credential: failedMessage.smsCredentials,
          sms_log_id: failedMessage.smsLogId,
          requestId: failedMessage.requestId,
        },
        retryCount: failedMessage.retryCount + 1,
        errorType: 'MANUAL_RETRY',
        messageId: failedMessage.id,
        timestamp: new Date().toISOString(),
      };

      // Schedule retry
      await this.smsProducer.scheduleRetry(retryMessage, delay);
      
      // Remove from persistence
      await this.failedSmsPersistence.removeFailedSms(failedMessage.id);
      
      this.logger.log(
        `Manually scheduled retry for message ${messageId} (attempt ${failedMessage.retryCount + 1}) in ${delay}ms`,
      );
      
      return true;
    } catch (error) {
      this.logger.error(`Failed to retry message ${messageId}: ${error.message}`);
      return false;
    }
  }

  /**
   * Retry all failed SMS messages
   */
  async retryAllFailedSms(): Promise<{ success: number; failed: number; skipped: number }> {
    try {
      const failedMessages = await this.failedSmsPersistence.loadFailedSms();
      
      if (failedMessages.length === 0) {
        this.logger.log('No failed SMS messages found to retry');
        return { success: 0, failed: 0, skipped: 0 };
      }

      this.logger.log(`Retrying ${failedMessages.length} failed SMS messages`);

      let successCount = 0;
      let failedCount = 0;
      let skippedCount = 0;

      for (const failedMessage of failedMessages) {
        try {
          if (failedMessage.retryCount >= this.maxRetries) {
            this.logger.warn(
              `Skipping message ${failedMessage.id}: max retries (${this.maxRetries}) reached`,
            );
            skippedCount++;
            continue;
          }

          const success = await this.retryFailedSms(failedMessage.id);
          if (success) {
            successCount++;
          } else {
            failedCount++;
          }
        } catch (error) {
          this.logger.error(
            `Failed to retry message ${failedMessage.id}: ${error.message}`,
          );
          failedCount++;
        }
      }

      this.logger.log(
        `Bulk retry complete: ${successCount} successful, ${failedCount} failed, ${skippedCount} skipped`,
      );

      return { success: successCount, failed: failedCount, skipped: skippedCount };
    } catch (error) {
      this.logger.error(`Failed to retry all failed SMS messages: ${error.message}`);
      return { success: 0, failed: 0, skipped: 0 };
    }
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  private calculateRetryDelay(retryCount: number): number {
    return this.retryDelayMs * Math.pow(this.backoffMultiplier, retryCount);
  }

  /**
   * Get retry statistics
   */
  async getRetryStats(): Promise<{
    totalFailed: number;
    retryableCount: number;
    maxRetriesReachedCount: number;
    averageRetryCount: number;
  }> {
    try {
      const failedMessages = await this.failedSmsPersistence.loadFailedSms();
      
      const totalFailed = failedMessages.length;
      const retryableCount = failedMessages.filter(msg => msg.retryCount < this.maxRetries).length;
      const maxRetriesReachedCount = failedMessages.filter(msg => msg.retryCount >= this.maxRetries).length;
      const averageRetryCount = totalFailed > 0 
        ? failedMessages.reduce((sum, msg) => sum + msg.retryCount, 0) / totalFailed 
        : 0;

      return {
        totalFailed,
        retryableCount,
        maxRetriesReachedCount,
        averageRetryCount: Math.round(averageRetryCount * 100) / 100,
      };
    } catch (error) {
      this.logger.error(`Failed to get retry stats: ${error.message}`);
      return {
        totalFailed: 0,
        retryableCount: 0,
        maxRetriesReachedCount: 0,
        averageRetryCount: 0,
      };
    }
  }
} 