import { Injectable, Logger } from '@nestjs/common';

/**
 * Error types for SMS processing
 */
export enum ErrorType {
  // Temporary errors that can be retried
  TEMPORARY_NETWORK = 'TEMPORARY_NETWORK',
  TEMPORARY_SERVICE = 'TEMPORARY_SERVICE',
  RATE_LIMIT = 'RATE_LIMIT',
  TIMEOUT = 'TIMEOUT',
  
  // Permanent errors that should not be retried
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  INVALID_RECIPIENT = 'INVALID_RECIPIENT',
  INVALID_MESSAGE = 'INVALID_MESSAGE',
  INSUFFICIENT_FUNDS = 'INSUFFICIENT_FUNDS',
  
  // Unknown errors
  UNKNOWN = 'UNKNOWN'
}

/**
 * Error context for SMS processing
 */
export interface ErrorContext {
  messageId: string;
  requestId: string;
  messageType: 'single' | 'bulk' | 'dlr';
  retryCount: number;
  provider?: string;
  recipient?: string;
  timestamp: Date;
  additionalInfo?: Record<string, any>;
}

/**
 * Error handler service for SMS processing
 * 
 * This service provides error classification, logging, and retry decision logic
 * for SMS processing errors.
 */
@Injectable()
export class ErrorHandlerService {
  private readonly logger = new Logger(ErrorHandlerService.name);
  
  /**
   * Classify an error based on its message and context
   * 
   * @param error - The error to classify
   * @param context - The error context
   * @returns The error type
   */
  classifyError(error: Error, context?: Partial<ErrorContext>): ErrorType {
    const errorMessage = error.message.toLowerCase();
    
    // Network errors
    if (
      errorMessage.includes('network') ||
      errorMessage.includes('connection') ||
      errorMessage.includes('econnrefused') ||
      errorMessage.includes('econnreset') ||
      errorMessage.includes('socket')
    ) {
      return ErrorType.TEMPORARY_NETWORK;
    }
    
    // Service errors
    if (
      errorMessage.includes('service unavailable') ||
      errorMessage.includes('503') ||
      errorMessage.includes('502') ||
      errorMessage.includes('500') ||
      errorMessage.includes('internal server error')
    ) {
      return ErrorType.TEMPORARY_SERVICE;
    }
    
    // Rate limit errors
    if (
      errorMessage.includes('rate limit') ||
      errorMessage.includes('too many requests') ||
      errorMessage.includes('429')
    ) {
      return ErrorType.RATE_LIMIT;
    }
    
    // Timeout errors
    if (
      errorMessage.includes('timeout') ||
      errorMessage.includes('timed out')
    ) {
      return ErrorType.TIMEOUT;
    }
    
    // Authentication errors
    if (
      errorMessage.includes('authentication') ||
      errorMessage.includes('auth') ||
      errorMessage.includes('credentials') ||
      errorMessage.includes('unauthorized') ||
      errorMessage.includes('401')
    ) {
      return ErrorType.INVALID_CREDENTIALS;
    }
    
    // Recipient errors
    if (
      errorMessage.includes('recipient') ||
      errorMessage.includes('phone') ||
      errorMessage.includes('invalid number') ||
      errorMessage.includes('destination')
    ) {
      return ErrorType.INVALID_RECIPIENT;
    }
    
    // Message errors
    if (
      errorMessage.includes('message') ||
      errorMessage.includes('content') ||
      errorMessage.includes('text')
    ) {
      return ErrorType.INVALID_MESSAGE;
    }
    
    // Billing errors
    if (
      errorMessage.includes('funds') ||
      errorMessage.includes('credit') ||
      errorMessage.includes('balance') ||
      errorMessage.includes('payment')
    ) {
      return ErrorType.INSUFFICIENT_FUNDS;
    }
    
    // Default to unknown
    return ErrorType.UNKNOWN;
  }
  
  /**
   * Determine if an error should be retried based on its type and retry count
   * 
   * @param errorType - The error type
   * @param retryCount - The current retry count
   * @param maxRetries - The maximum number of retries
   * @returns Whether the error should be retried
   */
  shouldRetry(errorType: ErrorType, retryCount: number, maxRetries: number): boolean {
    // Don't retry if we've reached the maximum retries
    if (retryCount >= maxRetries) {
      return false;
    }
    
    // Don't retry permanent errors
    switch (errorType) {
      case ErrorType.INVALID_CREDENTIALS:
      case ErrorType.INVALID_RECIPIENT:
      case ErrorType.INVALID_MESSAGE:
      case ErrorType.INSUFFICIENT_FUNDS:
        return false;
      
      // Retry temporary errors
      case ErrorType.TEMPORARY_NETWORK:
      case ErrorType.TEMPORARY_SERVICE:
      case ErrorType.RATE_LIMIT:
      case ErrorType.TIMEOUT:
      case ErrorType.UNKNOWN:
        return true;
    }
  }
  
  /**
   * Calculate the delay for a retry based on the error type and retry count
   * 
   * @param errorType - The error type
   * @param retryCount - The current retry count
   * @returns The delay in milliseconds
   */
  calculateRetryDelay(errorType: ErrorType, retryCount: number): number {
    // Base delay with exponential backoff
    const baseDelay = 1000 * Math.pow(2, retryCount);
    
    // Add jitter to prevent thundering herd
    const jitter = Math.random() * 1000;
    
    // Adjust delay based on error type
    switch (errorType) {
      case ErrorType.RATE_LIMIT:
        // Rate limit errors should have longer delays
        return baseDelay * 2 + jitter;
      
      case ErrorType.TEMPORARY_SERVICE:
        // Service errors should have longer delays
        return baseDelay * 1.5 + jitter;
      
      default:
        return baseDelay + jitter;
    }
  }
  
  /**
   * Log an error with context
   * 
   * @param error - The error to log
   * @param errorType - The error type
   * @param context - The error context
   */
  logError(error: Error, errorType: ErrorType, context: ErrorContext): void {
    const { messageId, requestId, messageType, retryCount, provider, recipient } = context;
    
    this.logger.error(
      `[${requestId}] ${messageType.toUpperCase()} SMS error (${errorType}): ${error.message}`,
      {
        errorType,
        messageId,
        requestId,
        messageType,
        retryCount,
        provider,
        recipient,
        timestamp: context.timestamp.toISOString(),
        stack: error.stack,
        additionalInfo: context.additionalInfo
      }
    );
  }
}
