import { Injectable, Logger } from '@nestjs/common';

/**
 * Metrics for SMS processing
 */
export interface SmsMetrics {
  // Message counts
  totalMessages: number;
  successfulMessages: number;
  failedMessages: number;

  // Message types
  singleMessages: number;
  bulkMessages: number;

  // Retry metrics
  retryAttempts: number;
  maxRetriesReached: number;

  // Error metrics
  errorsByType: Record<string, number>;

  // Performance metrics
  processingTimes: number[];
  averageProcessingTime: number;

  // Queue metrics
  queueDepths: Record<string, number>;
}

/**
 * Metrics service for SMS processing
 *
 * This service tracks metrics for SMS processing, including message counts,
 * error rates, processing times, and queue depths.
 */
@Injectable()
export class MetricsService {
  private readonly logger = new Logger(MetricsService.name);

  // Message counts
  private totalMessages = 0;
  private successfulMessages = 0;
  private failedMessages = 0;

  // Message types
  private singleMessages = 0;
  private bulkMessages = 0;

  // Retry metrics
  private retryAttempts = 0;
  private maxRetriesReached = 0;

  // Error metrics
  private errorsByType: Record<string, number> = {};

  // Performance metrics
  private processingTimes: number[] = [];

  // Queue metrics
  private queueDepths: Record<string, number> = {
    'sms_single_queue': 0,
    'sms_bulk_queue': 0,
    'sms_dlr_queue': 0,
    'sms_retry_queue': 0,
    'dlx_queue': 0
  };

  // HTTP metrics
  private httpRequestsTotal: Record<string, number> = {};
  private httpResponsesTotal: Record<string, number> = {};
  private httpRequestDurations: Record<string, number[]> = {};

  /**
   * Record a message received
   *
   * @param messageType - The type of message ('single' or 'bulk')
   */
  recordMessageReceived(messageType: 'single' | 'bulk'): void {
    this.totalMessages++;

    if (messageType === 'single') {
      this.singleMessages++;
    } else {
      this.bulkMessages++;
    }
  }

  /**
   * Record a message processed successfully
   *
   * @param processingTime - The time taken to process the message in milliseconds
   */
  recordMessageSuccess(processingTime: number): void {
    this.successfulMessages++;
    this.processingTimes.push(processingTime);

    // Keep only the last 1000 processing times to avoid memory issues
    if (this.processingTimes.length > 1000) {
      this.processingTimes.shift();
    }
  }

  /**
   * Record a message processing failure
   *
   * @param errorType - The type of error
   * @param isRetry - Whether this was a retry attempt
   * @param isMaxRetries - Whether the maximum retry count was reached
   */
  recordMessageFailure(errorType: string, isRetry: boolean = false, isMaxRetries: boolean = false): void {
    this.failedMessages++;

    // Increment error count by type
    this.errorsByType[errorType] = (this.errorsByType[errorType] || 0) + 1;

    // Track retry metrics
    if (isRetry) {
      this.retryAttempts++;
    }

    if (isMaxRetries) {
      this.maxRetriesReached++;
    }
  }

  /**
   * Update queue depth
   *
   * @param queueName - The name of the queue
   * @param depth - The current depth of the queue
   */
  updateQueueDepth(queueName: string, depth: number): void {
    if (this.queueDepths.hasOwnProperty(queueName)) {
      this.queueDepths[queueName] = depth;
    }
  }

  /**
   * Get the current metrics
   *
   * @returns The current metrics
   */
  getMetrics(): SmsMetrics & { http?: Record<string, any> } {
    // Calculate average processing time
    const averageProcessingTime = this.processingTimes.length > 0
      ? this.processingTimes.reduce((sum, time) => sum + time, 0) / this.processingTimes.length
      : 0;

    return {
      totalMessages: this.totalMessages,
      successfulMessages: this.successfulMessages,
      failedMessages: this.failedMessages,

      singleMessages: this.singleMessages,
      bulkMessages: this.bulkMessages,

      retryAttempts: this.retryAttempts,
      maxRetriesReached: this.maxRetriesReached,

      errorsByType: { ...this.errorsByType },

      processingTimes: [...this.processingTimes],
      averageProcessingTime,

      queueDepths: { ...this.queueDepths },

      // Include HTTP metrics
      http: this.getHttpMetrics()
    };
  }

  /**
   * Log the current metrics
   *
   * @param includeHttpMetrics - Whether to include HTTP metrics in the log
   */
  logMetrics(includeHttpMetrics: boolean = false): void {
    const metrics = this.getMetrics();

    // Log SMS metrics
    this.logger.log('SMS Processing Metrics', {
      totalMessages: metrics.totalMessages,
      successRate: metrics.totalMessages > 0
        ? `${((metrics.successfulMessages / metrics.totalMessages) * 100).toFixed(2)}%`
        : 'N/A',
      averageProcessingTime: `${metrics.averageProcessingTime.toFixed(2)}ms`,
      queueDepths: metrics.queueDepths,
      errorRates: metrics.failedMessages > 0
        ? Object.entries(metrics.errorsByType).map(([type, count]) => ({
            type,
            count,
            percentage: `${((count / metrics.failedMessages) * 100).toFixed(2)}%`
          }))
        : []
    });

    // Log HTTP metrics if requested
    if (includeHttpMetrics && metrics.http) {
      // Get top 5 endpoints by request count
      const topEndpoints = Object.entries(metrics.http.requests)
        .sort(([, countA], [, countB]) => (countB as number) - (countA as number))
        .slice(0, 5)
        .map(([key, count]) => ({ endpoint: key, count }));

      // Get response time statistics for all endpoints
      const responseTimeStats = Object.entries(metrics.http.durations || {})
        .map(([key, stats]) => {
          const typedStats = stats as { avg: number; p95: number; p99: number };
          return {
            endpoint: key,
            avg: `${typedStats.avg.toFixed(2)}ms`,
            p95: `${typedStats.p95.toFixed(2)}ms`,
            p99: `${typedStats.p99.toFixed(2)}ms`,
          };
        });

      this.logger.log('HTTP Metrics', {
        topEndpoints,
        responseTimeStats,
      });
    }
  }

  /**
   * Increment a counter metric
   *
   * @param name - The name of the counter
   * @param tags - Tags to associate with the counter
   */
  incrementCounter(name: string, tags: Record<string, string> = {}): void {
    // Create a unique key for this counter and tags
    const key = this.createMetricKey(name, tags);

    // Determine which counter to update based on the metric name
    if (name === 'http_requests_total') {
      this.httpRequestsTotal[key] = (this.httpRequestsTotal[key] || 0) + 1;
    } else if (name === 'http_responses_total') {
      this.httpResponsesTotal[key] = (this.httpResponsesTotal[key] || 0) + 1;
    } else {
      // For other counters, use a generic approach
      this.httpRequestsTotal[key] = (this.httpRequestsTotal[key] || 0) + 1;
    }
  }

  /**
   * Record a value in a histogram metric
   *
   * @param name - The name of the histogram
   * @param value - The value to record
   * @param tags - Tags to associate with the histogram
   */
  recordHistogram(name: string, value: number, tags: Record<string, string> = {}): void {
    // Create a unique key for this histogram and tags
    const key = this.createMetricKey(name, tags);

    // Initialize the histogram if it doesn't exist
    if (!this.httpRequestDurations[key]) {
      this.httpRequestDurations[key] = [];
    }

    // Add the value to the histogram
    this.httpRequestDurations[key].push(value);

    // Keep only the last 1000 values to avoid memory issues
    if (this.httpRequestDurations[key].length > 1000) {
      this.httpRequestDurations[key].shift();
    }
  }

  /**
   * Create a unique key for a metric and its tags
   *
   * @param name - The name of the metric
   * @param tags - Tags to associate with the metric
   * @returns A unique key for the metric and tags
   */
  private createMetricKey(name: string, tags: Record<string, string>): string {
    // Sort the tags by key to ensure consistent key generation
    const sortedTags = Object.entries(tags)
      .sort(([keyA], [keyB]) => keyA.localeCompare(keyB))
      .map(([key, value]) => `${key}=${value}`)
      .join(',');

    // Create a unique key for this metric and tags
    return sortedTags ? `${name}{${sortedTags}}` : name;
  }

  /**
   * Get HTTP metrics
   *
   * @returns HTTP metrics
   */
  getHttpMetrics(): Record<string, any> {
    // Calculate histogram statistics
    const histogramStats = Object.entries(this.httpRequestDurations).reduce((stats: Record<string, any>, [key, values]) => {
      if (values.length === 0) return stats;

      const sorted = [...values].sort((a, b) => a - b);
      const count = values.length;
      const sum = values.reduce((sum, value) => sum + value, 0);
      const avg = sum / count;
      const min = sorted[0];
      const max = sorted[count - 1];
      const p50 = sorted[Math.floor(count * 0.5)];
      const p90 = sorted[Math.floor(count * 0.9)];
      const p95 = sorted[Math.floor(count * 0.95)];
      const p99 = sorted[Math.floor(count * 0.99)];

      stats[key] = { count, sum, avg, min, max, p50, p90, p95, p99 };
      return stats;
    }, {} as Record<string, any>);

    return {
      requests: this.httpRequestsTotal,
      responses: this.httpResponsesTotal,
      durations: histogramStats,
    };
  }

  /**
   * Reset all metrics
   */
  resetMetrics(): void {
    this.totalMessages = 0;
    this.successfulMessages = 0;
    this.failedMessages = 0;

    this.singleMessages = 0;
    this.bulkMessages = 0;

    this.retryAttempts = 0;
    this.maxRetriesReached = 0;

    this.errorsByType = {};

    this.processingTimes = [];

    Object.keys(this.queueDepths).forEach(key => {
      this.queueDepths[key] = 0;
    });

    // Reset HTTP metrics
    this.httpRequestsTotal = {};
    this.httpResponsesTotal = {};
    this.httpRequestDurations = {};
  }
}
