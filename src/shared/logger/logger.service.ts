import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Log levels in order of increasing verbosity
 */
enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  VERBOSE = 4
}

/**
 * Type for log level strings that can be used in configuration
 */
type LogLevelString = keyof typeof LogLevel;

/**
 * Custom logger service that implements NestJS Logger interface
 * with structured JSON logging and log level filtering.
 */
@Injectable()
export class LoggerService implements NestLoggerService {
  private context?: string;
  private logLevelValue: LogLevel;

  /**
   * Creates a new LoggerService instance
   * @param configService - The NestJS ConfigService for accessing configuration
   */
  constructor(private readonly configService: ConfigService) {
    // Get log level from config or use INFO as default
    const configLevelStr = this.configService.get<string>('LOG_LEVEL') || 'info';
    const configLevel = configLevelStr.toUpperCase() as LogLevelString;
    this.logLevelValue = LogLevel[configLevel] !== undefined ? LogLevel[configLevel] : LogLevel.INFO;
  }

  /**
   * Sets the context for the logger
   * @param context - The context (usually class name) for log messages
   * @returns The logger instance for chaining
   */
  setContext(context: string): this {
    this.context = context;
    return this;
  }

  /**
   * Logs an informational message
   * @param message - The message to log
   * @param context - Optional context override
   */
  log(message: any, context?: string): void {
    this.logMessage(LogLevel.INFO, 'INFO', message, context);
  }

  /**
   * Logs an error message with optional stack trace
   * @param message - The error message
   * @param trace - Optional stack trace
   * @param context - Optional context override
   */
  error(message: any, trace?: string, context?: string): void {
    this.logMessage(LogLevel.ERROR, 'ERROR', message, context);
    if (trace) {
      // Log stack traces separately for better readability
      console.error(trace);
    }
  }

  /**
   * Logs a warning message
   * @param message - The warning message
   * @param context - Optional context override
   */
  warn(message: any, context?: string): void {
    this.logMessage(LogLevel.WARN, 'WARN', message, context);
  }

  /**
   * Logs a debug message
   * @param message - The debug message
   * @param context - Optional context override
   */
  debug(message: any, context?: string): void {
    this.logMessage(LogLevel.DEBUG, 'DEBUG', message, context);
  }

  /**
   * Logs a verbose message
   * @param message - The verbose message
   * @param context - Optional context override
   */
  verbose(message: any, context?: string): void {
    this.logMessage(LogLevel.VERBOSE, 'VERBOSE', message, context);
  }

  /**
   * Logs a message if the log level is enabled
   * @param level - The numeric log level
   * @param levelString - The string representation of the log level
   * @param message - The message to log
   * @param context - Optional context override
   */
  private logMessage(level: LogLevel, levelString: string, message: any, context?: string): void {
    // Skip if this log level is not enabled
    if (level > this.logLevelValue) {
      return;
    }

    // Format the message as a string if it's not already
    const messageString = typeof message === 'object'
      ? JSON.stringify(message)
      : String(message);

    // Create structured log entry
    const timestamp = new Date().toISOString();
    const logContext = context || this.context || 'Application';
    const correlationId = this.getCorrelationId();

    const logEntry = {
      timestamp,
      level: levelString,
      context: logContext,
      message: messageString,
      ...(correlationId ? { correlationId } : {}),
    };

    // Output to console as JSON
    console.log(JSON.stringify(logEntry));
  }

  /**
   * Gets the current correlation ID from async storage if available
   * @returns The correlation ID or undefined
   */
  private getCorrelationId(): string | undefined {
    // This is a placeholder for future implementation
    // In Phase 5, we'll implement proper correlation ID tracking
    return undefined;
  }
}
