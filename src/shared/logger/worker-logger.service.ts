import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Log levels in order of increasing verbosity
 */
enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  VERBOSE = 4
}

/**
 * Type for log level strings that can be used in configuration
 */
type LogLevelString = keyof typeof LogLevel;

/**
 * Worker-specific logger service that creates separate log files for each worker
 * with structured JSON logging and log level filtering.
 */
@Injectable()
export class WorkerLoggerService implements NestLoggerService {
  private context?: string;
  private logLevelValue: LogLevel;
  private workerId: number;
  private logsDir: string;
  private errorLogStream: fs.WriteStream | null = null;
  private successLogStream: fs.WriteStream | null = null;

  /**
   * Creates a new WorkerLoggerService instance
   * @param configService - The NestJS ConfigService for accessing configuration
   * @param workerId - The worker ID for creating worker-specific log files
   */
  constructor(
    private readonly configService: ConfigService,
    workerId: number = 0
  ) {
    this.workerId = workerId;
    
    // Get log level from config or use INFO as default
    const configLevelStr = this.configService.get<string>('LOG_LEVEL') || 'info';
    const configLevel = configLevelStr.toUpperCase() as LogLevelString;
    this.logLevelValue = LogLevel[configLevel] !== undefined ? LogLevel[configLevel] : LogLevel.INFO;
    
    // Set up logs directory
    this.logsDir = this.configService.get<string>('LOGS_DIR') || path.join(process.cwd(), 'logs');
    this.ensureLogsDirectory();
    this.initializeLogStreams();
  }

  /**
   * Ensures the logs directory exists
   */
  private ensureLogsDirectory(): void {
    if (!fs.existsSync(this.logsDir)) {
      fs.mkdirSync(this.logsDir, { recursive: true });
    }
  }

  /**
   * Initializes log file streams for the worker
   */
  private initializeLogStreams(): void {
    const errorLogPath = path.join(this.logsDir, `worker-${this.workerId}-error.log`);
    const successLogPath = path.join(this.logsDir, `worker-${this.workerId}-success.log`);

    try {
      this.errorLogStream = fs.createWriteStream(errorLogPath, { flags: 'a' });
      this.successLogStream = fs.createWriteStream(successLogPath, { flags: 'a' });
    } catch (error) {
      console.error(`Failed to initialize log streams for worker ${this.workerId}:`, error);
    }
  }

  /**
   * Sets the context for the logger
   * @param context - The context (usually class name) for log messages
   * @returns The logger instance for chaining
   */
  setContext(context: string): this {
    this.context = context;
    return this;
  }

  /**
   * Logs an informational message
   * @param message - The message to log
   * @param context - Optional context override
   */
  log(message: any, context?: string): void {
    this.logMessage(LogLevel.INFO, 'INFO', message, context, 'success');
  }

  /**
   * Logs an error message with optional stack trace
   * @param message - The error message
   * @param trace - Optional stack trace
   * @param context - Optional context override
   */
  error(message: any, trace?: string, context?: string): void {
    this.logMessage(LogLevel.ERROR, 'ERROR', message, context, 'error');
    if (trace) {
      this.logMessage(LogLevel.ERROR, 'ERROR', `Stack trace: ${trace}`, context, 'error');
    }
  }

  /**
   * Logs a warning message
   * @param message - The warning message
   * @param context - Optional context override
   */
  warn(message: any, context?: string): void {
    this.logMessage(LogLevel.WARN, 'WARN', message, context, 'success');
  }

  /**
   * Logs a debug message
   * @param message - The debug message
   * @param context - Optional context override
   */
  debug(message: any, context?: string): void {
    this.logMessage(LogLevel.DEBUG, 'DEBUG', message, context, 'success');
  }

  /**
   * Logs a verbose message
   * @param message - The verbose message
   * @param context - Optional context override
   */
  verbose(message: any, context?: string): void {
    this.logMessage(LogLevel.VERBOSE, 'VERBOSE', message, context, 'success');
  }

  /**
   * Logs a success message (SMS sent successfully)
   * @param message - The success message
   * @param context - Optional context override
   */
  success(message: any, context?: string): void {
    this.logMessage(LogLevel.INFO, 'SUCCESS', message, context, 'success');
  }

  /**
   * Logs a message if the log level is enabled
   * @param level - The numeric log level
   * @param levelString - The string representation of the log level
   * @param message - The message to log
   * @param context - Optional context override
   * @param logType - The type of log (error or success)
   */
  private logMessage(
    level: LogLevel, 
    levelString: string, 
    message: any, 
    context?: string, 
    logType: 'error' | 'success' = 'success'
  ): void {
    // Skip if this log level is not enabled
    if (level > this.logLevelValue) {
      return;
    }

    // Format the message as a string if it's not already
    const messageString = typeof message === 'object'
      ? JSON.stringify(message)
      : String(message);

    // Create structured log entry
    const timestamp = new Date().toISOString();
    const logContext = context || this.context || `Worker-${this.workerId}`;
    const correlationId = this.getCorrelationId();

    const logEntry = {
      timestamp,
      level: levelString,
      context: logContext,
      workerId: this.workerId,
      message: messageString,
      ...(correlationId ? { correlationId } : {}),
    };

    // Write to appropriate log file
    const logStream = logType === 'error' ? this.errorLogStream : this.successLogStream;
    if (logStream) {
      try {
        logStream.write(JSON.stringify(logEntry) + '\n');
      } catch (error) {
        console.error(`Failed to write to ${logType} log for worker ${this.workerId}:`, error);
      }
    }

    // Also output to console for development
    if (process.env.NODE_ENV !== 'production') {
      console.log(JSON.stringify(logEntry));
    }
  }

  /**
   * Gets the current correlation ID from async storage if available
   * @returns The correlation ID or undefined
   */
  private getCorrelationId(): string | undefined {
    // This is a placeholder for future implementation
    // In Phase 5, we'll implement proper correlation ID tracking
    return undefined;
  }

  /**
   * Closes the log streams
   */
  close(): void {
    if (this.errorLogStream) {
      this.errorLogStream.end();
      this.errorLogStream = null;
    }
    if (this.successLogStream) {
      this.successLogStream.end();
      this.successLogStream = null;
    }
  }
} 