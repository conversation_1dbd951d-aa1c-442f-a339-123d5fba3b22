import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Log levels in order of increasing verbosity
 */
enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  VERBOSE = 4
}

/**
 * Type for log level strings that can be used in configuration
 */
type LogLevelString = keyof typeof LogLevel;

/**
 * Enhanced Worker-specific logger service that creates separate log files for each worker
 * with structured JSON logging, log level filtering, and automatic file rotation.
 */
@Injectable()
export class EnhancedWorkerLoggerService {
  private readonly workerId: number;
  private readonly logsDir: string;
  private readonly logLevel: number;
  private readonly maxFileSize: number;
  private readonly nodeEnv: string;
  
  // Simplified file streams to prevent memory leaks
  private successStream: fs.WriteStream | null = null;
  private errorStream: fs.WriteStream | null = null;
  private combinedStream: fs.WriteStream | null = null;
  private performanceStream: fs.WriteStream | null = null;

  constructor(configService: ConfigService, workerId: number) {
    this.workerId = workerId;
    this.logsDir = configService.get<string>('LOGS_DIR') || './logs';
    this.logLevel = this.getLogLevel(configService.get<string>('LOG_LEVEL') || 'info');
    this.maxFileSize = parseInt(configService.get<string>('LOG_MAX_SIZE_MB') || '50') * 1024 * 1024;
    this.nodeEnv = configService.get<string>('NODE_ENV') || 'development';
    
    this.initializeLogDirectory();
    this.initializeStreams();
  }

  private getLogLevel(level: string): number {
    const levels = { error: 0, warn: 1, info: 2, debug: 3 };
    return levels[level as keyof typeof levels] ?? 2;
  }

  private initializeLogDirectory(): void {
    if (!fs.existsSync(this.logsDir)) {
      fs.mkdirSync(this.logsDir, { recursive: true });
    }
  }

  private initializeStreams(): void {
    const today = new Date().toISOString().split('T')[0];
    
    // Initialize file streams with error handling
    try {
      this.successStream = this.createWriteStream(`worker-${this.workerId}-success-${today}.log`);
      this.errorStream = this.createWriteStream(`worker-${this.workerId}-error-${today}.log`);
      this.combinedStream = this.createWriteStream(`worker-${this.workerId}-combined-${today}.log`);
      this.performanceStream = this.createWriteStream(`worker-${this.workerId}-performance-${today}.log`);
    } catch (error) {
      console.error(`Failed to initialize log streams for worker ${this.workerId}:`, error.message);
    }
  }

  private createWriteStream(filename: string): fs.WriteStream | null {
    try {
      const filePath = path.join(this.logsDir, filename);
      return fs.createWriteStream(filePath, { flags: 'a', encoding: 'utf8' });
    } catch (error) {
      console.error(`Failed to create write stream for ${filename}:`, error.message);
      return null;
    }
  }

  private writeToStream(stream: fs.WriteStream | null, message: string): void {
    if (!stream) return;
    
    try {
      // Check if stream is still writable
      if (stream.writable) {
        stream.write(message + '\n');
      }
    } catch (error) {
      // Silently handle stream errors to prevent crashes
      console.error(`Stream write error for worker ${this.workerId}:`, error.message);
    }
  }

  private formatLogEntry(level: string, message: string, data?: any): string {
    const timestamp = new Date().toISOString();
    const baseLog: any = {
      timestamp,
      level: level.toUpperCase(),
      context: `Worker-${this.workerId}`,
      workerId: this.workerId,
      message
    };

    // Simplified data handling to prevent memory issues
    let logData = baseLog;
    if (data && typeof data === 'object') {
      try {
        // Limit data size to prevent memory issues
        const limitedData = this.limitObjectSize(data, 1000);
        logData = { ...baseLog, ...limitedData };
      } catch (error) {
        logData = { ...baseLog, dataError: 'Data serialization failed' };
      }
    }

    return JSON.stringify(logData);
  }

  private limitObjectSize(obj: any, maxSize: number): any {
    if (typeof obj !== 'object' || obj === null) return obj;
    
    const result: any = {};
    let currentSize = 0;
    
    for (const [key, value] of Object.entries(obj)) {
      if (currentSize >= maxSize) break;
      
      if (typeof value === 'string') {
        result[key] = value.length > 200 ? value.substring(0, 200) + '...' : value;
      } else if (typeof value === 'object' && value !== null) {
        result[key] = this.limitObjectSize(value, 100);
      } else {
        result[key] = value;
      }
      
      currentSize += JSON.stringify(result[key]).length;
    }
    
    return result;
  }

  private writeLog(level: string, message: string, data?: any): void {
    if (this.getLogLevel(level) > this.logLevel) return;

    const logEntry = this.formatLogEntry(level, message, data);
    
    // Write to appropriate streams
    if (level === 'error') {
      this.writeToStream(this.errorStream, logEntry);
    } else if (level === 'success') {
      this.writeToStream(this.successStream, logEntry);
    }
    
    // Always write to combined stream
    this.writeToStream(this.combinedStream, logEntry);
    
    // Console output for development
    if (this.nodeEnv === 'development') {
      console.log(`[Worker-${this.workerId}] ${level.toUpperCase()}: ${message}`);
    }
  }

  log(message: string, data?: any): void {
    this.writeLog('info', message, data);
  }

  error(message: string, data?: any): void {
    this.writeLog('error', message, data);
  }

  warn(message: string, data?: any): void {
    this.writeLog('warn', message, data);
  }

  debug(message: string, data?: any): void {
    this.writeLog('debug', message, data);
  }

  success(message: string, data?: any): void {
    this.writeLog('success', message, data);
  }

  performance(data: any): void {
    if (!this.performanceStream) return;
    
    try {
      const timestamp = new Date().toISOString();
      const performanceLog = {
        timestamp,
        level: 'PERFORMANCE',
        context: `Worker-${this.workerId}`,
        workerId: this.workerId,
        ...this.limitObjectSize(data, 500)
      };
      
      this.writeToStream(this.performanceStream, JSON.stringify(performanceLog));
    } catch (error) {
      // Silently handle performance logging errors
      console.error(`Performance logging error for worker ${this.workerId}:`, error.message);
    }
  }

  // Cleanup method to properly close streams
  shutdown(): void {
    try {
      this.successStream?.end();
      this.errorStream?.end();
      this.combinedStream?.end();
      this.performanceStream?.end();
      
      this.successStream = null;
      this.errorStream = null;
      this.combinedStream = null;
      this.performanceStream = null;
    } catch (error) {
      console.error(`Error shutting down logger for worker ${this.workerId}:`, error.message);
    }
  }
} 