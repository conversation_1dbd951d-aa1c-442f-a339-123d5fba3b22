import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from './logger.service';

/**
 * Unit tests for the LoggerService
 */
describe('LoggerService', () => {
  let service: LoggerService;
  let configService: ConfigService;
  let consoleLogSpy: jest.SpyInstance;
  let consoleErrorSpy: jest.SpyInstance;

  beforeEach(async () => {
    // Spy on console methods before creating the service
    consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();
    consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

    // Create a testing module with our dependencies
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LoggerService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key: string) => {
              if (key === 'LOG_LEVEL') return 'debug';
              return undefined;
            }),
          },
        },
      ],
    }).compile();

    // Get the service instance
    service = module.get<LoggerService>(LoggerService);
    configService = module.get<ConfigService>(ConfigService);

    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Restore console methods
    if (consoleLogSpy) consoleLogSpy.mockRestore();
    if (consoleErrorSpy) consoleErrorSpy.mockRestore();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should log messages with the correct format', () => {
    // Arrange
    const message = 'Test message';
    const context = 'TestContext';

    // Act
    service.log(message, context);

    // Assert
    expect(consoleLogSpy).toHaveBeenCalledTimes(1);
    expect(consoleLogSpy).toHaveBeenCalledWith(
      expect.stringContaining('"level":"INFO"'),
    );
    expect(consoleLogSpy).toHaveBeenCalledWith(
      expect.stringContaining(`"context":"${context}"`),
    );
    expect(consoleLogSpy).toHaveBeenCalledWith(
      expect.stringContaining(`"message":"${message}"`),
    );
  });

  it('should log error messages with the correct format', () => {
    // Arrange
    const message = 'Test error';
    const context = 'TestContext';
    const trace = 'Error stack trace';

    // Act
    service.error(message, trace, context);

    // Assert
    expect(consoleLogSpy).toHaveBeenCalledTimes(1);
    expect(consoleLogSpy).toHaveBeenCalledWith(
      expect.stringContaining('"level":"ERROR"'),
    );
    expect(consoleErrorSpy).toHaveBeenCalledTimes(1);
    expect(consoleErrorSpy).toHaveBeenCalledWith(trace);
  });

  it('should use the default context if none is provided', () => {
    // Arrange
    const message = 'Test message';

    // Act
    service.log(message);

    // Assert
    expect(consoleLogSpy).toHaveBeenCalledWith(
      expect.stringContaining('"context":"Application"'),
    );
  });

  it('should use the context set via setContext', () => {
    // Arrange
    const message = 'Test message';
    const context = 'SetContext';

    // Act
    service.setContext(context);
    service.log(message);

    // Assert
    expect(consoleLogSpy).toHaveBeenCalledWith(
      expect.stringContaining(`"context":"${context}"`),
    );
  });

  it('should respect log level configuration', () => {
    // Arrange - clear previous calls and reconfigure the service with a different log level
    consoleLogSpy.mockClear();
    jest.spyOn(configService, 'get').mockReturnValue('info');

    // Create a new service instance with the mocked config
    const newService = new LoggerService(configService);

    // Act
    newService.debug('This should not be logged');
    newService.log('This should be logged');

    // Assert
    expect(consoleLogSpy).toHaveBeenCalledTimes(1);
    expect(consoleLogSpy).toHaveBeenCalledWith(
      expect.stringContaining('"level":"INFO"'),
    );
  });
});
