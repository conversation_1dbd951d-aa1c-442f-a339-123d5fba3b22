import { Injectable, NestMiddleware, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';
import { ExtendedRequest } from '../interfaces/request.interface';

/**
 * Simple in-memory rate limiter middleware
 * For production, consider using a distributed rate limiter with Redis
 */
@Injectable()
export class RateLimiterMiddleware implements NestMiddleware {
  private readonly logger = new Logger('RateLimiter');
  private readonly requestMap = new Map<string, { count: number; resetTime: number }>();

  // Default values
  private readonly defaultLimit = 100; // requests per window

  constructor(private readonly configService: ConfigService) {}

  use(req: ExtendedRequest, res: Response, next: NextFunction) {
    // Get configuration from ConfigService
    const limit = this.configService.get<number>('api.throttle.limit', this.defaultLimit);
    const ttl = this.configService.get<number>('api.throttle.ttl', 60); // seconds
    const window = ttl * 1000; // convert to milliseconds

    this.logger.debug(`Rate limiter applied with limit: ${limit}, window: ${ttl}s`);

    // Get client identifier (IP address or API key if available)
    const clientId = this.getClientIdentifier(req);

    // Get current time
    const now = Date.now();

    // Get or create client record
    let clientRecord = this.requestMap.get(clientId);

    // If no record exists or the window has expired, create a new one
    if (!clientRecord || now > clientRecord.resetTime) {
      clientRecord = {
        count: 0,
        resetTime: now + window,
      };
    }

    // Increment request count
    clientRecord.count++;

    // Update the record
    this.requestMap.set(clientId, clientRecord);

    // Log rate limit information at debug level
    if (clientRecord.count % 10 === 0 || clientRecord.count > limit - 3) {
      // Only log every 10th request or when approaching the limit
      this.logger.debug(
        `Rate limit for ${clientId}: ${clientRecord.count}/${limit} requests, ` +
        `resets at ${new Date(clientRecord.resetTime).toISOString()}`
      );
    }

    // Set rate limit headers
    res.setHeader('X-RateLimit-Limit', limit.toString());
    res.setHeader('X-RateLimit-Remaining', Math.max(0, limit - clientRecord.count).toString());
    res.setHeader('X-RateLimit-Reset', Math.ceil(clientRecord.resetTime / 1000).toString());

    // Check if rate limit is exceeded
    if (clientRecord.count > limit) {
      this.logger.warn(`Rate limit exceeded for client ${clientId}: ${clientRecord.count}/${limit}`);

      // Set retry-after header
      const retryAfter = Math.ceil((clientRecord.resetTime - now) / 1000);
      res.setHeader('Retry-After', retryAfter.toString());

      throw new HttpException(
        {
          statusCode: HttpStatus.TOO_MANY_REQUESTS,
          message: 'Rate limit exceeded',
          retryAfter,
        },
        HttpStatus.TOO_MANY_REQUESTS,
      );
    }

    next();
  }

  /**
   * Get a unique identifier for the client
   * Uses API key if available, otherwise falls back to IP address
   */
  private getClientIdentifier(req: ExtendedRequest): string {
    // Include request ID in logs for traceability
    const requestId = req.requestId;

    // Use API key if available (from header or query parameter)
    const apiKey = req.headers['x-api-key'] || req.query.api_key;
    if (apiKey) {
      this.logger.debug(`[${requestId}] Using API key for rate limiting`);
      return `api-key:${apiKey}`;
    }

    // Use IP address as fallback
    const xForwardedFor = req.headers['x-forwarded-for'] as string;
    if (xForwardedFor) {
      const ip = xForwardedFor.split(',')[0];
      this.logger.debug(`[${requestId}] Using forwarded IP ${ip} for rate limiting`);
      return `ip:${ip}`;
    }

    this.logger.debug(`[${requestId}] Using direct IP ${req.ip || 'unknown'} for rate limiting`);
    return `ip:${req.ip || 'unknown'}`;
  }

  /**
   * Clean up expired records periodically
   * This should be called from a scheduled task
   */
  public cleanUp(): void {
    const now = Date.now();
    let expiredCount = 0;

    for (const [clientId, record] of this.requestMap.entries()) {
      if (now > record.resetTime) {
        this.requestMap.delete(clientId);
        expiredCount++;
      }
    }

    if (expiredCount > 0) {
      this.logger.debug(`Cleaned up ${expiredCount} expired rate limit records`);
    }
  }
}
