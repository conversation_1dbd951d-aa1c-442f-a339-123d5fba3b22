import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { ExtendedRequest } from '../interfaces/request.interface';

/**
 * Middleware to log HTTP requests and add a unique request ID
 * This helps with tracing requests through the system
 */
@Injectable()
export class RequestLoggerMiddleware implements NestMiddleware {
  private readonly logger = new Logger('HTTP');

  use(req: ExtendedRequest, res: Response, next: NextFunction) {
    // Generate a unique request ID if not already present
    const requestId = req.headers['x-request-id'] as string || uuidv4();

    // Add the request ID to the request object for use in controllers
    req.requestId = requestId;

    // Add the request ID to response headers
    res.setHeader('X-Request-ID', requestId);

    // Get the request start time
    const startTime = Date.now();

    // Log the incoming request
    this.logger.log(
      `[${requestId}] ${req.method} ${req.originalUrl} - ${this.getClientIp(req)}`,
    );

    // Log request body for POST/PUT/PATCH requests (excluding sensitive data)
    if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
      const sanitizedBody = this.sanitizeRequestBody(req.body);
      this.logger.debug(
        `[${requestId}] Request Body: ${JSON.stringify(sanitizedBody)}`,
      );
    }

    // Log the response when it's sent
    res.on('finish', () => {
      const duration = Date.now() - startTime;
      const logMethod = res.statusCode >= 400 ? 'warn' : 'log';

      this.logger[logMethod](
        `[${requestId}] ${req.method} ${req.originalUrl} ${res.statusCode} - ${duration}ms`,
      );
    });

    next();
  }

  /**
   * Get the client IP address
   */
  private getClientIp(req: ExtendedRequest): string {
    // Get IP from various headers that might contain the real client IP
    const xForwardedFor = req.headers['x-forwarded-for'] as string;
    if (xForwardedFor) {
      return xForwardedFor.split(',')[0];
    }

    return req.ip || 'unknown';
  }

  /**
   * Sanitize the request body to remove sensitive information
   */
  private sanitizeRequestBody(body: any): any {
    if (!body) return {};

    // Create a deep copy of the body
    const sanitized = JSON.parse(JSON.stringify(body));

    // List of sensitive fields to mask
    const sensitiveFields = [
      'password', 'pwd', 'token', 'apiKey', 'api_key', 'secret',
      'credential', 'credentials', 'auth', 'authentication'
    ];

    // Recursively sanitize the body
    this.recursiveSanitize(sanitized, sensitiveFields);

    return sanitized;
  }

  /**
   * Recursively sanitize an object to mask sensitive fields
   */
  private recursiveSanitize(obj: any, sensitiveFields: string[]): void {
    if (!obj || typeof obj !== 'object') return;

    Object.keys(obj).forEach(key => {
      // Check if this is a sensitive field
      const lowerKey = key.toLowerCase();
      if (sensitiveFields.some(field => lowerKey.includes(field))) {
        // Mask the value
        obj[key] = typeof obj[key] === 'string'
          ? '***MASKED***'
          : obj[key] ? { masked: true } : null;
      }
      // Recursively sanitize nested objects
      else if (typeof obj[key] === 'object' && obj[key] !== null) {
        this.recursiveSanitize(obj[key], sensitiveFields);
      }
    });
  }
}
