import { Injectable, NestMiddleware, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';
import { ExtendedRequest } from '../interfaces/request.interface';

/**
 * Middleware to validate API authentication token
 * This ensures that only authorized clients can access the API
 */
@Injectable()
export class AuthTokenMiddleware implements NestMiddleware {
  private readonly logger = new Logger('AuthToken');
  private readonly apiToken: string;
  private readonly headerName: string = 'x-api-token';

  constructor(private readonly configService: ConfigService) {
    // Get the API token from configuration
    this.apiToken = this.configService.get<string>('api.auth.token') || '';

    // Also check the raw environment variable
    const rawToken = process.env.API_AUTH_TOKEN;

    console.log('=== AUTH TOKEN MIDDLEWARE INITIALIZATION ===');
    console.log(`Raw API_AUTH_TOKEN: ${rawToken}`);
    console.log(`ConfigService api.auth.token: ${this.apiToken}`);

    if (!this.apiToken) {
      this.logger.warn(
        'API token is not configured. Set API_AUTH_TOKEN in your environment variables.',
      );
    } else {
      this.logger.log(
        `API token authentication is enabled with token: ${this.apiToken.substring(0, 4)}...`,
      );
    }
  }

  use(req: ExtendedRequest, res: Response, next: NextFunction) {
    console.log(`=== AUTH TOKEN MIDDLEWARE CALLED ===`);
    console.log(`Path: ${req.originalUrl}`);
    console.log(`Method: ${req.method}`);
    console.log(`API Token configured: ${this.apiToken ? 'YES' : 'NO'}`);

    // Skip token validation if no token is configured
    if (!this.apiToken) {
      console.log('No API token configured, skipping validation');
      this.logger.warn(
        `Request to ${req.method} ${req.originalUrl} allowed without token (no token configured)`,
      );
      return next();
    }

    // Get the token from the request header or query parameter
    const token = this.getTokenFromRequest(req);
    console.log(`Token in request: ${token ? 'YES' : 'NO'}`);

    if (token) {
      console.log(`Token match: ${token === this.apiToken ? 'YES' : 'NO'}`);
    }

    // If no token is provided, return 401 Unauthorized
    if (!token) {
      console.log('No token provided, returning 401 Unauthorized');
      this.logger.warn(
        `Unauthorized request to ${req.method} ${req.originalUrl} - No token provided`,
      );
      throw new HttpException(
        {
          statusCode: HttpStatus.UNAUTHORIZED,
          message: 'API token is required',
        },
        HttpStatus.UNAUTHORIZED,
      );
    }

    // Validate the token
    if (token !== this.apiToken) {
      console.log('Invalid token provided, returning 401 Unauthorized');
      this.logger.warn(`Unauthorized request to ${req.method} ${req.originalUrl} - Invalid token`);
      throw new HttpException(
        {
          statusCode: HttpStatus.UNAUTHORIZED,
          message: 'Invalid API token',
        },
        HttpStatus.UNAUTHORIZED,
      );
    }

    // Token is valid, proceed with the request
    console.log('Token is valid, proceeding with request');
    this.logger.debug(`Authenticated request to ${req.method} ${req.originalUrl}`);
    next();
  }

  /**
   * Get the token from the request
   * Checks both the header and query parameter
   */
  private getTokenFromRequest(req: ExtendedRequest): string | undefined {
    console.log('=== CHECKING FOR TOKEN IN REQUEST ===');

    // Log all headers for debugging
    console.log('Headers:');
    Object.entries(req.headers).forEach(([key, value]) => {
      console.log(`  ${key}: ${value}`);
    });

    // Log all query parameters for debugging
    console.log('Query parameters:');
    Object.entries(req.query).forEach(([key, value]) => {
      console.log(`  ${key}: ${value}`);
    });

    // Check Authorization header for Bearer token
    const authHeader = req.headers['authorization'] as string;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      console.log(`TOKEN: ${authHeader.slice(7).trim()}`);
      return authHeader.slice(7).trim();
    }

    // Check header first (preferred method)
    const headerToken = req.headers[this.headerName] as string;
    if (headerToken) {
      console.log(`Found token in header: ${this.headerName}`);
      return headerToken;
    }

    // Check query parameter as fallback
    const queryToken = req.query.token as string;
    if (queryToken) {
      console.log(`Found token in query parameter: token`);
      return queryToken;
    }

    // Check body for token
    const bodyToken = req.body?.token as string;
    if (bodyToken) {
      console.log(`Found token in body: ${bodyToken}`);
      return bodyToken;
    }

    // No token found
    console.log('No token found in request');
    return undefined;
  }
}
