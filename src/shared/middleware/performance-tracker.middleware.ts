import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Response, NextFunction } from 'express';
import { MetricsService } from '../services/metrics.service';
import { ExtendedRequest } from '../interfaces/request.interface';

/**
 * Middleware to track performance metrics for HTTP requests
 * This helps with monitoring system performance and identifying bottlenecks
 */
@Injectable()
export class PerformanceTrackerMiddleware implements NestMiddleware {
  private readonly logger = new Logger('Performance');

  constructor(private readonly metricsService: MetricsService) {}

  use(req: ExtendedRequest, res: Response, next: NextFunction) {
    // Get the request start time
    const startTime = Date.now();

    // Get the request path (normalize to remove query parameters)
    const path = this.normalizePath(req.originalUrl);

    // Get request ID for correlation
    const requestId = req.requestId;

    // Track the request
    this.metricsService.incrementCounter('http_requests_total', {
      method: req.method,
      path,
      requestId,
    });

    // Track response time and status code when the response is sent
    res.on('finish', () => {
      const duration = Date.now() - startTime;
      const statusCode = res.statusCode.toString();
      const statusCategory = Math.floor(res.statusCode / 100) + 'xx';

      // Record response time
      this.metricsService.recordHistogram('http_request_duration_ms', duration, {
        method: req.method,
        path,
        status: statusCategory,
        requestId,
      });

      // Track status codes
      this.metricsService.incrementCounter('http_responses_total', {
        method: req.method,
        path,
        status: statusCode,
        category: statusCategory,
        requestId,
      });

      // Log slow requests (over 1000ms)
      if (duration > 1000) {
        this.logger.warn(
          `Slow request: ${req.method} ${path} ${res.statusCode} - ${duration}ms`,
        );
      }
    });

    next();
  }

  /**
   * Normalize the path to remove query parameters and standardize patterns
   * This helps reduce cardinality in metrics
   */
  private normalizePath(url: string): string {
    // Remove query parameters
    const path = url.split('?')[0];

    // Replace numeric IDs with placeholders
    // Example: /users/123 -> /users/:id
    return path.replace(/\/\d+/g, '/:id');
  }


}
