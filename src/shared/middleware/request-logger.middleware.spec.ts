import { Test } from '@nestjs/testing';
import { RequestLoggerMiddleware } from './request-logger.middleware';
import { Logger } from '@nestjs/common';

describe('RequestLoggerMiddleware', () => {
  let middleware: RequestLoggerMiddleware;
  let mockRequest: any;
  let mockResponse: any;
  let mockNext: jest.Mock;

  beforeEach(async () => {
    // Create a mock logger
    const mockLogger = {
      log: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
    };

    // Create the testing module
    const moduleRef = await Test.createTestingModule({
      providers: [
        RequestLoggerMiddleware,
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    // Get the middleware instance
    middleware = moduleRef.get<RequestLoggerMiddleware>(RequestLoggerMiddleware);

    // Create mock request, response, and next function
    mockRequest = {
      method: 'GET',
      originalUrl: '/api/v1/test',
      ip: '127.0.0.1',
      headers: {},
      body: {},
    };

    mockResponse = {
      setHeader: jest.fn(),
      on: jest.fn(),
      statusCode: 200,
    };

    mockNext = jest.fn();
  });

  it('should add a request ID to the request and response', () => {
    // Call the middleware
    middleware.use(mockRequest, mockResponse, mockNext);

    // Check that a request ID was added
    expect(mockRequest.requestId).toBeDefined();
    expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Request-ID', mockRequest.requestId);
  });

  it('should use an existing request ID if provided', () => {
    // Set a request ID in the headers
    const existingRequestId = 'test-request-id';
    mockRequest.headers['x-request-id'] = existingRequestId;

    // Call the middleware
    middleware.use(mockRequest, mockResponse, mockNext);

    // Check that the existing request ID was used
    expect(mockRequest.requestId).toBe(existingRequestId);
    expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Request-ID', existingRequestId);
  });

  it('should call next()', () => {
    // Call the middleware
    middleware.use(mockRequest, mockResponse, mockNext);

    // Check that next was called
    expect(mockNext).toHaveBeenCalled();
  });

  it('should sanitize sensitive fields in the request body', () => {
    // Set a request with sensitive data
    mockRequest.method = 'POST';
    mockRequest.body = {
      username: 'testuser',
      password: 'secret',
      data: {
        token: 'sensitive-token',
        message: 'Hello, world!',
      },
    };

    // Create a spy on the logger debug method
    const loggerSpy = jest.spyOn((middleware as any).logger, 'debug');

    // Call the middleware
    middleware.use(mockRequest, mockResponse, mockNext);

    // Check that sensitive data was sanitized
    expect(loggerSpy).toHaveBeenCalled();

    // Get the logged message
    const loggedMessage = loggerSpy.mock.calls[0][0] as string;

    // Extract the JSON part
    const jsonStart = loggedMessage.indexOf('Request Body: ') + 'Request Body: '.length;
    const jsonString = loggedMessage.substring(jsonStart);

    // Parse the JSON
    const loggedBody = JSON.parse(jsonString);

    // Verify sanitization
    expect(loggedBody.password).toBe('***MASKED***');
    expect(loggedBody.data.token).toBe('***MASKED***');
    expect(loggedBody.username).toBe('testuser');
    expect(loggedBody.data.message).toBe('Hello, world!');
  });
});
