import { ConfigService } from '@nestjs/config';

/**
 * Performance configuration for high-throughput SMS processing
 * Optimized for 5K SMS per second
 */
export class PerformanceConfig {
  private readonly configService: ConfigService;

  constructor(configService: ConfigService) {
    this.configService = configService;
  }

  /**
   * Get optimal worker count based on CPU cores and target throughput
   */
  getWorkerCount(): number {
    const cpuCores = require('os').cpus().length;
    const targetThroughput = this.getTargetThroughput();
    const messagesPerWorkerPerSecond = this.getMessagesPerWorkerPerSecond();
    
    // Calculate optimal worker count for 5K SMS/second
    const optimalWorkers = Math.ceil(targetThroughput / messagesPerWorkerPerSecond);
    
    // Use CPU cores as base, but scale up for high throughput
    const maxWorkers = Math.max(cpuCores * 4, optimalWorkers);
    
    return Math.min(maxWorkers, 50); // Increased cap to 50 workers
  }

  /**
   * Get target throughput (SMS per second)
   */
  getTargetThroughput(): number {
    return parseInt(this.configService.get<string>('SMS_TARGET_THROUGHPUT') || '5000');
  }

  /**
   * Get estimated messages per worker per second
   */
  getMessagesPerWorkerPerSecond(): number {
    return parseInt(this.configService.get<string>('SMS_PER_WORKER_PER_SECOND') || '200');
  }

  /**
   * Get RabbitMQ connection pool size
   */
  getRabbitMQPoolSize(): number {
    return parseInt(this.configService.get<string>('RABBITMQ_POOL_SIZE') || '20');
  }

  /**
   * Get HTTP client timeout
   */
  getHttpTimeout(): number {
    return parseInt(this.configService.get<string>('HTTP_TIMEOUT_MS') || '3000');
  }

  /**
   * Get HTTP client max connections
   */
  getHttpMaxConnections(): number {
    return parseInt(this.configService.get<string>('HTTP_MAX_CONNECTIONS') || '200');
  }

  /**
   * Get queue prefetch count
   */
  getQueuePrefetchCount(): number {
    return parseInt(this.configService.get<string>('QUEUE_PREFETCH_COUNT') || '50');
  }

  /**
   * Get memory limit for workers
   */
  getWorkerMemoryLimit(): number {
    return parseInt(this.configService.get<string>('WORKER_MEMORY_LIMIT_MB') || '256');
  }

  /**
   * Get heartbeat interval
   */
  getHeartbeatInterval(): number {
    return parseInt(this.configService.get<string>('WORKER_HEARTBEAT_INTERVAL_MS') || '3000');
  }

  /**
   * Get retry configuration
   */
  getRetryConfig() {
    return {
      maxRetries: parseInt(this.configService.get<string>('SMS_MAX_RETRIES') || '2'),
      retryDelay: parseInt(this.configService.get<string>('SMS_RETRY_DELAY_MS') || '500'),
      backoffMultiplier: parseFloat(this.configService.get<string>('SMS_BACKOFF_MULTIPLIER') || '1.5'),
    };
  }

  /**
   * Get logging configuration
   */
  getLoggingConfig() {
    return {
      level: this.configService.get<string>('LOG_LEVEL') || 'warn',
      enableWorkerLogs: this.configService.get<string>('ENABLE_WORKER_LOGS') === 'true',
      logRotation: {
        maxSize: this.configService.get<string>('LOG_MAX_SIZE_MB') || '50',
        maxFiles: this.configService.get<string>('LOG_MAX_FILES') || '5',
      },
    };
  }

  /**
   * Get performance monitoring configuration
   */
  getMonitoringConfig() {
    return {
      enableMetrics: this.configService.get<string>('ENABLE_METRICS') === 'true',
      metricsInterval: parseInt(this.configService.get<string>('METRICS_INTERVAL_MS') || '3000'),
      enableHealthChecks: this.configService.get<string>('ENABLE_HEALTH_CHECKS') === 'true',
    };
  }

  /**
   * Get batch processing configuration
   */
  getBatchConfig() {
    return {
      batchSize: parseInt(this.configService.get<string>('SMS_BATCH_SIZE') || '10'),
      batchTimeout: parseInt(this.configService.get<string>('SMS_BATCH_TIMEOUT_MS') || '100'),
      enableBatching: this.configService.get<string>('ENABLE_SMS_BATCHING') === 'true',
    };
  }

  /**
   * Get connection pooling configuration
   */
  getConnectionPoolConfig() {
    return {
      maxConnections: parseInt(this.configService.get<string>('MAX_CONNECTIONS') || '100'),
      connectionTimeout: parseInt(this.configService.get<string>('CONNECTION_TIMEOUT_MS') || '5000'),
      keepAlive: this.configService.get<string>('KEEP_ALIVE') === 'true',
      keepAliveMsecs: parseInt(this.configService.get<string>('KEEP_ALIVE_MSECS') || '1000'),
    };
  }
} 