// shared/config/configuration.ts
export default () => ({
  rabbitmq: {
    hostname: process.env.RABBITMQ_HOST || 'localhost',
    port: parseInt(process.env.RABBITMQ_PORT || '5672'),
    username: process.env.RABBITMQ_USER || 'guest',
    password: process.env.RABBITMQ_PASSWORD || 'guest',
    url: process.env.RABBITMQ_URL || 'amqp://localhost:5672',
  },
  sms: {
    retries: parseInt(process.env.SMS_MAX_RETRIES || '3'),
    providers: {
      primary: process.env.SMS_PROVIDER_PRIMARY || 'default',
      fallback: process.env.SMS_PROVIDER_FALLBACK || 'none',
    },
    queues: {
      single: process.env.QUEUE_SMS_SINGLE || 'sms_single_queue',
      bulk: process.env.QUEUE_SMS_BULK || 'sms_bulk_queue',
      failed: process.env.QUEUE_SMS_FAILED || 'sms_failed_queue',
      dlr: process.env.QUEUE_SMS_DLR || 'sms_dlr_queue',
    },
    workers: {
      single_count: parseInt(process.env.SINGLE_SMS_WORKER_COUNT || '0'), // 0 means auto (use CPU count)
      bulk_count: parseInt(process.env.BULK_SMS_WORKER_COUNT || '0'), // 0 means auto (use CPU count)
      categories: (
        process.env.SMS_WORKER_CATEGORIES || 'default,bulk,marketing,transactional,high-priority'
      ).split(','),
      throughputPerSecond: parseInt(process.env.WORKER_SMS_THROUGHPUT_PER_SECOND || '5000'),
      maxConcurrentMessages: parseInt(process.env.WORKER_MAX_CONCURRENT_MESSAGES || '100'),
    },
  },
  api: {
    throttle: {
      enabled: process.env.API_THROTTLE_ENABLED === 'true',
      ttl: parseInt(process.env.API_THROTTLE_TTL || '60'),
      limit: parseInt(process.env.API_THROTTLE_LIMIT || '100'),
    },
    auth: {
      token: process.env.API_AUTH_TOKEN,
      enabled: !!process.env.API_AUTH_TOKEN,
    },
    backendRestfulApi: process.env.PL_RESTFULL_API_URL,
    teleJasminApi: process.env.TELE_JASMINE_API_URL,
    safaricomJasminApi: process.env.SAFARICOM_JASMINE_API_URL,
    smppNestJsApi: process.env.SAFARICOM_JASMINE_API_URL,
  },
  persistence: {
    enabled: process.env.ENABLE_FAILED_SMS_PERSISTENCE === 'true',
    failedSmsPath: process.env.FAILED_SMS_STORAGE_PATH || './data/failed_sms',
    pendingSmsPath: process.env.PENDING_SMS_STORAGE_PATH || './data/pending_sms',
  },
});
