import { Test, TestingModule } from '@nestjs/testing';
import { MetricsService } from '../services/metrics.service';
import { Logger } from '@nestjs/common';

describe('MetricsService', () => {
  let service: MetricsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [MetricsService],
    }).compile();

    service = module.get<MetricsService>(MetricsService);
    
    // Mock the logger to avoid console output during tests
    jest.spyOn(Logger.prototype, 'log').mockImplementation(() => {});
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('recordMessageReceived', () => {
    it('should increment total messages and single messages', () => {
      service.recordMessageReceived('single');
      const metrics = service.getMetrics();
      expect(metrics.totalMessages).toBe(1);
      expect(metrics.singleMessages).toBe(1);
      expect(metrics.bulkMessages).toBe(0);
    });

    it('should increment total messages and bulk messages', () => {
      service.recordMessageReceived('bulk');
      const metrics = service.getMetrics();
      expect(metrics.totalMessages).toBe(1);
      expect(metrics.singleMessages).toBe(0);
      expect(metrics.bulkMessages).toBe(1);
    });
  });

  describe('recordMessageSuccess', () => {
    it('should increment successful messages and record processing time', () => {
      service.recordMessageSuccess(100);
      const metrics = service.getMetrics();
      expect(metrics.successfulMessages).toBe(1);
      expect(metrics.processingTimes).toContain(100);
      expect(metrics.averageProcessingTime).toBe(100);
    });

    it('should calculate average processing time correctly', () => {
      service.recordMessageSuccess(100);
      service.recordMessageSuccess(200);
      const metrics = service.getMetrics();
      expect(metrics.successfulMessages).toBe(2);
      expect(metrics.processingTimes).toEqual([100, 200]);
      expect(metrics.averageProcessingTime).toBe(150);
    });
  });

  describe('recordMessageFailure', () => {
    it('should increment failed messages and error counts', () => {
      service.recordMessageFailure('NETWORK_ERROR');
      const metrics = service.getMetrics();
      expect(metrics.failedMessages).toBe(1);
      expect(metrics.errorsByType['NETWORK_ERROR']).toBe(1);
    });

    it('should track retry metrics', () => {
      service.recordMessageFailure('NETWORK_ERROR', true, false);
      const metrics = service.getMetrics();
      expect(metrics.retryAttempts).toBe(1);
      expect(metrics.maxRetriesReached).toBe(0);
    });

    it('should track max retries reached', () => {
      service.recordMessageFailure('NETWORK_ERROR', true, true);
      const metrics = service.getMetrics();
      expect(metrics.retryAttempts).toBe(1);
      expect(metrics.maxRetriesReached).toBe(1);
    });
  });

  describe('updateQueueDepth', () => {
    it('should update queue depth for known queues', () => {
      service.updateQueueDepth('sms_single_queue', 10);
      const metrics = service.getMetrics();
      expect(metrics.queueDepths['sms_single_queue']).toBe(10);
    });

    it('should ignore unknown queues', () => {
      service.updateQueueDepth('unknown_queue', 10);
      const metrics = service.getMetrics();
      expect(metrics.queueDepths['unknown_queue']).toBeUndefined();
    });
  });

  describe('logMetrics', () => {
    it('should log metrics', () => {
      const logSpy = jest.spyOn(Logger.prototype, 'log');
      service.recordMessageReceived('single');
      service.recordMessageSuccess(100);
      service.recordMessageFailure('NETWORK_ERROR');
      service.logMetrics();
      expect(logSpy).toHaveBeenCalled();
    });
  });

  describe('resetMetrics', () => {
    it('should reset all metrics', () => {
      service.recordMessageReceived('single');
      service.recordMessageSuccess(100);
      service.recordMessageFailure('NETWORK_ERROR');
      service.updateQueueDepth('sms_single_queue', 10);
      
      service.resetMetrics();
      
      const metrics = service.getMetrics();
      expect(metrics.totalMessages).toBe(0);
      expect(metrics.successfulMessages).toBe(0);
      expect(metrics.failedMessages).toBe(0);
      expect(metrics.singleMessages).toBe(0);
      expect(metrics.bulkMessages).toBe(0);
      expect(metrics.retryAttempts).toBe(0);
      expect(metrics.maxRetriesReached).toBe(0);
      expect(metrics.errorsByType).toEqual({});
      expect(metrics.processingTimes).toEqual([]);
      expect(metrics.averageProcessingTime).toBe(0);
      expect(metrics.queueDepths['sms_single_queue']).toBe(0);
    });
  });
});
