import { Test, TestingModule } from '@nestjs/testing';
import { ErrorHandlerService, ErrorType } from '../services/error-handler.service';
import { Logger } from '@nestjs/common';

describe('ErrorHandlerService', () => {
  let service: ErrorHandlerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ErrorHandlerService],
    }).compile();

    service = module.get<ErrorHandlerService>(ErrorHandlerService);
    
    // Mock the logger to avoid console output during tests
    jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {});
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('classifyError', () => {
    it('should classify network errors', () => {
      const error = new Error('Network connection failed');
      expect(service.classifyError(error)).toBe(ErrorType.TEMPORARY_NETWORK);
    });

    it('should classify service errors', () => {
      const error = new Error('Service unavailable');
      expect(service.classifyError(error)).toBe(ErrorType.TEMPORARY_SERVICE);
    });

    it('should classify rate limit errors', () => {
      const error = new Error('Rate limit exceeded');
      expect(service.classifyError(error)).toBe(ErrorType.RATE_LIMIT);
    });

    it('should classify timeout errors', () => {
      const error = new Error('Request timed out');
      expect(service.classifyError(error)).toBe(ErrorType.TIMEOUT);
    });

    it('should classify authentication errors', () => {
      const error = new Error('Invalid credentials');
      expect(service.classifyError(error)).toBe(ErrorType.INVALID_CREDENTIALS);
    });

    it('should classify recipient errors', () => {
      const error = new Error('Invalid phone number');
      expect(service.classifyError(error)).toBe(ErrorType.INVALID_RECIPIENT);
    });

    it('should classify message errors', () => {
      const error = new Error('Invalid message content');
      expect(service.classifyError(error)).toBe(ErrorType.INVALID_MESSAGE);
    });

    it('should classify billing errors', () => {
      const error = new Error('Insufficient funds');
      expect(service.classifyError(error)).toBe(ErrorType.INSUFFICIENT_FUNDS);
    });

    it('should classify unknown errors', () => {
      const error = new Error('Some random error');
      expect(service.classifyError(error)).toBe(ErrorType.UNKNOWN);
    });
  });

  describe('shouldRetry', () => {
    it('should not retry if max retries reached', () => {
      expect(service.shouldRetry(ErrorType.TEMPORARY_NETWORK, 3, 3)).toBe(false);
    });

    it('should not retry permanent errors', () => {
      expect(service.shouldRetry(ErrorType.INVALID_CREDENTIALS, 0, 3)).toBe(false);
      expect(service.shouldRetry(ErrorType.INVALID_RECIPIENT, 0, 3)).toBe(false);
      expect(service.shouldRetry(ErrorType.INVALID_MESSAGE, 0, 3)).toBe(false);
      expect(service.shouldRetry(ErrorType.INSUFFICIENT_FUNDS, 0, 3)).toBe(false);
    });

    it('should retry temporary errors', () => {
      expect(service.shouldRetry(ErrorType.TEMPORARY_NETWORK, 0, 3)).toBe(true);
      expect(service.shouldRetry(ErrorType.TEMPORARY_SERVICE, 0, 3)).toBe(true);
      expect(service.shouldRetry(ErrorType.RATE_LIMIT, 0, 3)).toBe(true);
      expect(service.shouldRetry(ErrorType.TIMEOUT, 0, 3)).toBe(true);
      expect(service.shouldRetry(ErrorType.UNKNOWN, 0, 3)).toBe(true);
    });
  });

  describe('calculateRetryDelay', () => {
    it('should use exponential backoff', () => {
      expect(service.calculateRetryDelay(ErrorType.TEMPORARY_NETWORK, 0)).toBeGreaterThanOrEqual(1000);
      expect(service.calculateRetryDelay(ErrorType.TEMPORARY_NETWORK, 1)).toBeGreaterThanOrEqual(2000);
      expect(service.calculateRetryDelay(ErrorType.TEMPORARY_NETWORK, 2)).toBeGreaterThanOrEqual(4000);
    });

    it('should add longer delays for rate limit errors', () => {
      const regularDelay = service.calculateRetryDelay(ErrorType.TEMPORARY_NETWORK, 1);
      const rateLimitDelay = service.calculateRetryDelay(ErrorType.RATE_LIMIT, 1);
      expect(rateLimitDelay).toBeGreaterThan(regularDelay);
    });

    it('should add longer delays for service errors', () => {
      const regularDelay = service.calculateRetryDelay(ErrorType.TEMPORARY_NETWORK, 1);
      const serviceDelay = service.calculateRetryDelay(ErrorType.TEMPORARY_SERVICE, 1);
      expect(serviceDelay).toBeGreaterThan(regularDelay);
    });
  });

  describe('logError', () => {
    it('should log errors with context', () => {
      const logSpy = jest.spyOn(Logger.prototype, 'error');
      const error = new Error('Test error');
      const errorType = ErrorType.TEMPORARY_NETWORK;
      const context = {
        messageId: 'test-123',
        requestId: 'req-456',
        messageType: 'single' as const,
        retryCount: 1,
        provider: 'test-provider',
        recipient: '+**********',
        timestamp: new Date(),
      };

      service.logError(error, errorType, context);
      expect(logSpy).toHaveBeenCalled();
    });
  });
});
