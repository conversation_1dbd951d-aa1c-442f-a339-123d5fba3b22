import { ExceptionFilter, Catch, ArgumentsHost, HttpException, Logger } from '@nestjs/common';
import { Request, Response } from 'express';

/**
 * Global HTTP exception filter that catches all HttpExceptions
 * and formats them into a consistent JSON response structure.
 *
 * This filter handles all exceptions that extend HttpException
 * and provides detailed error information including timestamp,
 * path, method, and the error message.
 */

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  /**
   * Catches and processes HTTP exceptions
   *
   * @param exception - The caught HttpException
   * @param host - The execution context of the current request
   */

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();
    const errorResponse = exception.getResponse();

    // Extract error message from the exception response
    // It can be either a string or an object with a message property
    const errorMessage =
      typeof errorResponse === 'object' && 'message' in errorResponse
        ? errorResponse['message']
        : exception.message;

    // Create a standardized error response object
    const errorObj = {
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
      message: errorMessage,
      // Add request ID if available (will be implemented in Phase 5)
      ...(request.headers['x-request-id'] ? { requestId: request.headers['x-request-id'] } : {}),
    };

    // Log the error with its stack trace
    this.logger.error(
      `${request.method} ${request.url} ${status} - ${JSON.stringify(errorMessage)}`,
      exception.stack,
    );

    // Send the error response to the client
    response.status(status).json(errorObj);
  }
}
