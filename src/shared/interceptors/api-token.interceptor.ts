import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Observable } from 'rxjs';
import { Request } from 'express';

/**
 * Interceptor to validate API authentication token
 * This ensures that only authorized clients can access protected routes
 */
@Injectable()
export class ApiTokenInterceptor implements NestInterceptor {
  private readonly logger = new Logger('ApiTokenInterceptor');
  private readonly apiToken: string;
  private readonly headerName: string = 'x-api-token';

  constructor(private readonly configService: ConfigService) {
    // Get the API token from configuration
    this.apiToken = this.configService.get<string>('api.auth.token') || '';

    if (!this.apiToken) {
      this.logger.warn(
        'API token is not configured. Set API_AUTH_TOKEN in your environment variables.',
      );
    } else {
      this.logger.log(
        `API token interceptor initialized with token: ${this.apiToken.substring(0, 4)}...`,
      );
    }
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const path = request.url;
    const method = request.method;

    // Skip token validation for health endpoints and metrics
    if (path.includes('/health') || 
        path.includes('/api/v1/sms/send/success') || 
        path.includes('/worker/status') || 
        path.includes('/worker/metrics') ||
        path.includes('/api/v1/worker/status') ||
        path.includes('/api/v1/worker/metrics') ||
        path.startsWith('/worker/')) {
      this.logger.debug(`Skipping token validation for health/metrics endpoint: ${method} ${path}`);
      return next.handle();
    }

    // Skip token validation if no token is configured
    if (!this.apiToken) {
      this.logger.warn(`Request to ${method} ${path} allowed without token (no token configured)`);
      return next.handle();
    }

    // Get the token from the request header or query parameter
    const token = this.getTokenFromRequest(request);

    // If no token is provided, throw Unauthorized exception
    if (!token) {
      this.logger.warn(`Unauthorized request to ${method} ${path} - No token provided`);
      throw new UnauthorizedException('API token is required');
    }

    // Validate the token
    if (token !== this.apiToken) {
      this.logger.warn(`Unauthorized request to ${method} ${path} - Invalid token`);
      throw new UnauthorizedException('Invalid API token');
    }

    // Token is valid, proceed with the request
    this.logger.debug(`Authenticated request to ${method} ${path}`);
    return next.handle();
  }

  /**
   * Get the token from the request
   * Checks both the header and query parameter
   */
  private getTokenFromRequest(request: Request): string | undefined {
    // Check Authorization header for Bearer token
    const authHeader = request.headers['authorization'] as string;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.slice(7).trim();
    }

    // Check header first (preferred method)
    const headerToken = request.headers[this.headerName] as string;
    if (headerToken) {
      return headerToken;
    }

    // Check query parameter as fallback
    const queryToken = request.query.token as string;
    if (queryToken) {
      return queryToken;
    }

    // Check body for token
    const bodyToken = request.body?.token as string;
    if (bodyToken) {
      return bodyToken;
    }

    // No token found
    return undefined;
  }
}
