import {
  Injectable,
  CanActivate,
  ExecutionContext,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Observable } from 'rxjs';
import { Request } from 'express';

/**
 * Guard to validate API authentication token
 * This ensures that only authorized clients can access protected routes
 */
@Injectable()
export class ApiTokenGuard implements CanActivate {
  private readonly logger = new Logger('ApiTokenGuard');
  private readonly apiToken: string;
  private readonly headerName: string = 'x-api-token';

  constructor(private readonly configService: ConfigService) {
    // Get the API token from configuration
    this.apiToken = this.configService.get<string>('api.auth.token') || '';

    if (!this.apiToken) {
      this.logger.warn(
        'API token is not configured. Set API_AUTH_TOKEN in your environment variables.',
      );
    } else {
      this.logger.log(
        `API token guard initialized with token: ${this.apiToken.substring(0, 4)}...`,
      );
    }
  }

  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const path = request.url;
    const method = request.method;

    this.logger.debug(`Checking token for ${method} ${path}`);

    // Skip token validation for health endpoint
    if (path.includes('/health')) {
      this.logger.debug(`Skipping token validation for health endpoint: ${method} ${path}`);
      return true;
    }

    // Skip token validation if no token is configured
    if (!this.apiToken) {
      this.logger.warn(`Request to ${method} ${path} allowed without token (no token configured)`);
      return true;
    }

    // Get the token from the request header or query parameter
    const token = this.getTokenFromRequest(request);

    // If no token is provided, throw Unauthorized exception
    if (!token) {
      this.logger.warn(`Unauthorized request to ${method} ${path} - No token provided`);
      throw new UnauthorizedException('API token is required');
    }

    // Validate the token
    if (token !== this.apiToken) {
      this.logger.warn(`Unauthorized request to ${method} ${path} - Invalid token`);
      throw new UnauthorizedException('Invalid API token');
    }

    // Token is valid, allow the request
    this.logger.debug(`Authenticated request to ${method} ${path}`);
    return true;
  }

  /**
   * Get the token from the request
   * Checks both the header and query parameter
   */
  private getTokenFromRequest(request: Request): string | undefined {
    // Check Authorization header for Bearer token
    const authHeader = request.headers['authorization'] as string;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.slice(7).trim();
    }

    // Check header first (preferred method)
    const headerToken = request.headers[this.headerName] as string;
    if (headerToken) {
      return headerToken;
    }

    // Check query parameter as fallback
    const queryToken = request.query.token as string;
    if (queryToken) {
      return queryToken;
    }

    // No token found
    return undefined;
  }
}
