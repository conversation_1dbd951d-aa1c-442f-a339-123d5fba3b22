/**
 * Interface for error context
 */
export interface ErrorContext {
  /**
   * Message ID
   */
  messageId: string;
  
  /**
   * Request ID
   */
  requestId: string;
  
  /**
   * Message type (single, bulk, dlr etc.)
   */
  messageType: 'single' | 'bulk' | 'dlr';
  
  /**
   * Retry count
   */
  retryCount: number;
  
  /**
   * Provider
   */
  provider: string;
  
  /**
   * Recipient
   */
  recipient: string;
  
  /**
   * Timestamp
   */
  timestamp: Date;
  
  /**
   * Additional info
   */
  additionalInfo: {
    /**
     * Correlation ID
     */
    correlationId?: string;
    
    /**
     * Bulk type (for bulk messages)
     */
    bulkType?: string;
    
    /**
     * Message count (for bulk messages)
     */
    messageCount?: number;
  };
}
