import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../app.module';
import { SmsService } from '../sms/sms.service';
import { SmsConsumer } from '../queue/consumers/sms.consumer';
import { QueueManagerService } from '../queue/services/queue-manager.service';
import { WorkerManagerService } from '../worker/services/worker-manager.service';
import { SmsProviderService } from '../sms/services/sms-provider.service';
import { RabbitMQConnectionService } from '../queue/services/rabbitmq-connection.service';
import { v4 as uuidv4 } from 'uuid';
import { SingleSmsDto, SmsServiceProvider } from '../sms/dto/sms-request.dto';

/**
 * Load test for the SMS API
 *
 * This test verifies that the SMS API can handle high concurrency.
 */
describe('SMS API Load Test', () => {
  let app: INestApplication;
  let smsService: SmsService;
  let smsConsumer: SmsConsumer;
  let queueManager: QueueManagerService;
  let workerManager: WorkerManagerService;
  let smsProvider: SmsProviderService;
  let rabbitMQConnection: RabbitMQConnectionService;

  beforeAll(async () => {
    // Create a test module with all the necessary components
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Get the services
    smsService = moduleFixture.get<SmsService>(SmsService);
    smsConsumer = moduleFixture.get<SmsConsumer>(SmsConsumer);
    queueManager = moduleFixture.get<QueueManagerService>(QueueManagerService);
    workerManager = moduleFixture.get<WorkerManagerService>(WorkerManagerService);
    smsProvider = moduleFixture.get<SmsProviderService>(SmsProviderService);
    rabbitMQConnection = moduleFixture.get<RabbitMQConnectionService>(RabbitMQConnectionService);
  });

  afterAll(async () => {
    // Clean up any mocks that might be keeping the process alive
    jest.restoreAllMocks();

    // Close the app
    await app.close();

    // Add a small delay to allow any remaining operations to complete
    await new Promise(resolve => setTimeout(resolve, 100));
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('High Concurrency Tests', () => {
    it('should handle 100 simultaneous single SMS requests', async () => {
      // Create a spy to track the number of messages processed
      const sendSingleSpy = jest.spyOn(smsService, 'sendSingle');
      const sendSingleSmsSpy = jest.spyOn(smsProvider, 'sendSingleSms');

      // Number of concurrent requests
      const concurrentRequests = 100;
      
      // Start time for performance measurement
      const startTime = Date.now();

      // Create an array of test messages with different phone numbers
      const requests = Array.from({ length: concurrentRequests }, (_, i) => {
        // Alternate between TELE and SAFARICOM phone numbers
        const phoneNumber = i % 2 === 0 
          ? `2519${String(i).padStart(8, '0')}` // TELE
          : `2517${String(i).padStart(8, '0')}`; // SAFARICOM
          
        const smsServiceProvider = i % 2 === 0 
          ? SmsServiceProvider.TELE 
          : SmsServiceProvider.SAFARICOM;
          
        return request(app.getHttpServer())
          .post('/sms/send/single')
          .send({
            to: phoneNumber,
            message: `Test message ${i} - ${uuidv4().substring(0, 8)}`,
            sms_credential: {
              usr: 'testuser',
              pwd: 'testpass',
              from: 'SENDER',
              token: 'testtoken',
              sms_service_provider: smsServiceProvider
            }
          });
      });

      // Send all requests simultaneously
      const responses = await Promise.all(requests);
      
      // End time for performance measurement
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      
      console.log(`Processed ${concurrentRequests} requests in ${totalTime}ms`);
      console.log(`Average time per request: ${totalTime / concurrentRequests}ms`);
      console.log(`Requests per second: ${(concurrentRequests / totalTime) * 1000}`);

      // Verify all responses
      responses.forEach(response => {
        expect(response.status).toBe(202);
        expect(response.body).toHaveProperty('statusCode');
        expect(response.body.statusCode).toBe(202);
        expect(response.body).toHaveProperty('message');
        expect(response.body.message).toBe('SMS has been queued for delivery');
      });

      // Verify the service was called the correct number of times
      expect(sendSingleSpy).toHaveBeenCalledTimes(concurrentRequests);
      
      // Wait for all messages to be processed (give some time for async processing)
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Verify that the provider service was called
      expect(sendSingleSmsSpy).toHaveBeenCalled();
      
      // Log the number of messages processed by the provider
      console.log(`Messages processed by provider: ${sendSingleSmsSpy.mock.calls.length}`);
      
      // Verify that all messages were processed
      // Note: This might not be exactly equal to concurrentRequests due to async nature
      // but should be close
      expect(sendSingleSmsSpy.mock.calls.length).toBeGreaterThanOrEqual(concurrentRequests * 0.9);
    }, 60000); // Increase timeout to 60 seconds for this test

    it('should handle 20 simultaneous bulk SMS requests', async () => {
      // Create a spy to track the number of messages processed
      const sendUnifiedBulkSpy = jest.spyOn(smsService, 'sendUnifiedBulk');
      const sendBulkSmsSpy = jest.spyOn(smsProvider, 'sendBulkSms');

      // Number of concurrent requests
      const concurrentRequests = 20;
      
      // Start time for performance measurement
      const startTime = Date.now();

      // Create an array of test messages
      const requests = Array.from({ length: concurrentRequests }, (_, i) => {
        // Alternate between TELE and SAFARICOM as the service provider
        const smsServiceProvider = i % 2 === 0 
          ? SmsServiceProvider.TELE 
          : SmsServiceProvider.SAFARICOM;
          
        return request(app.getHttpServer())
          .post('/sms/send/bulk')
          .send({
            message: `Bulk test message ${i} - ${uuidv4().substring(0, 8)}`,
            receivers: `contact_group_${i}`,
            bulk_type: 'DASHBOARD',
            user_id: 12345,
            billing_id: 67890,
            job_name: `Test Bulk SMS ${i}`,
            job_unique_id: `test-${uuidv4()}`,
            sms_body_id: 456,
            sms_credential: {
              usr: 'testuser',
              pwd: 'testpass',
              from: 'SENDER',
              token: 'testtoken',
              sms_service_provider: smsServiceProvider
            }
          });
      });

      // Send all requests simultaneously
      const responses = await Promise.all(requests);
      
      // End time for performance measurement
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      
      console.log(`Processed ${concurrentRequests} bulk requests in ${totalTime}ms`);
      console.log(`Average time per bulk request: ${totalTime / concurrentRequests}ms`);
      console.log(`Bulk requests per second: ${(concurrentRequests / totalTime) * 1000}`);

      // Verify all responses
      responses.forEach((response, i) => {
        expect(response.status).toBe(202);
        expect(response.body).toHaveProperty('statusCode');
        expect(response.body.statusCode).toBe(202);
        expect(response.body).toHaveProperty('message');
        expect(response.body.message).toBe(`Bulk SMS for contact group contact_group_${i} has been queued for delivery`);
      });

      // Verify the service was called the correct number of times
      expect(sendUnifiedBulkSpy).toHaveBeenCalledTimes(concurrentRequests);
      
      // Wait for all messages to be processed (give some time for async processing)
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Verify that the provider service was called
      expect(sendBulkSmsSpy).toHaveBeenCalled();
      
      // Log the number of messages processed by the provider
      console.log(`Bulk messages processed by provider: ${sendBulkSmsSpy.mock.calls.length}`);
      
      // Verify that all messages were processed
      expect(sendBulkSmsSpy.mock.calls.length).toBeGreaterThanOrEqual(concurrentRequests * 0.9);
    }, 60000); // Increase timeout to 60 seconds for this test
  });
});
