import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { WorkerManagerService } from '../services/worker-manager.service';
import { Worker } from 'worker_threads';

// Define a custom Worker type for testing
interface MockWorker extends Worker {
  _triggerEvent: (event: string, data: any) => void;
}

// Mock the Worker class
jest.mock('worker_threads', () => {
  return {
    Worker: jest.fn().mockImplementation(() => {
      const eventHandlers: Record<string, Function[]> = {};

      return {
        on: jest.fn((event: string, handler: Function) => {
          if (!eventHandlers[event]) {
            eventHandlers[event] = [];
          }
          eventHandlers[event].push(handler);
          return this;
        }),

        postMessage: jest.fn((message: any) => {
          // Simulate worker sending a result back
          if (eventHandlers['message']) {
            eventHandlers['message'].forEach(handler => {
              handler({
                type: 'result',
                success: true,
                data: {
                  messageId: `msg_${Math.random().toString(36).substring(2, 15)}`,
                  processingTime: 50,
                  workerId: 1,
                  timestamp: new Date().toISOString()
                },
                requestId: message.requestId
              });
            });
          }
        }),

        terminate: jest.fn().mockImplementation(() => {
          if (eventHandlers['exit']) {
            eventHandlers['exit'].forEach(handler => handler(0));
          }
        }),

        once: jest.fn((event: string, handler: Function) => {
          if (!eventHandlers[event]) {
            eventHandlers[event] = [];
          }
          eventHandlers[event].push(handler);
          return this;
        }),

        // Helper method for tests to trigger events
        _triggerEvent: (event: string, data: any) => {
          if (eventHandlers[event]) {
            eventHandlers[event].forEach(handler => handler(data));
          }
        }
      };
    })
  };
});

describe('WorkerManagerService', () => {
  let workerManager: WorkerManagerService;
  let mockConfigService: Partial<ConfigService>;

  beforeEach(async () => {
    // Reset mocks
    jest.clearAllMocks();

    // Create mock ConfigService
    mockConfigService = {
      get: jest.fn().mockImplementation((key: string) => {
        if (key === 'sms.workers.count') {
          return 3; // Return 3 workers for testing
        }
        return null;
      })
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkerManagerService,
        {
          provide: ConfigService,
          useValue: mockConfigService
        }
      ],
    }).compile();

    workerManager = module.get<WorkerManagerService>(WorkerManagerService);

    // Initialize workers
    await workerManager.onModuleInit();
  });

  afterEach(async () => {
    // Clean up
    await workerManager.onModuleDestroy();
  });

  it('should initialize the correct number of workers', () => {
    expect(Worker).toHaveBeenCalledTimes(3);
    expect(workerManager.checkHealth().workerCount).toBe(3);
  });

  it('should get a worker using round-robin', () => {
    const worker1 = workerManager.getWorker();
    const worker2 = workerManager.getWorker();
    const worker3 = workerManager.getWorker();
    const worker4 = workerManager.getWorker(); // Should wrap around to the first worker

    expect(worker1).toBeDefined();
    expect(worker2).toBeDefined();
    expect(worker3).toBeDefined();
    expect(worker4).toBe(worker1); // Round-robin should have wrapped around
  });

  it('should get a consistent worker with the same assignment value', () => {
    const worker1 = workerManager.getWorker(123);
    const worker2 = workerManager.getWorker(123);

    expect(worker1).toBe(worker2); // Same assignment value should get the same worker
  });

  it('should handle worker messages', () => {
    const worker = workerManager.getWorker() as MockWorker;
    expect(worker).not.toBeNull();

    // Simulate a heartbeat message
    worker?._triggerEvent('message', {
      type: 'heartbeat',
      workerId: 0,
      timestamp: new Date().toISOString()
    });

    // Simulate a log message
    worker?._triggerEvent('message', {
      type: 'log',
      level: 'info',
      text: 'Test log message'
    });

    // Check health after heartbeat
    const health = workerManager.checkHealth();
    expect(health.status).toBe('up');
  });

  it('should handle worker errors', () => {
    const worker = workerManager.getWorker() as MockWorker;
    expect(worker).not.toBeNull();

    // Simulate an error
    worker?._triggerEvent('error', new Error('Test error'));

    // Check health after error
    const health = workerManager.checkHealth();
    expect(health.status).toBe('down');
  });

  it('should replace a worker that exits', async () => {
    // Reset the mock call count before this test
    jest.clearAllMocks();

    const worker = workerManager.getWorker() as MockWorker;
    expect(worker).not.toBeNull();

    // Simulate worker exit
    worker?._triggerEvent('exit', 1);

    // Wait for replacement
    await new Promise(resolve => setTimeout(resolve, 1100));

    // Should have created a new worker (just check that it was called at least once for replacement)
    expect(Worker).toHaveBeenCalled();
  });

  it('should send messages to workers and get results', async () => {
    const worker = workerManager.getWorker() as MockWorker;
    expect(worker).not.toBeNull();

    // Create a promise that resolves when we get a result
    const resultPromise = new Promise<any>(resolve => {
      worker?.on('message', (message: any) => {
        if (message.type === 'result') {
          resolve(message);
        }
      });
    });

    // Send a message
    worker?.postMessage({
      provider: 'test-provider',
      to: 'recipient',
      message: 'Test message',
      requestId: 'test-123'
    });

    // Wait for the result
    const result = await resultPromise;

    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    expect(result.data.messageId).toBeDefined();
    expect(result.requestId).toBe('test-123');
  });
});
