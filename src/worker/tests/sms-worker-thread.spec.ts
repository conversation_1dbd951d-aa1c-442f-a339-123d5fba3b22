import { Worker } from 'worker_threads';
import * as path from 'path';
import { EventEmitter } from 'events';

describe('SMS Worker Thread', () => {
  let worker: Worker;
  let mockParentPort: EventEmitter;

  beforeEach(() => {
    // Create a mock parent port
    mockParentPort = new EventEmitter();
    mockParentPort.postMessage = jest.fn();

    // Mock the worker_threads module
    jest.mock('worker_threads', () => ({
      parentPort: mockParentPort,
      workerData: {},
      isMainThread: false,
    }));
  });

  afterEach(() => {
    // Clean up
    jest.resetModules();
  });

  it('should process TELE provider messages correctly', async () => {
    // Load the worker thread module
    const workerThreadPath = path.resolve(__dirname, '../threads/sms-worker-thread.js');
    
    // Create a test message
    const testMessage = {
      id: 'test-message-id',
      data: {
        provider: 'default',
        to: '251953960596',
        message: 'Test message',
        attempt: 1,
        requestId: 'test-request-id',
        assignmentValue: 123,
        messageType: 'single',
        timestamp: new Date().toISOString(),
        workerId: 'worker-0',
        smsServiceProvider: 'TELE',
      },
    };

    // Create a promise that will be resolved when the worker responds
    const responsePromise = new Promise((resolve) => {
      mockParentPort.postMessage = jest.fn().mockImplementation((message) => {
        if (message.type === 'result') {
          resolve(message);
        }
      });
    });

    // Send the message to the worker
    mockParentPort.emit('message', testMessage);

    // Wait for the response
    const response = await responsePromise;

    // Verify the response
    expect(response).toHaveProperty('type', 'result');
    expect(response).toHaveProperty('success', true);
    expect(response).toHaveProperty('data');
    expect(response.data).toHaveProperty('to', '251953960596');
    expect(response.data).toHaveProperty('messageType', 'single');
    expect(response.data).toHaveProperty('provider', 'default');
  });

  it('should process SAFARICOM provider messages correctly', async () => {
    // Load the worker thread module
    const workerThreadPath = path.resolve(__dirname, '../threads/sms-worker-thread.js');
    
    // Create a test message
    const testMessage = {
      id: 'test-message-id',
      data: {
        provider: 'default',
        to: '251711220033',
        message: 'Test message',
        attempt: 1,
        requestId: 'test-request-id',
        assignmentValue: 123,
        messageType: 'single',
        timestamp: new Date().toISOString(),
        workerId: 'worker-0',
        smsServiceProvider: 'SAFARICOM',
      },
    };

    // Create a promise that will be resolved when the worker responds
    const responsePromise = new Promise((resolve) => {
      mockParentPort.postMessage = jest.fn().mockImplementation((message) => {
        if (message.type === 'result') {
          resolve(message);
        }
      });
    });

    // Send the message to the worker
    mockParentPort.emit('message', testMessage);

    // Wait for the response
    const response = await responsePromise;

    // Verify the response
    expect(response).toHaveProperty('type', 'result');
    expect(response).toHaveProperty('success', true);
    expect(response).toHaveProperty('data');
    expect(response.data).toHaveProperty('to', '251711220033');
    expect(response.data).toHaveProperty('messageType', 'single');
    expect(response.data).toHaveProperty('provider', 'default');
  });

  it('should handle errors correctly', async () => {
    // Load the worker thread module
    const workerThreadPath = path.resolve(__dirname, '../threads/sms-worker-thread.js');
    
    // Create a test message with invalid data
    const testMessage = {
      id: 'test-message-id',
      data: {
        provider: 'default',
        to: 'invalid-phone-number',
        message: 'Test message',
        attempt: 1,
        requestId: 'test-request-id',
        assignmentValue: 123,
        messageType: 'single',
        timestamp: new Date().toISOString(),
        workerId: 'worker-0',
        smsServiceProvider: 'TELE',
      },
    };

    // Create a promise that will be resolved when the worker responds
    const responsePromise = new Promise((resolve) => {
      mockParentPort.postMessage = jest.fn().mockImplementation((message) => {
        if (message.type === 'result') {
          resolve(message);
        }
      });
    });

    // Send the message to the worker
    mockParentPort.emit('message', testMessage);

    // Wait for the response
    const response = await responsePromise;

    // Verify the response
    expect(response).toHaveProperty('type', 'result');
    expect(response).toHaveProperty('success', false);
    expect(response).toHaveProperty('error');
  });
});
