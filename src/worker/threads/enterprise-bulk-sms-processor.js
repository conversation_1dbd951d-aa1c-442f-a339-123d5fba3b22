// Enterprise Bulk SMS Processor Module
// Extracted from untitled:Untitled-1 for integration with sms-bulk-worker.js

const { performance } = require('perf_hooks');
const { parentPort, workerData } = require('worker_threads');
const EventEmitter = require('events');
const {
  extractMessageContent,
  getContactsByContactGroup,
  getContactsByBulkApi,
  templateParse,
  phoneType,
  DynamicConfigAdapter,
} = require('./sms-worker-thread-utils');
const { sendViaTELE, sendViaSAFARICOM } = require('./sms-provider-utils');

/**
 * The workerLog function will be injected from the worker file for consistent logging
 * @type {function}
 */
let workerLog = console.log;

class BulkSMSMetrics extends EventEmitter {
  constructor() {
    super();
    this.metrics = {
      totalContacts: 0,
      teleContacts: 0,
      safariContacts: 0,
      processedContacts: 0,
      successfulContacts: 0,
      failedContacts: 0,
      batchesProcessed: 0,
      batchesFailed: 0,
      averageProcessingTime: 0,
      throughputPerSecond: 0,
      errorRate: 0,
      memoryUsage: 0,
      startTime: null,
      endTime: null,
      totalProcessingTime: 0,
    };
    this.startTime = performance.now();
    this.processingTimes = [];
    this.errorCounts = new Map();
    this.isHealthy = true;
  }

  updateMetrics(data) {
    Object.assign(this.metrics, data);
    this.calculateDerivedMetrics();
    this.emit('metrics-updated', this.metrics);
  }

  calculateDerivedMetrics() {
    const { processedContacts, failedContacts } = this.metrics;
    if (processedContacts > 0) {
      this.metrics.errorRate = (failedContacts / processedContacts) * 100;
    }
    if (this.processingTimes.length > 0) {
      this.metrics.averageProcessingTime =
        this.processingTimes.reduce((a, b) => a + b, 0) / this.processingTimes.length;
    }
    if (this.metrics.startTime) {
      const elapsedSeconds = (performance.now() - this.metrics.startTime) / 1000;
      this.metrics.throughputPerSecond = Math.floor(processedContacts / elapsedSeconds);
    }
    const memInfo = process.memoryUsage();
    this.metrics.memoryUsage = memInfo.heapUsed / memInfo.heapTotal;
  }

  recordProcessingTime(time) {
    this.processingTimes.push(time);
    if (this.processingTimes.length > 1000) {
      this.processingTimes.shift();
    }
  }

  recordError(error, context = {}) {
    const errorKey = `${error.name || 'UnknownError'}-${error.message || 'Unknown'}`;
    this.errorCounts.set(errorKey, (this.errorCounts.get(errorKey) || 0) + 1);
    workerLog('ERROR', 'SMS processing error', {
      error: error.message,
      stack: error.stack,
      context,
      errorCount: this.errorCounts.get(errorKey),
    });
  }

  getHealthStatus() {
    return {
      isHealthy: this.isHealthy,
      metrics: this.metrics,
      topErrors: Array.from(this.errorCounts.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5),
    };
  }
}

class CircuitBreaker {
  constructor(threshold, timeout) {
    this.threshold = threshold;
    this.timeout = timeout;
    this.failures = 0;
    this.successes = 0;
    this.state = 'CLOSED';
    this.nextAttempt = 0;
  }

  async execute(fn, ...args) {
    if (this.state === 'OPEN') {
      if (Date.now() < this.nextAttempt) {
        throw new Error('Circuit breaker is OPEN');
      }
      this.state = 'HALF_OPEN';
    }
    try {
      const result = await fn(...args);
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  onSuccess() {
    this.successes++;
    this.failures = 0;
    if (this.state === 'HALF_OPEN') {
      this.state = 'CLOSED';
    }
  }

  onFailure() {
    this.failures++;
    const total = this.failures + this.successes;
    if (total >= 10 && this.failures / total >= this.threshold) {
      this.state = 'OPEN';
      this.nextAttempt = Date.now() + this.timeout;
      workerLog('WARN', `Circuit breaker opened. Failures: ${this.failures}, Total: ${total}`);
    }
  }
}

class AdaptiveRateLimiter {
  constructor(baseDelay) {
    this.baseDelay = baseDelay;
    this.currentDelay = baseDelay;
    this.lastRequestTime = 0;
    this.consecutiveErrors = 0;
    this.maxDelay = baseDelay * 10;
  }

  async throttle() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    if (timeSinceLastRequest < this.currentDelay) {
      await this.sleep(this.currentDelay - timeSinceLastRequest);
    }
    this.lastRequestTime = Date.now();
  }

  onSuccess() {
    this.consecutiveErrors = 0;
    this.currentDelay = Math.max(this.baseDelay, this.currentDelay * 0.9);
  }

  onError() {
    this.consecutiveErrors++;
    this.currentDelay = Math.min(this.maxDelay, this.currentDelay * 1.5);
  }

  sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}

/**
 * Enterprise-grade Bulk SMS Processor
 *
 * @param {object} options - { config, workerLog }
 */
class EnterpriseBulkSMSProcessor {
  constructor(options = {}) {
    /**
     * Default configuration constants. Instance config is used for each processor.
     */
    const configAdapter = new DynamicConfigAdapter(options.config || {});
    const DEFAULT_CONFIG = configAdapter.getConfig();

    this.config = { ...DEFAULT_CONFIG, ...options.config };
    workerLog('INFO', 'Loaded dynamic config', this.config);

    workerLog = options.workerLog || workerLog;
    if (!this.metrics || !this.metrics.metrics) {
      this.metrics = new BulkSMSMetrics();
    }
    this.teleCircuitBreaker = new CircuitBreaker(
      this.config.CIRCUIT_BREAKER_THRESHOLD,
      this.config.CIRCUIT_BREAKER_TIMEOUT,
    );
    this.safariCircuitBreaker = new CircuitBreaker(
      this.config.CIRCUIT_BREAKER_THRESHOLD,
      this.config.CIRCUIT_BREAKER_TIMEOUT,
    );
    this.teleRateLimiter = new AdaptiveRateLimiter(this.config.RATE_LIMIT_DELAY);
    this.safariRateLimiter = new AdaptiveRateLimiter(this.config.RATE_LIMIT_DELAY);
    this.isProcessing = false;
    this.shouldStop = false;
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, this.config.HEALTH_CHECK_INTERVAL);
  }

  /**
   * Main entrypoint for bulk SMS processing
   * @param {object} messageData
   */
  async processBulkSMS(messageData) {
    // Helper functions for enterprise processor
    const helpers = {
      getDashboardContact: getContactsByContactGroup,
      getApiContacts: getContactsByBulkApi,
      extractMessageContent,
      phoneType,
      sendTeleBatch: async (batch, messageContent, sms_credential, bulk_id, requestId) => {
        // Use your existing send logic or processSms for tele
        const results = await Promise.allSettled(
          batch.map((contact) => {
            const today = new Date().toISOString();
            const phone = contact.phone_number ?? contact.to;
            const templateData = {
              from: sms_credential.from,
              date: today,
              lname: contact.lname || '',
              fname: contact.fname || '',
              birthday: contact.birthday || '',
              phone: phone || '',
              ...contact,
            };
            const parsedMessageContent = templateParse(messageContent, templateData); // Parse for each contact
            return sendViaTELE(sms_credential, bulk_id, phone, parsedMessageContent, requestId);
          }),
        );

        results.forEach((res, idx) => {
          if (res.status === 'rejected') {
            workerLog('ERROR', 'Failed to send SMS to contact', {
              contact: batch[idx],
              reason: res.reason?.message || res.reason,
            });
          }
        });

        const successful = results.filter((r) => r.status === 'fulfilled').length;
        const failed = results.length - successful;
        return { successful, failed, results };
      },
      sendSafariBatch: async (batch, messageContent, sms_credential, bulk_id, requestId) => {
        // Use your existing send logic or processSms for safari
        const results = await Promise.allSettled(
          batch.map((contact) => {
            const phone = contact.phone_number ?? contact.to;
            const today = new Date().toISOString();
            const templateData = {
              from: sms_credential.from,
              date: today,
              lname: contact.lname || '',
              fname: contact.fname || '',
              birthday: contact.birthday || '',
              phone: phone || '',
              ...contact,
            };
            const parsedMessageContent = templateParse(messageContent, templateData); // Parse for each contact
            return sendViaSAFARICOM(
              sms_credential,
              bulk_id,
              phone,
              parsedMessageContent,
              requestId,
            );
          }),
        );

        results.forEach((res, idx) => {
          if (res.status === 'rejected') {
            workerLog('ERROR', 'Failed to send SMS to contact', {
              contact: batch[idx],
              reason: res.reason?.message || res.reason,
            });
          }
        });

        const successful = results.filter((r) => r.status === 'fulfilled').length;
        const failed = results.length - successful;
        return { successful, failed, results };
      },
    };

    // Validate helpers contract
    const requiredHelpers = [
      'getDashboardContact',
      'getApiContacts',
      'extractMessageContent',
      'sendTeleBatch',
      'sendSafariBatch',
    ];
    for (const fn of requiredHelpers) {
      if (typeof helpers[fn] !== 'function') {
        throw new Error(`Missing required helper function: ${fn}`);
      }
    }
    if (this.isProcessing) {
      throw new Error('Bulk SMS processing already in progress');
    }
    this.isProcessing = true;
    this.shouldStop = false;
    this.metrics.metrics.startTime = performance.now();
    let finalMetrics = this.metrics.metrics;

    try {
      const {
        to: group_id,
        user_id,
        sms_body_id,
        requestId = 'unknown',
        bulk_type, // 1 for dashboard bulk, 2 for api bulk
        page_size,
      } = messageData;
      const messageType =
        typeof bulk_type === 'string' && bulk_type.toUpperCase() === 'DASHBOARD'
          ? 1
          : typeof bulk_type === 'string' && bulk_type.toUpperCase() === 'API'
            ? 2
            : bulk_type; // Fallback to original value if not recognised string

      const requiredParams = ['to', 'user_id', 'sms_body_id', 'bulk_type'];

      workerLog('DEBUG', `Checking bulk SMS processing ${group_id}`);

      // Validate request of messageData
      for (const paramName of requiredParams) {
        if (
          messageData[paramName] === undefined ||
          messageData[paramName] === null ||
          !messageData[paramName]
        ) {
          throw new Error(`Missing required param: ${paramName}`);
        }
      }

      workerLog('INFO', 'Starting bulk SMS processing', {
        requestId,
        messageType,
        group_id,
        user_id,
      });

      // Contacts
      let teleContacts = [];
      let safariContacts = [];

      // Page
      let currentPage = 1;
      let totalProcessedContacts = 0;
      let hasMorePages = true;

      if (messageType == 1) {
        // Process tele contacts
        while (hasMorePages) {
          // Fetch and validate contacts
          const contactsData = await helpers.getDashboardContact(
            group_id,
            user_id,
            currentPage,
            sms_body_id,
            'tele',
          );
          workerLog('SUCCESS', `Contact Fetch!`);
          if (!contactsData || !contactsData.data || !Array.isArray(contactsData.data.data)) {
            workerLog('WARN', 'No valid Tele contacts data found or API returned an error', {
              requestId,
              page: currentPage,
              group_id,
            });
            hasMorePages = false; // Stop fetching if data is malformed
            continue;
            // throw new Error('No valid tele contacts found');
          }
          teleContacts = contactsData.data.data;
          const totalContactsLength = teleContacts.length;
          if (totalContactsLength === 0) {
            // If current page has no contacts, it means no more for this type
            workerLog('INFO', `No more Tele contacts found on page ${currentPage}`, {
              requestId,
              group_id,
            });
            hasMorePages = false;
            continue;
          }
          this.metrics.updateMetrics({
            totalContacts: this.metrics.metrics.totalContacts + totalContactsLength, // Accumulate total contacts
            teleContacts: this.metrics.metrics.teleContacts + totalContactsLength,
          });

          workerLog(
            'INFO',
            `Accumulated Contacts (Tele): Total: ${this.metrics.metrics.totalContacts}, Tele: ${this.metrics.metrics.teleContacts}`,
            {
              requestId,
            },
          );

          const processingPromises = [];
          if (totalContactsLength > 0) {
            processingPromises.push(
              this.processTeleContactsOptimized(teleContacts, messageData, helpers),
            );
          }
          const results = await Promise.allSettled(processingPromises);
          finalMetrics = this.processFinalResults();
          workerLog('SUCCESS', 'Tele Bulk SMS processing completed for batch', {
            // Changed log message for clarity
            requestId,
            ...finalMetrics,
          });

          if (!contactsData.data.has_more) {
            hasMorePages = false;
          } else {
            currentPage++; // Move to the next page
          }
        }

        currentPage = 1;
        hasMorePages = true;
        // Process tele contacts
        while (hasMorePages) {
          // Fetch and validate contacts
          const contactsData = await helpers.getDashboardContact(
            group_id,
            user_id,
            currentPage,
            sms_body_id,
            'safari',
          );

          if (!contactsData || !contactsData.data || !Array.isArray(contactsData.data.data)) {
            workerLog('WARN', 'No valid Safari contacts data found or API returned an error', {
              requestId,
              page: currentPage,
              group_id,
            });
            hasMorePages = false;
            continue;
            // throw new Error('No valid safari contacts found');
          }
          safariContacts = contactsData.data.data || [];
          const totalContactsLength = safariContacts.length;
          if (totalContactsLength === 0) {
            // If current page has no contacts, it means no more for this type
            workerLog('INFO', `No more Safari contacts found on page ${currentPage}`, {
              requestId,
              group_id,
            });
            hasMorePages = false;
            continue;
          }

          this.metrics.updateMetrics({
            totalContacts: this.metrics.metrics.totalContacts + totalContactsLength,
            safariContacts: this.metrics.metrics.safariContacts + totalContactsLength,
          });

          workerLog(
            'INFO',
            `Accumulated Contacts (Safari): Total: ${this.metrics.metrics.totalContacts}, Safari: ${this.metrics.metrics.safariContacts}`,
            {
              requestId,
            },
          );

          const processingPromises = [];
          if (totalContactsLength > 0) {
            processingPromises.push(
              this.processSafariContactsOptimized(safariContacts, messageData, helpers),
            );
          }
          const results = await Promise.allSettled(processingPromises);
          finalMetrics = this.processFinalResults();
          workerLog('SUCCESS', 'Safari Bulk SMS processing completed for batch', {
            // Changed log message for clarity
            requestId,
            ...finalMetrics,
          });

          if (!contactsData.data.has_more) {
            hasMorePages = false;
          } else {
            currentPage++;
          }
        }
      } else if (messageType == 2) {
        // Fetch and validate contacts
        const contactsData = await helpers.getApiContacts(group_id, user_id, page, sms_body_id);

        if (!contactsData || !contactsData.data || !Array.isArray(contactsData.data.to)) {
          workerLog('WARN', 'No valid bulk api contacts data found or API returned an error', {
            requestId,
            page: currentPage,
            group_id,
          });
          hasMorePages = false;
          return;
          // throw new Error('No valid safari contacts found');
        }

        const allContacts = JSON.parse(contactsData.data.to) || [];

        teleContacts = allContacts.filter((contact) => {
          return helpers.phoneType(contact.phone_number) === 'TELE';
        });

        safariContacts = allContacts.filter((contact) => {
          return helpers.phoneType(contact.phone_number) === 'SAFARICOM';
        });
        const totalContactsLength = teleContacts.length + safariContacts.length;
        if (totalContactsLength === 0) {
          // If current page has no contacts, it means no more for this type
          workerLog('INFO', `No more bulk api contacts found on page ${currentPage}`, {
            requestId,
            group_id,
          });
          return;
        }
        this.metrics.updateMetrics({
          totalContacts: this.metrics.metrics.totalContacts + totalContactsLength, // Accumulate total contacts
          teleContacts: this.metrics.metrics.teleContacts + teleContacts.length,
          safariContacts: this.metrics.metrics.safariContacts + safariContacts.length,
        });

        workerLog(
          'INFO',
          `Accumulated Contacts (Tele): Total: ${this.metrics.metrics.totalContacts}, Tele: ${this.metrics.metrics.teleContacts}`,
          {
            requestId,
          },
        );

        const processingPromisesTele = [];
        if (teleContacts.length > 0) {
          processingPromisesTele.push(
            this.processTeleContactsOptimized(teleContacts, messageData, helpers),
          );
        }
        const teleResults = await Promise.allSettled(processingPromisesTele);
        const teleFinalMetrics = this.processFinalResults();
        workerLog('SUCCESS', 'Tele Api Bulk SMS processing completed for batch', {
          // Changed log message for clarity
          requestId,
          ...teleFinalMetrics,
        });

        const processingPromisesSafari = [];
        if (safariContacts.length > 0) {
          processingPromisesSafari.push(
            this.processSafariContactsOptimized(safariContacts, messageData, helpers),
          );
        }
        const safariResults = await Promise.allSettled(processingPromisesSafari);
        const safariFinalMetrics = this.processFinalResults();
        workerLog('SUCCESS', 'Safari Api Bulk SMS processing completed for batch', {
          // Changed log message for clarity
          requestId,
          ...safariFinalMetrics,
        });
      }
      finalMetrics.messageId = messageData.id || messageData.messageId || messageData.requestId || undefined;
      return finalMetrics;
    } catch (error) {
      this.metrics.recordError(error, { operation: 'processBulkSMS' });
      workerLog('ERROR', 'SMS processing error', { error: error.message, stack: error.stack });
      finalMetrics = this.processFinalResults(); // Ensure finalMetrics is a properly structured object
      finalMetrics.messageId = messageData.id || messageData.messageId || messageData.requestId || undefined;
      throw error;
    } finally {
      this.isProcessing = false;
      // Ensure finalMetrics is updated with end time even on error
      if (finalMetrics) {
        // This check is good practice
        finalMetrics.endTime = performance.now();
        // Only calculate totalProcessingTime if startTime was actually set
        if (this.metrics.metrics.startTime) {
          finalMetrics.totalProcessingTime = finalMetrics.endTime - this.metrics.metrics.startTime;
        }
      }
    }
  }

  async processTeleContactsOptimized(teleContacts, messageData, helpers) {
    const batchSize = this.config.TELE_BATCH_SIZE;
    const concurrency = this.config.TELE_CONCURRENCY;
    return await this.processContactsWithConcurrency(
      teleContacts,
      batchSize,
      concurrency,
      'tele',
      messageData,
      helpers,
    );
  }

  async processSafariContactsOptimized(safariContacts, messageData, helpers) {
    const batchSize = this.config.SAFARI_BATCH_SIZE;
    const concurrency = this.config.SAFARI_CONCURRENCY;
    return await this.processContactsWithConcurrency(
      safariContacts,
      batchSize,
      concurrency,
      'safari',
      messageData,
      helpers,
    );
  }

  async processContactsWithConcurrency(
    contacts,
    batchSize,
    concurrency,
    type,
    messageData,
    helpers,
  ) {
    const batches = this.createBatches(contacts, batchSize);
    const results = [];
    let totalSuccessfulInType = 0;
    let totalFailedInType = 0;
    for (let i = 0; i < batches.length; i += concurrency) {
      if (this.shouldStop) {
        workerLog('WARN', 'Processing stopped by request', { type });
        break;
      }
      const batchGroup = batches.slice(i, i + concurrency);
      const batchPromises = batchGroup.map((batch, index) => {
        const batchIndex = i + index;
        const batchStartTime = performance.now();
        return this.processBatchWithResilience(batch, type, i + index, messageData, helpers).then(
          (result) => {
            const duration = performance.now() - batchStartTime;
            workerLog(
              'DEBUG',
              `Batch ${batchIndex} (${type}) processed in ${duration.toFixed(2)}ms`,
            );
            return result;
          },
        );
      });
      const batchResults = await Promise.allSettled(batchPromises);
      results.push(...batchResults);

      // Aggregate successes and failures from this group of batches
      batchResults.forEach((result) => {
        if (result.status === 'fulfilled' && result.value) {
          totalSuccessfulInType += result.value.successful;
          totalFailedInType += result.value.failed;
        } else {
          // If a batch promise rejected, assume all contacts in that batch failed for this specific type.
          // This is a simplification; a more robust solution might require knowing the original batch's contact count.
          // For now, we only rely on the 'failedContacts' increment in processBatchWithResilience.
        }
      });

      if (this.metrics.metrics.memoryUsage > this.config.MAX_MEMORY_USAGE) {
        workerLog('WARN', 'High memory usage detected, triggering GC', {
          memoryUsage: this.metrics.metrics.memoryUsage,
        });
        if (global.gc) {
          global.gc();
          workerLog('INFO', 'Manual garbage collection triggered');
        } else {
          workerLog('WARN', 'GC not available — start with node --expose-gc to enable manual GC');
        }
      }
    }
    return {
      successful: totalSuccessfulInType,
      failed: totalFailedInType,
      results,
    };
  }

  createBatches(contacts, batchSize) {
    const batches = [];
    for (let i = 0; i < contacts.length; i += batchSize) {
      batches.push(contacts.slice(i, i + batchSize));
    }
    return batches;
  }

  async processBatchWithResilience(batch, type, batchIndex, messageData, helpers) {
    const startTime = performance.now();
    const circuitBreaker = type === 'tele' ? this.teleCircuitBreaker : this.safariCircuitBreaker;
    const rateLimiter = type === 'tele' ? this.teleRateLimiter : this.safariRateLimiter;
    let attempt = 0;
    let lastError;
    while (attempt < this.config.RETRY_ATTEMPTS) {
      try {
        await rateLimiter.throttle();
        const result = await circuitBreaker.execute(
          this.processBatchDirect.bind(this),
          batch,
          type,
          batchIndex,
          messageData,
          helpers,
        );
        rateLimiter.onSuccess();
        const processingTime = performance.now() - startTime;
        this.metrics.recordProcessingTime(processingTime);
        this.metrics.updateMetrics({
          batchesProcessed: this.metrics.metrics.batchesProcessed + 1,
          processedContacts: this.metrics.metrics.processedContacts + batch.length,
          successfulContacts: this.metrics.metrics.successfulContacts + result.successful,
          failedContacts: this.metrics.metrics.failedContacts + result.failed,
        });
        return result;
      } catch (error) {
        lastError = error;
        attempt++;
        rateLimiter.onError();
        if (attempt < this.config.RETRY_ATTEMPTS) {
          const delay = this.config.RETRY_DELAY * Math.pow(2, attempt - 1);
          workerLog('WARN', `Retrying batch ${batchIndex} (attempt ${attempt})`, {
            type,
            delay,
            error: error.message,
          });
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    }
    this.metrics.updateMetrics({
      batchesFailed: this.metrics.metrics.batchesFailed + 1,
      processedContacts: this.metrics.metrics.processedContacts + batch.length,
      failedContacts: this.metrics.metrics.failedContacts + batch.length,
    });
    this.metrics.recordError(lastError, {
      type,
      batchIndex,
      batchSize: batch.length,
      allRetriesExhausted: true,
    });
    throw lastError;
  }

  async processBatchDirect(batch, type, batchIndex, messageData, helpers) {
    const messageContent = helpers.extractMessageContent(messageData);
    workerLog('DEBUG', `Process Batch ${JSON.stringify(messageData)}`);
    const { sms_credential, requestId, sms_body_id: bulk_id } = messageData;
    if (type === 'tele') {
      return await helpers.sendTeleBatch(batch, messageContent, sms_credential, bulk_id, requestId);
    } else if (type === 'safari') {
      return await helpers.sendSafariBatch(
        batch,
        messageContent,
        sms_credential,
        bulk_id,
        requestId,
      );
    }
    throw new Error(`Unknown contact type: ${type}`);
  }

  processFinalResults(results) {
    // const finalMetrics = {
    //   totalBatches: results.length,
    //   successfulBatches: results.filter((r) => r.status === 'fulfilled').length,
    //   failedBatches: results.filter((r) => r.status === 'rejected').length,
    //   ...this.metrics.metrics,
    // };
    const finalMetrics = { ...this.metrics.metrics };
    finalMetrics.endTime = performance.now();
    if (finalMetrics.startTime) {
      // Ensure startTime exists before calculating
      finalMetrics.totalProcessingTime = finalMetrics.endTime - finalMetrics.startTime;
    }
    // finalMetrics.totalProcessingTime = finalMetrics.endTime - this.metrics.metrics.startTime;
    return finalMetrics;
  }

  performHealthCheck() {
    const health = this.metrics.getHealthStatus();
    const { memoryUsage, errorRate } = health.metrics;

    if (errorRate > 50) {
      this.metrics.isHealthy = false;
      workerLog('WARN', 'High error rate detected', {
        errorRate,
        topErrors: health.topErrors,
      });
    }

    if (health.metrics.memoryUsage > this.config.MAX_MEMORY_USAGE) {
      workerLog('WARN', 'High memory usage detected', { memoryUsage: health.metrics.memoryUsage });
    }

    // Shutdown if there is critical high usage
    if (memoryUsage > this.config.CRITICAL_MEMORY_USAGE) {
      this.shouldStop = true;
      this.gracefulShutdown('critical_memory');
      workerLog('ERROR', 'Memory usage critical — initiating emergency shutdown', { memoryUsage });
    }
  }

  gracefulShutdown(reason = 'manual/unknown') {
    this.shouldStop = true;
    clearInterval(this.healthCheckInterval);
    workerLog('INFO', `Graceful shutdown initiated: reason = ${reason}`);
  }
}

module.exports = {
  EnterpriseBulkSMSProcessor,
  BulkSMSMetrics,
  CircuitBreaker,
  AdaptiveRateLimiter,
  setEnterpriseBulkSMSConfig: (config) => {
    /* deprecated, use instance config */
  },
};
