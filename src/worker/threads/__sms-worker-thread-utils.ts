/**
 * Utility functions for SMS worker thread
 */

/**
 * Truncate a message to a specified length with ellipsis
 *
 * @param message - The message to truncate
 * @param maxLength - The maximum length of the message (default: 50)
 * @returns The truncated message
 */
export function truncateMessage(message?: string, maxLength = 50): string {
  if (!message) {
    return '(empty message)';
  }

  if (message.length <= maxLength) {
    return message;
  }

  return `${message.substring(0, maxLength - 3)}...`;
}

/**
 * Extract message content from data object or string
 * Handles different field names used in different parts of the system
 *
 * @param data - The data object or string containing the message
 * @returns The message content
 */
export function extractMessageContent(data: any): string {
  if (typeof data === 'string') {
    return data;
  }
  
  if (data && typeof data === 'object') {
    // Check different possible field names for message content
    return data.message || data.msg || data.content || data.text || '';
  }
  
  return '';
}

/**
 * Generate a unique message ID
 *
 * @returns A unique message ID
 */
export function generateMessageId(): string {
  return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
}

/**
 * Calculate a hash for a message
 * Useful for message deduplication and tracking
 *
 * @param message - The message to hash
 * @returns A numeric hash of the message
 */
export function hashMessage(message?: string): number {
  if (!message) {
    return 0;
  }

  let hash = 0;
  for (let i = 0; i < message.length; i++) {
    const char = message.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return hash;
}

/**
 * Calculate exponential backoff time with jitter for retries
 *
 * @param attempt - The current attempt number (0-based)
 * @param baseMs - The base time in milliseconds (default: 1000)
 * @param maxMs - The maximum time in milliseconds (default: 30000)
 * @returns The backoff time in milliseconds
 */
export function calculateBackoffTime(attempt: number, baseMs = 1000, maxMs = 30000): number {
  // Exponential backoff with jitter to prevent thundering herd
  const jitter = Math.random() * 100;
  const backoffTime = Math.min(baseMs * Math.pow(2, attempt) + jitter, maxMs);
  return backoffTime;
}

/**
 * Determine the phone type based on the phone number prefix
 *
 * @param phoneNumber - The phone number to check
 * @returns The phone type ('TELE', 'SAFARICOM', or 'UNKNOWN')
 */
export function phoneType(phoneNumber?: string | number): string {
  const phoneNumberString = String(phoneNumber || '');

  if (phoneNumberString.startsWith("2519")) {
    return "TELE";
  } else if (phoneNumberString.startsWith("2517")) {
    return "SAFARICOM";
  } else {
    return "UNKNOWN";
  }
}
