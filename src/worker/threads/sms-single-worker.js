const { parentPort, workerData } = require('worker_threads');
const {
  truncateMessage,
  extractMessageContent,
  hashMessage,
} = require('./sms-worker-thread-utils');
const { processSms } = require('./sms-provider-utils');
const iconv = require('iconv-lite');
const fs = require('fs');
const path = require('path');
const { EnterpriseSingleSMSProcessor } = require('./enterprise-single-sms-processor');

// Get worker ID from worker data
const workerId = workerData?.workerId || 0;

// Initialize worker-specific logging
const logsDir = process.env.LOGS_DIR || path.join(process.cwd(), 'logs');
const errorLogPath = path.join(logsDir, `worker-${workerId}-error.log`);
const successLogPath = path.join(logsDir, `worker-${workerId}-success.log`);

// Ensure logs directory exists
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Create log streams
const errorLogStream = fs.createWriteStream(errorLogPath, { flags: 'a' });
const successLogStream = fs.createWriteStream(successLogPath, { flags: 'a' });

// Worker-specific logging function
function workerLog(level, message, data = {}) {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    level,
    workerId,
    message,
    ...data,
  };

  const logString = JSON.stringify(logEntry) + '\n';

  // Write to appropriate log file
  if (level === 'ERROR') {
    errorLogStream.write(logString);
  } else {
    successLogStream.write(logString);
  }

  // Also send to parent for console output in development
  parentPort.postMessage({
    type: 'log',
    level: level.toLowerCase(),
    text: `[Worker-${workerId}] ${message}`,
    data,
    workerType: 'single',
  });
}

function convertString(msg) {
  const utf16beEncoded = iconv.encode(msg, 'UTF-16BE');
  return utf16beEncoded.toString('hex').toUpperCase();
}

// Add this function to calculate current throughput
function getCurrentThroughputPerSecond() {
  if (messageTimestamps.length === 0) return 0;

  const now = Date.now();
  const oneSecondAgo = now - 1000;
  const tenSecondsAgo = now - 10000;
  const oneMinuteAgo = now - 60000;

  // workerLog('DEBUG', 'Throughput calculation context', {
  //   now: new Date(now).toISOString(),
  //   oneSecondAgo: new Date(oneSecondAgo).toISOString(),
  //   latestTimestampInArray:
  //     messageTimestamps.length > 0
  //       ? new Date(Math.max(...messageTimestamps)).toISOString()
  //       : 'N/A',
  //   oldestTimestampInArray:
  //     messageTimestamps.length > 0
  //       ? new Date(Math.min(...messageTimestamps)).toISOString()
  //       : 'N/A',
  //   totalTimestamps: messageTimestamps.length,
  // });

  const messagesLastSecond = messageTimestamps.filter((t) => t > oneSecondAgo).length;
  const messagesLast10Seconds = messageTimestamps.filter((t) => t > tenSecondsAgo).length;
  const messagesLastMinute = messageTimestamps.filter((t) => t > oneMinuteAgo).length;

  workerLog('DEBUG', `[Single] [${workerId}] Timestamp: ${JSON.stringify(messageTimestamps)}`);

  workerLog(
    'DEBUG',
    `[Single] [${workerId}] Throughput: ${JSON.stringify({
      lastSecond: messagesLastSecond,
      last10Seconds: (messagesLast10Seconds / 10).toFixed(2),
      lastMinute: (messagesLastMinute / 60).toFixed(2),
    })}`,
  );
  return {
    lastSecond: messagesLastSecond,
    last10Seconds: (messagesLast10Seconds / 10).toFixed(2),
    lastMinute: (messagesLastMinute / 60).toFixed(2),
  };
}

// Send heartbeats to parent
setInterval(() => {
  const throughput = getCurrentThroughputPerSecond();
  parentPort.postMessage({
    type: 'heartbeat',
    workerId,
    timestamp: new Date().toISOString(),
    throughput: throughput.last10Seconds,
    totalProcessed: messagesProcessed,
    successfulMessages,
    failedMessages,
    workerType: 'single',
  });
}, 5000);

// Log worker startup
workerLog('INFO', 'Worker started successfully', { workerId });

// Performance tracking
let messagesProcessed = 0;
let totalProcessingTime = 0;
let startTime = Date.now();

// Add these new variables for throughput tracking
let messageTimestamps = [];
let successfulMessages = 0;
let failedMessages = 0;

// Instantiate the enterprise single SMS processor with workerLog injection
const enterpriseSingleProcessor = new EnterpriseSingleSMSProcessor({ workerLog });

// Listen for messages from the main thread
parentPort.on('message', async (data) => {
  const { id, data: messageData } = data;

  // Use the enterprise processor for all single SMS jobs
  const result = await enterpriseSingleProcessor.processSingleSMS(messageData, {
    extractMessageContent,
    processSms,
  });
  workerLog(
    'INFO',
    `[Single] [${workerId}] Worker successfully completed work of: ${JSON.stringify(result)}`,
  );

  messageTimestamps.push(result.data.timestamp);

  workerLog('INFO', `[Single] [${workerId}] Timestamp: ${JSON.stringify(messageTimestamps)}`);

  if (result.success) {
    successfulMessages++;
  } else {
    failedMessages++;
  }
  messagesProcessed++;

  // Post result back to parent
  parentPort.postMessage({ ...result, id, workerType: 'single' });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  workerLog('INFO', 'Worker shutting down gracefully');
  errorLogStream.end();
  successLogStream.end();
  process.exit(0);
});

process.on('SIGINT', () => {
  workerLog('INFO', 'Worker shutting down gracefully');
  errorLogStream.end();
  successLogStream.end();
  process.exit(0);
});

// Export performance metrics using enterprise processor
module.exports = {
  getMetrics: () => enterpriseSingleProcessor.getMetrics(),
};
