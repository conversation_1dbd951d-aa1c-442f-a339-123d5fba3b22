// Enterprise Single SMS Processor Module
// Extracted and refactored from sms-single-worker.js for modular, robust, and testable single SMS processing

const { performance } = require('perf_hooks');
const EventEmitter = require('events');

/**
 * Enterprise-grade Single SMS Processor
 * Handles metrics, logging, and robust error handling for single SMS jobs.
 *
 * @param {object} options - { workerLog }
 */
class EnterpriseSingleSMSProcessor {
  constructor(options = {}) {
    this.workerLog = options.workerLog || console.log;
    this.metrics = {
      messagesProcessed: 0,
      totalProcessingTime: 0,
      successfulMessages: 0,
      failedMessages: 0,
      messageTimestamps: [],
      startTime: performance.now(),
    };
  }

  /**
   * Process a single SMS message
   * @param {object} messageData
   * @param {object} helpers - { extractMessageContent, processSms }
   * @returns {Promise<object>} result
   */
  async processSingleSMS(messageData, helpers) {
    const { extractMessageContent, processSms } = helpers;
    if (typeof extractMessageContent !== 'function' || typeof processSms !== 'function') {
      throw new Error('Missing required helper functions: extractMessageContent, processSms');
    }
    const {
      sms_credential,
      to,
      message,
      attempt,
      requestId,
      sms_log_id,
      assignmentValue,
      messageType,
    } = messageData;
    const messageContent = message;
    const processingStartTime = performance.now();
    try {
      this.workerLog('INFO', `Received ${messageType || 'SMS'} message`, {
        requestId,
        to,
        provider: sms_credential.sms_service_provider,
        attempt,
        assignmentValue,
      });
      this.workerLog('INFO', `Processing SMS via ${sms_credential.sms_service_provider} - ${to}, ${messageContent}, ${sms_log_id}, ${requestId}`, {
        requestId,
        to,
        provider: sms_credential.sms_service_provider,
        messageLength: messageContent.length,
      });
      const result = await processSms(sms_credential, to, messageContent, sms_log_id, requestId);
      const processingTime = performance.now() - processingStartTime;
      this.metrics.messagesProcessed++;
      this.metrics.totalProcessingTime += processingTime;
      if (result.status === 'delivered') {
        this.metrics.messageTimestamps.push(Date.now());
        this.metrics.successfulMessages++;
        // Keep only last 60 seconds of timestamps
        const oneMinuteAgo = Date.now() - 60000;
        this.metrics.messageTimestamps = this.metrics.messageTimestamps.filter((timestamp) => timestamp > oneMinuteAgo);
        this.workerLog('SUCCESS', `SMS delivered successfully`, {
          requestId,
          to,
          provider: sms_credential.sms_service_provider,
          processingTime,
          messageType,
          providerResponse: result.responseData,
        });
        return {
          type: 'result',
          success: true,
          data: {
            ...result,
            status: 'sent',
            provider: sms_credential.sms_service_provider,
            to,
            timestamp: new Date().toISOString(),
            requestId,
            processingTime,
            messageType,
          },
          requestId,
        };
      } else {
        this.metrics.failedMessages++;
        throw new Error(`SMS delivery failed: ${result.status}`);
      }
    } catch (error) {
      const processingTime = performance.now() - processingStartTime;
      this.metrics.failedMessages++;
      this.workerLog('ERROR', `Failed to process SMS`, {
        requestId,
        to,
        provider: sms_credential.sms_service_provider,
        error: error.message,
        providerDetails: { provider: error.provider },
        processingTime,
        messageType,
      });
      return {
        type: 'result',
        success: false,
        error: error.message,
        provider: sms_credential.sms_service_provider,
        to,
        attempt,
        requestId,
        processingTime,
        messageType,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Get real-time and aggregate metrics
   */
  getMetrics() {
    const uptimeSeconds = (performance.now() - this.metrics.startTime) / 1000;
    const overallThroughput = this.metrics.messagesProcessed / uptimeSeconds;
    const now = Date.now();
    const oneSecondAgo = now - 1000;
    const tenSecondsAgo = now - 10000;
    const oneMinuteAgo = now - 60000;
    const messagesLastSecond = this.metrics.messageTimestamps.filter((t) => t > oneSecondAgo).length;
    const messagesLast10Seconds = this.metrics.messageTimestamps.filter((t) => t > tenSecondsAgo).length;
    const messagesLastMinute = this.metrics.messageTimestamps.filter((t) => t > oneMinuteAgo).length;
    return {
      messagesProcessed: this.metrics.messagesProcessed,
      totalProcessingTime: this.metrics.totalProcessingTime,
      averageProcessingTime: this.metrics.messagesProcessed > 0 ? this.metrics.totalProcessingTime / this.metrics.messagesProcessed : 0,
      uptime: performance.now() - this.metrics.startTime,
      successfulMessages: this.metrics.successfulMessages,
      failedMessages: this.metrics.failedMessages,
      uptimeSeconds,
      overallThroughputPerSecond: parseFloat(overallThroughput.toFixed(2)),
      overallThroughputPerMinute: parseFloat((overallThroughput * 60).toFixed(2)),
      overallThroughputPerHour: parseFloat((overallThroughput * 3600).toFixed(2)),
      currentThroughput: {
        lastSecond: messagesLastSecond,
        last10Seconds: (messagesLast10Seconds / 10).toFixed(2),
        lastMinute: (messagesLastMinute / 60).toFixed(2),
      },
      successRate: this.metrics.messagesProcessed > 0 ? ((this.metrics.successfulMessages / this.metrics.messagesProcessed) * 100).toFixed(2) : 0,
    };
  }
}

module.exports = {
  EnterpriseSingleSMSProcessor,
};
