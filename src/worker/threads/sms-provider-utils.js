const { default: axios } = require('axios');
const iconv = require('iconv-lite');

// App API URL and Port
const APP_URL = process.env.APP_URL;
const PORT = process.env.PORT;
// SMS Gateways API Endpoints
const PL_API_URL = process.env.PL_API_URL;
const TELE_JASMINE_API_URL = process.env.TELE_JASMINE_API_URL;
const SAFARICOM_JASMINE_API_URL =
  process.env.SAFARICOM_JASMINE_API_URL || 'http://196.189.61.87:443/api/v2/sendbulk';
const SMPP_NESTJS_API_URL = process.env.SMPP_NESTJS_API_URL;

function convertString(msg) {
  const utf16beEncoded = iconv.encode(msg, 'UTF-16BE');
  return utf16beEncoded.toString('hex').toUpperCase();
}

// Optimized SMS processing function
async function processSms(sms_credentials, toNumber, message, sms_log_id, requestId = 'unknown') {
  const provider = sms_credentials.sms_service_provider;

  try {
    switch (provider) {
      case 'TELE':
        return await sendViaTELE(sms_credentials, sms_log_id, toNumber, message, requestId);
      case 'SAFARICOM':
        return await sendViaSAFARICOM(sms_credentials, sms_log_id, toNumber, message, requestId);
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  } catch (error) {
    throw error;
  }
}

// Optimized TELE provider implementation
async function sendViaTELE(sms_credentials, bulk_id, phoneNumber, message, requestId) {
  const apiUrl = SMPP_NESTJS_API_URL;
  try {
    const extraInfo = new URLSearchParams({
      msg: message,
      to: phoneNumber,
      bid: bulk_id.toString(),
      sms_credential: sms_credentials,
    });

    const payload = {
      messageId: bulk_id.toString(),
      from: sms_credentials.from,
      to: phoneNumber,
      text: message,
      encoding: 'gsm7',
      connectorId: 'geez-connector',
      strategy: 'round_robin',
      callbackUrl: `${APP_URL}/api/v1/sendbulk/single/geez-smpp/${bulk_id}?${extraInfo}`, // TODO
      reference: `order-${bulk_id}`,
      esmClass: 0,
      registeredDelivery: 1,
      source_addr_ton: 5,
      source_addr_npi: 0,
      dest_addr_ton: 0,
      dest_addr_npi: 0,
    };

    // return { status: 'delivered', provider: 'TELE', responseData: 'response.data' };
    // const response = await axios.post(SMPP_NESTJS_API_URL, payload);
    const response = await axios.post(SMPP_NESTJS_API_URL, payload, {
      timeout: 10000, // Always set a timeout to prevent stuck requests from blocking RabbitMQ ack
    });

    if (response.status === 200) {
      return { status: 'delivered', provider: 'TELE', responseData: response.data };
    } else {
      throw new Error(`TELE API returned status ${response.status}`);
    }

    // Old way jasmin
    // const jasminPayload = {
    //   hex_content: convertString(message),
    //   dlr: 'yes',
    //   coding: '8', // make sure this is 8
    //   'dlr-level': sms_credentials.sdp ? 3 : 1,
    //   'dlr-method': 'POST',
    //   'dlr-url': `http://host.docker.internal:${config.port}/api/sendbulk/single/${bid}?${url}`,
    //   username: sms_credentials.usr,
    //   password: sms_credentials.pwd,
    //   from: sms_credentials.from,
    //   to: phoneNumber,
    //   // 'priority': 3,
    //   // 'validity-period': 1440,
    // };

    // const response2 = await axios.post(
    //   apiUrl,
    //   {
    //     usr: sms_credentials.usr,
    //     pwd: sms_credentials.pwd,
    //     from: sms_credentials.from,
    //     to: phoneNumber,
    //     msg: message,
    //     token: sms_credentials.token,
    //     sdp: sms_credentials.sdp || 'default',
    //   },
    //   {
    //     timeout: 10000, // 10 second timeout
    //     headers: {
    //       'Content-Type': 'application/json',
    //     },
    //   },
    // );

    // let jasminRestURL = 'http://127.0.0.1:8080/secure/send';
    // if (sms_credentials.sdp) jasminRestURL = 'http://127.0.0.1:28080/secure/send';

    // const jasmineRes = await axios.post(jasminRestURL, jasminPayload, {
    //   headers: {
    //     'Content-Type': 'application/json',
    //   },
    //   auth: {
    //     username: sms_credentials.usr,
    //     password: sms_credentials.pwd,
    //   },
    // });

    // if (jasmineRes.status === 200) {
    //   return { status: 'delivered', provider: 'TELE', responseData: jasmineRes.data };
    // } else {
    //   throw new Error(`TELE API returned status ${jasmineRes.status}`);
    // }
  } catch (error) {
    const newError = new Error(`TELE SMS failed: ${error}`);
    newError.originalError = error;
    newError.provider = 'TELE';
    throw newError;
  }
}

// Optimized SAFARICOM provider implementation
async function sendViaSAFARICOM(sms_credentials, bulk_id, phoneNumber, message, requestId) {
  try {
    const payload = {
      msg: message,
      phone: phoneNumber,
      from: sms_credentials.from,
      sms_log_id: bulk_id.toString(),
    };

    // return { status: 'delivered', provider: 'SAFARICOM', responseData: 'response.data' };
    const response = await axios.post(SMPP_NESTJS_API_URL, payload, {
      timeout: 10000, // Always set a timeout to prevent stuck requests from blocking RabbitMQ ack
    });

    if (response.status === 200) {
      return { status: 'delivered', provider: 'SAFARICOM', responseData: response.data };
    } else {
      throw new Error(`SAFARICOM API returned status ${response.status}`);
    }
  } catch (error) {
    const newError = new Error(`SAFARICOM SMS failed: ${error.message}`);
    newError.originalError = error;
    newError.provider = 'SAFARICOM';
    throw newError;
  }
}

module.exports = {
  processSms,
  sendViaTELE,
  sendViaSAFARICOM,
};
