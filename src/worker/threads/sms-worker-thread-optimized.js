const { parentPort, workerData } = require('worker_threads');
const {
  truncateMessage,
  extractMessageContent,
  hashMessage,
} = require('./sms-worker-thread-utils');
const https = require('https');
const http = require('http');
const url = require('url');
const iconv = require('iconv-lite');
const { default: axios } = require('axios');
const fs = require('fs');
const path = require('path');

const APP_URL = process.env.APP_URL;
const PORT = process.env.PORT;
const TELE_JASMINE_API_URL = process.env.TELE_JASMINE_API_URL;
const TELE_ENDPOINT = process.env.TELE_ENDPOINT;

// Get worker ID from worker data
const workerId = workerData?.workerId || 0;

// Initialize worker-specific logging
const logsDir = process.env.LOGS_DIR || path.join(process.cwd(), 'logs');
const errorLogPath = path.join(logsDir, `worker-${workerId}-error.log`);
const successLogPath = path.join(logsDir, `worker-${workerId}-success.log`);

// Ensure logs directory exists
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Create log streams
const errorLogStream = fs.createWriteStream(errorLogPath, { flags: 'a' });
const successLogStream = fs.createWriteStream(successLogPath, { flags: 'a' });

// Worker-specific logging function
function workerLog(level, message, data = {}) {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    level,
    workerId,
    message,
    ...data,
  };

  const logString = JSON.stringify(logEntry) + '\n';

  // Write to appropriate log file
  if (level === 'ERROR') {
    errorLogStream.write(logString);
  } else {
    successLogStream.write(logString);
  }

  // Also send to parent for console output in development
  parentPort.postMessage({
    type: 'log',
    level: level.toLowerCase(),
    text: `[Worker-${workerId}] ${message}`,
    data,
  });
}

function convertString(msg) {
  const utf16beEncoded = iconv.encode(msg, 'UTF-16BE');
  return utf16beEncoded.toString('hex').toUpperCase();
}

// Add this function to calculate current throughput
function getCurrentThroughputPerSecond() {
  if (messageTimestamps.length === 0) return 0;

  const now = Date.now();
  const oneSecondAgo = now - 1000;
  const tenSecondsAgo = now - 10000;
  const oneMinuteAgo = now - 60000;

  const messagesLastSecond = messageTimestamps.filter((t) => t > oneSecondAgo).length;
  const messagesLast10Seconds = messageTimestamps.filter((t) => t > tenSecondsAgo).length;
  const messagesLastMinute = messageTimestamps.filter((t) => t > oneMinuteAgo).length;

  return {
    lastSecond: messagesLastSecond,
    last10Seconds: (messagesLast10Seconds / 10).toFixed(2),
    lastMinute: (messagesLastMinute / 60).toFixed(2),
  };
}

// Send heartbeats to parent
setInterval(() => {
  const throughput = getCurrentThroughputPerSecond();
  parentPort.postMessage({
    type: 'heartbeat',
    workerId,
    timestamp: new Date().toISOString(),
    throughput: throughput.last10Seconds,
    totalProcessed: messagesProcessed,
    successfulMessages,
    failedMessages,
  });
}, 5000);

// Log worker startup
workerLog('INFO', 'Worker started successfully', { workerId });

// Performance tracking
let messagesProcessed = 0;
let totalProcessingTime = 0;
let startTime = Date.now();

// Add these new variables for throughput tracking
let messageTimestamps = [];
let successfulMessages = 0;
let failedMessages = 0;

// Listen for messages from the main thread
parentPort.on('message', async (data) => {
  const { id, data: messageData } = data;

  const {
    sms_credentials,
    to,
    attempt,
    sms_log_id,
    requestId = 'unknown',
    assignmentValue,
    messageType,
  } = messageData;

  const messageContent = extractMessageContent(messageData);
  const processingStartTime = Date.now();

  try {
    workerLog('INFO', `Received ${messageType || 'SMS'} message`, {
      requestId,
      messageId: id,
      to,
      provider: sms_credentials.sms_service_provider,
      attempt,
      assignmentValue,
    });

    const result = await processSms(
      sms_credentials,
      to,
      messageContent,
      sms_log_id,
      requestId,
      assignmentValue,
    );

    const processingTime = Date.now() - processingStartTime;

    // Update performance metrics
    messagesProcessed++;
    totalProcessingTime += processingTime;

    if (result.status === 'delivered') {
      // Track successful message
      messageTimestamps.push(Date.now());
      successfulMessages++;

      // Keep only last 60 seconds of timestamps
      const oneMinuteAgo = Date.now() - 60000;
      messageTimestamps = messageTimestamps.filter((timestamp) => timestamp > oneMinuteAgo);

      workerLog('SUCCESS', `SMS delivered successfully`, {
        requestId,
        messageId: id,
        to,
        provider: sms_credentials.sms_service_provider,
        processingTime,
        messageType,
      });

      parentPort.postMessage({
        type: 'result',
        success: true,
        data: {
          ...result,
          status: 'sent',
          provider: sms_credentials.sms_service_provider,
          to,
          timestamp: new Date().toISOString(),
          requestId,
          processingTime,
          messageType,
        },
        requestId,
        id,
      });
    } else {
      failedMessages++;
      throw new Error(`SMS delivery failed: ${result.status}`);
    }
  } catch (error) {
    const processingTime = Date.now() - processingStartTime;

    workerLog('ERROR', `Failed to process SMS`, {
      requestId,
      messageId: id,
      to,
      provider: sms_credentials.sms_service_provider,
      error: error.message,
      processingTime,
      messageType,
    });

    parentPort.postMessage({
      type: 'result',
      success: false,
      error: error.message,
      provider: sms_credentials.sms_service_provider,
      to,
      attempt,
      requestId,
      id,
      processingTime,
      messageType,
      timestamp: new Date().toISOString(),
    });
  }
});

// Optimized SMS processing function
async function processSms(
  sms_credentials,
  toNumber,
  message,
  sms_log_id,
  requestId = 'unknown',
  assignmentValue,
) {
  const provider = sms_credentials.sms_service_provider;

  workerLog('INFO', `Processing SMS via ${provider}`, {
    requestId,
    to: toNumber,
    provider,
    messageLength: message.length,
  });

  try {
    switch (provider) {
      case 'TELE':
        return await sendViaTELE(sms_credentials, toNumber, message, sms_log_id, requestId);
      case 'SAFARICOM':
        return await sendViaSAFARICOM(sms_credentials, toNumber, message, requestId);
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  } catch (error) {
    workerLog('ERROR', `Provider error`, {
      requestId,
      provider,
      error: error.message,
    });
    throw error;
  }
}

// Optimized TELE provider implementation
async function sendViaTELE(sms_credentials, phoneNumber, message, sms_log_id, requestId) {
  const apiUrl = TELE_ENDPOINT || TELE_JASMINE_API_URL || 'https://api.tele.com/send';

  try {
    // const response = await axios.post(apiUrl, {
    //   usr: sms_credentials.usr,
    //   pwd: sms_credentials.pwd,
    //   from: sms_credentials.from,
    //   to: phoneNumber,
    //   msg: message,
    //   token: sms_credentials.token,
    //   sdp: sms_credentials.sdp || 'default'
    // }, {
    //   timeout: 10000, // Always set a timeout to prevent stuck requests from blocking RabbitMQ ack
    //   headers: {
    //     'Content-Type': 'application/json'
    //   }
    // });

    workerLog('SUCCESS', `TELE SMS sent successfully`, {
      requestId,
      to: phoneNumber,
      //   responseStatus: response.status
      responseStatus: 200,
    });
    return { status: 'delivered', provider: 'TELE' };
    if (response.status === 200) {
    } else {
      throw new Error(`TELE API returned status ${response.status}`);
    }
  } catch (error) {
    workerLog('ERROR', `TELE SMS failed`, {
      requestId,
      to: phoneNumber,
      error: error.message,
    });
    throw error;
  }
}

// Optimized SAFARICOM provider implementation
async function sendViaSAFARICOM(sms_credentials, phoneNumber, message, requestId) {
  const apiUrl = process.env.SAFARICOM_JASMINE_API_URL || 'https://api.safaricom.com/send';

  try {
    // const response = await axios.post(apiUrl, {
    //   usr: sms_credentials.usr,
    //   pwd: sms_credentials.pwd,
    //   from: sms_credentials.from,
    //   to: phoneNumber,
    //   msg: message,
    //   token: sms_credentials.token
    // }, {
    //   timeout: 10000, // Always set a timeout to prevent stuck requests from blocking RabbitMQ ack
    //   headers: {
    //     'Content-Type': 'application/json'
    //   }
    // });

    workerLog('SUCCESS', `SAFARICOM SMS sent successfully`, {
      requestId,
      to: phoneNumber,
      //   responseStatus: response.status
      responseStatus: 200,
    });
    return { status: 'delivered', provider: 'SAFARICOM' };
    if (response.status === 200) {
    } else {
      throw new Error(`SAFARICOM API returned status ${response.status}`);
    }
  } catch (error) {
    workerLog('ERROR', `SAFARICOM SMS failed`, {
      requestId,
      to: phoneNumber,
      error: error.message,
    });
    throw error;
  }
}

// Graceful shutdown
process.on('SIGTERM', () => {
  workerLog('INFO', 'Worker shutting down gracefully');
  errorLogStream.end();
  successLogStream.end();
  process.exit(0);
});

process.on('SIGINT', () => {
  workerLog('INFO', 'Worker shutting down gracefully');
  errorLogStream.end();
  successLogStream.end();
  process.exit(0);
});

// Export performance metrics
module.exports = {
  getMetrics: () => {
    const uptimeSeconds = (Date.now() - startTime) / 1000;
    const overallThroughput = messagesProcessed / uptimeSeconds;
    const currentThroughput = getCurrentThroughputPerSecond();

    return {
      messagesProcessed,
      totalProcessingTime,
      averageProcessingTime: messagesProcessed > 0 ? totalProcessingTime / messagesProcessed : 0,
      uptime: Date.now() - startTime,

      successfulMessages,
      failedMessages,

      uptimeSeconds,

      // Throughput metrics
      overallThroughputPerSecond: parseFloat(overallThroughput.toFixed(2)),
      overallThroughputPerMinute: parseFloat((overallThroughput * 60).toFixed(2)),
      overallThroughputPerHour: parseFloat((overallThroughput * 3600).toFixed(2)),

      // Real-time throughput
      currentThroughput: currentThroughput,

      // Success rate
      successRate:
        messagesProcessed > 0 ? ((successfulMessages / messagesProcessed) * 100).toFixed(2) : 0,
    };
  },
};
