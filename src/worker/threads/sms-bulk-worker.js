const { parentPort, workerData } = require('worker_threads');
const { extractMessageContent } = require('./sms-worker-thread-utils');
const { processSms } = require('./sms-provider-utils');
const iconv = require('iconv-lite');
const fs = require('fs');
const path = require('path');
const { EnterpriseBulkSMSProcessor } = require('./enterprise-bulk-sms-processor');

// Get worker ID from worker data
const workerId = workerData?.workerId || 0;

// Initialize worker-specific logging
const logsDir = process.env.LOGS_DIR || path.join(process.cwd(), 'logs');
const errorLogPath = path.join(logsDir, `worker-${workerId}-error.log`);
const successLogPath = path.join(logsDir, `worker-${workerId}-success.log`);

// Ensure logs directory exists
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Create log streams
const errorLogStream = fs.createWriteStream(errorLogPath, { flags: 'a' });
const successLogStream = fs.createWriteStream(successLogPath, { flags: 'a' });

// Worker-specific logging function
function workerLog(level, message, data = {}) {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    level,
    workerId,
    message,
    ...data,
  };

  const logString = JSON.stringify(logEntry) + '\n';

  // Write to appropriate log file
  if (level === 'ERROR') {
    errorLogStream.write(logString);
  } else {
    successLogStream.write(logString);
  }

  // Also send to parent for console output in development
  parentPort.postMessage({
    type: 'log',
    level: level.toLowerCase(),
    text: `[Worker-${workerId}] ${message}`,
    data,
    workerType: 'bulk',
  });
}

function convertString(msg) {
  const utf16beEncoded = iconv.encode(msg, 'UTF-16BE');
  return utf16beEncoded.toString('hex').toUpperCase();
}

// Add this function to calculate current throughput
function getCurrentThroughputPerSecond() {
  if (messageTimestamps.length === 0) return 0;

  const now = Date.now();
  const oneSecondAgo = now - 1000;
  const tenSecondsAgo = now - 10000;
  const oneMinuteAgo = now - 60000;

  const messagesLastSecond = messageTimestamps.filter((t) => t > oneSecondAgo).length;
  const messagesLast10Seconds = messageTimestamps.filter((t) => t > tenSecondsAgo).length;
  const messagesLastMinute = messageTimestamps.filter((t) => t > oneMinuteAgo).length;

  return {
    lastSecond: messagesLastSecond,
    last10Seconds: (messagesLast10Seconds / 10).toFixed(2),
    lastMinute: (messagesLastMinute / 60).toFixed(2),
  };
}

// Send heartbeats to parent
setInterval(() => {
  const throughput = getCurrentThroughputPerSecond();
  parentPort.postMessage({
    type: 'heartbeat',
    workerId,
    timestamp: new Date().toISOString(),
    // throughput: throughput.lastSecond,
    throughput: throughputVal,
    totalProcessed: messagesProcessed,
    successfulMessages,
    failedMessages,
    workerType: 'bulk',
  });
}, 5000);

// Log worker startup
workerLog('INFO', 'Worker started successfully', { workerId });

// Performance tracking
let messagesProcessed = 0;
let throughputVal = 0;
let totalProcessingTime = 0;
let startTime = Date.now();

// Add these new variables for throughput tracking
let messageTimestamps = [];
let successfulMessages = 0;
let failedMessages = 0;

// Instantiate the enterprise bulk SMS processor with workerLog and config injection
const enterpriseBulkProcessor = new EnterpriseBulkSMSProcessor({
  workerLog,
  // config: typeof config !== 'undefined' ? config : {},
});

// Listen for messages from the main thread
parentPort.on('message', async (data) => {
  const { id, data: messageData } = data;

  try {
    // Use the enterprise processor for all bulk SMS jobs
    const bulkResults = await enterpriseBulkProcessor.processBulkSMS(messageData);
    workerLog(
      'INFO',
      `[Bulk] [${workerId}] Worker successfully completed work of: ${JSON.stringify(bulkResults)}`,
    );
  
    messagesProcessed = bulkResults.totalContacts;
    successfulMessages = bulkResults.successfulContacts;
    failedMessages = bulkResults.failedContacts;
    throughputVal = bulkResults.throughputPerSecond;
  
    parentPort.postMessage({
      type: 'bulk_result',
      success: true,
      data: bulkResults,
      requestId: messageData.requestId || 'unknown',
      id,
      messageId: id, // <-- Always include messageId
      timestamp: new Date().toISOString(),
      workerType: 'bulk',
    });
    return;
  } catch (err) {
    workerLog('ERROR', `[Worker-${workerId}] SMS processing error`, { error: err.stack || err.message || err });
    parentPort.postMessage({
      type: 'bulk_result',
      success: false,
      data: err.stack || err.message || err,
      requestId: messageData.requestId || 'unknown',
      id,
      messageId: id, // <-- Always include messageId
      timestamp: new Date().toISOString(),
      workerType: 'bulk',
    });
  }
});

// Graceful shutdown
process.on('SIGTERM', () => {
  workerLog('INFO', 'Worker shutting down gracefully');
  errorLogStream.end();
  successLogStream.end();
  process.exit(0);
});

process.on('SIGINT', () => {
  workerLog('INFO', 'Worker shutting down gracefully');
  errorLogStream.end();
  successLogStream.end();
  process.exit(0);
});

process.on('uncaughtException', (err) => {
  workerLog('ERROR', 'Uncaught Exception', { error: err.stack || err.message || err });
  // Do not exit! Just log.
});
process.on('unhandledRejection', (reason) => {
  workerLog('ERROR', 'Unhandled Rejection', { error: reason && reason.stack ? reason.stack : reason });
  // Do not exit! Just log.
});

// Export performance metrics using enterprise processor
module.exports = {
  getMetrics: () => enterpriseBulkProcessor.metrics.metrics,
};
