import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { RetryStrategy } from './retry.strategy';
import { Logger } from '@nestjs/common';

/**
 * Unit tests for the RetryStrategy
 */
describe('RetryStrategy', () => {
  let service: RetryStrategy;
  let configService: ConfigService;

  // Mock the logger to avoid console output during tests
  const mockLogger = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    verbose: jest.fn(),
  };

  beforeEach(async () => {
    // Create a testing module with our dependencies
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RetryStrategy,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key: string) => {
              if (key === 'LOG_LEVEL') return 'debug';
              return undefined;
            }),
          },
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    // Get the service instance
    service = module.get<RetryStrategy>(RetryStrategy);
    configService = module.get<ConfigService>(ConfigService);

    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return the result when the function succeeds on first attempt', async () => {
    // Arrange
    const expectedResult = { success: true };
    const fn = jest.fn().mockResolvedValue(expectedResult);

    // Act
    const result = await service.execute(fn);

    // Assert
    expect(result).toEqual(expectedResult);
    expect(fn).toHaveBeenCalledTimes(1);
  });

  it('should retry and succeed on second attempt', async () => {
    // Arrange
    const expectedResult = { success: true };
    const error = new Error('Test error');
    const fn = jest.fn()
      .mockRejectedValueOnce(error)
      .mockResolvedValueOnce(expectedResult);

    // Act
    const result = await service.execute(fn, { delay: 10 }); // Use small delay for faster tests

    // Assert
    expect(result).toEqual(expectedResult);
    expect(fn).toHaveBeenCalledTimes(2);
  });

  it('should throw the last error after all retries fail', async () => {
    // Arrange
    const error = new Error('Test error');
    const fn = jest.fn().mockRejectedValue(error);

    // Act & Assert
    await expect(service.execute(fn, { maxAttempts: 3, delay: 10 }))
      .rejects.toThrow(error);
    expect(fn).toHaveBeenCalledTimes(3);
  });

  it('should use custom retry options', async () => {
    // Arrange
    const expectedResult = { success: true };
    const error = new Error('Test error');
    const fn = jest.fn()
      .mockRejectedValueOnce(error)
      .mockRejectedValueOnce(error)
      .mockRejectedValueOnce(error)
      .mockRejectedValueOnce(error)
      .mockResolvedValueOnce(expectedResult);

    const options = {
      maxAttempts: 5,
      delay: 10,
      factor: 1.5,
    };

    // Act
    const result = await service.execute(fn, options);

    // Assert
    expect(result).toEqual(expectedResult);
    expect(fn).toHaveBeenCalledTimes(5);
  });
});
