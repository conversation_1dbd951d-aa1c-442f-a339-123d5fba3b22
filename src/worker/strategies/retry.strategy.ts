import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Options for configuring the retry behavior
 */
interface RetryOptions {
  /** Maximum number of retry attempts */
  maxAttempts: number;

  /** Base delay in milliseconds between retries */
  delay: number;

  /** Exponential backoff factor (delay * factor^attempt) */
  factor: number;
}

/**
 * Strategy for retrying operations with exponential backoff
 *
 * This service provides a mechanism to retry failed operations with
 * configurable exponential backoff to handle transient failures gracefully.
 */
@Injectable()
export class RetryStrategy {
  private readonly logger = new Logger(RetryStrategy.name);

  /**
   * Default retry options if not specified
   */
  private readonly defaultOptions: RetryOptions = {
    maxAttempts: 3,  // Retry up to 3 times
    delay: 1000,     // Start with 1 second delay
    factor: 2        // Double the delay on each retry
  };

  /**
   * Creates a new RetryStrategy instance
   *
   * @param configService - The NestJS ConfigService for accessing configuration
   */
  constructor(
    private readonly configService: ConfigService,
  ) {}

  /**
   * Executes a function with retry logic
   *
   * This method will attempt to execute the provided function and retry
   * if it fails, using exponential backoff between attempts.
   *
   * @param fn - The async function to execute
   * @param options - Optional retry configuration
   * @returns The result of the function execution
   * @throws The last error encountered if all retries fail
   *
   * @example
   * // Retry an HTTP request up to 5 times
   * const result = await retryStrategy.execute(
   *   () => httpClient.get('https://api.example.com'),
   *   { maxAttempts: 5 }
   * );
   */
  async execute<T>(fn: () => Promise<T>, options?: Partial<RetryOptions>): Promise<T> {
    // Merge provided options with defaults
    const retryOptions = { ...this.defaultOptions, ...options };
    let lastError: Error = new Error('Unknown error occurred');

    for (let attempt = 1; attempt <= retryOptions.maxAttempts; attempt++) {
      try {
        // Attempt to execute the function
        return await fn();
      } catch (error) {
        // Store the error for potential re-throw or logging
        lastError = error;
        this.logger.warn(`Attempt ${attempt} failed: ${error.message}`);

        // If we have more attempts, wait before retrying
        if (attempt < retryOptions.maxAttempts) {
          // Calculate delay with exponential backoff: delay * factor^(attempt-1)
          const delay = retryOptions.delay * Math.pow(retryOptions.factor, attempt - 1);
          this.logger.debug(`Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // If we've exhausted all retries, throw the last error
    throw lastError;
  }
}
