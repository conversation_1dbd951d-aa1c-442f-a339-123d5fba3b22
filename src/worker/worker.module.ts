import { Module, forwardRef } from '@nestjs/common';
import { SharedModule } from '../shared/shared.module';
import { QueueModule } from '../queue/queue.module';
import { SmsModule } from '../sms/sms.module';

// Import existing services to maintain compatibility during transition
import { WorkerManagerService } from './services/worker-manager.service';
import { WorkerHealthIndicator } from './health/worker-health.indicator';
import { WorkerStatusController } from './controllers/worker-status.controller';

// Import new architecture components
import { SmsHandler } from './handlers/sms.handler';
import { RetryStrategy } from './strategies/retry.strategy';

@Module({
  imports: [
    forwardRef(() => SharedModule),
    QueueModule,
    SmsModule,
  ],
  controllers: [
    WorkerStatusController,
  ],
  providers: [
    // Legacy providers
    // WorkerManagerService is now provided by CoreModule
    WorkerHealthIndicator,

    // New architecture providers
    SmsHandler,
    RetryStrategy,
  ],
  exports: [
    // Legacy exports
    // WorkerManagerService is now provided by CoreModule
    WorkerHealthIndicator,

    // New architecture exports
    SmsHandler,
    RetryStrategy,
  ],
})
export class WorkerModule {}
