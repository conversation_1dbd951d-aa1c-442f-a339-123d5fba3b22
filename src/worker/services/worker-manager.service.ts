import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Worker } from 'worker_threads';
import * as path from 'path';
import * as os from 'os';
import { EnhancedWorkerLoggerService } from '../../shared/logger/enhanced-worker-logger.service';
import { QueueManagerService } from '../../queue/services/queue-manager.service';

@Injectable()
export class WorkerManagerService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(WorkerManagerService.name);
  private workers: Worker[] = [];
  private workerStatuses: Map<
    number,
    { healthy: boolean; lastHeartbeat: Date; throughput: number }
  > = new Map();
  private readonly numWorkers: number;
  private isInitialized = false;
  private workerIndex = 0;
  private workerLoggers: Map<number, EnhancedWorkerLoggerService> = new Map();
  private performanceConfig: any;
  private singleWorkers: Worker[] = [];
  private bulkWorkers: Worker[] = [];
  private singleWorkerIndex = 0; // Separate round-robin index for single workers
  private bulkWorkerIndex = 0;   // Separate round-robin index for bulk workers
  private workerCrashCounts: Map<number, number> = new Map(); // Track consecutive crashes
  private readonly MAX_CRASH_BACKOFF = 60000; // 1 minute max backoff
  private readonly INITIAL_CRASH_BACKOFF = 2000; // 2 seconds initial backoff
  private shutdownRequested = false; // For graceful shutdown
  private inFlightJobs: Map<number, number> = new Map(); // Track in-flight jobs per worker

  constructor(
    private configService: ConfigService,
    // private readonly queueManager: QueueManagerService,
  ) {
    // Import PerformanceConfig dynamically to avoid circular dependencies
    const { PerformanceConfig } = require('../../shared/config/performance.config');
    this.performanceConfig = new PerformanceConfig(configService);

    // Get worker count from environment variables with fallback logic
    this.numWorkers = this.determineWorkerCount();

    this.logger.log(`🚀 Worker Manager Configuration`);
    this.logger.log(`📊 Configured to use ${this.numWorkers} worker threads`);
    this.logger.log(
      `🎯 Target throughput: ${this.performanceConfig.getTargetThroughput()} SMS/second`,
    );
    this.logger.log(
      `⚡ Per-worker throughput: ${this.performanceConfig.getMessagesPerWorkerPerSecond()} SMS/second`,
    );
    this.logger.log(`💻 CPU cores available: ${os.cpus().length}`);
    this.logger.log(
      `🧠 Memory limit per worker: ${this.performanceConfig.getWorkerMemoryLimit()}MB`,
    );
  }

  /**
   * Determine optimal worker count based on environment configuration
   */
  private determineWorkerCount(): number {
    const cpuCores = os.cpus().length;
    const defaultWorkerCount = '2';

    // Priority order for worker count determination:
    // 1. Explicit SMS_WORKER_COUNT environment variable
    // 2. Calculated based on target throughput and per-worker capacity
    // 3. CPU-based calculation with safety limits

    // Debug: Check all possible sources
    // const configWorkerCount = this.configService.get<number>('SMS_WORKER_COUNT', 2);
    // const configWorkerCountString = this.configService.get<string>('SMS_WORKER_COUNT', '2');

    const singleWorkerCount = this.configService.get('SINGLE_WORKER_COUNT', defaultWorkerCount);
    const bulkWorkerCount = this.configService.get('BULK_WORKER_COUNT', defaultWorkerCount);
    const envWorkerCount =
      parseInt(process.env.SINGLE_SMS_WORKER_COUNT ?? defaultWorkerCount) +
      parseInt(process.env.BULK_SMS_WORKER_COUNT ?? defaultWorkerCount);

    this.logger.log(
      `🔍 DEBUG: Environment SINGLE_WORKER_COUNT & BULK_WORKER_COUNT: ${envWorkerCount}`,
    );
    this.logger.log(`🔍 DEBUG: ConfigService SINGLE_WORKER_COUNT (number): ${singleWorkerCount}`);

    // Check for explicit worker count
    if ((singleWorkerCount && bulkWorkerCount) && (singleWorkerCount + bulkWorkerCount > 0)) {
      this.logger.log(
        `📋 Using explicit worker count from SINGLE_WORKER_COUNT & BULK_WORKER_COUNT: ${singleWorkerCount + bulkWorkerCount}`,
      );
      return Math.min(singleWorkerCount + bulkWorkerCount, 4); // Cap at 15 workers for safety
    }

    this.logger.log(`🔍 DEBUG: ConfigService BULK_WORKER_COUNT (number): ${bulkWorkerCount}`);

    // Check for explicit worker count
    if (envWorkerCount && envWorkerCount > 0) {
      this.logger.log(
        `📋 Using explicit worker count from SINGLE_WORKER_COUNT & BULK_WORKER_COUNT: ${envWorkerCount}`,
      );
      return Math.min(envWorkerCount, 15); // Cap at 15 workers for safety
    }

    // Fallback: Try environment variable directly
    if (envWorkerCount && envWorkerCount > 0) {
      const envCount = envWorkerCount;
      this.logger.log(`📋 Using environment variable SMS_WORKER_COUNT: ${envCount}`);
      return Math.min(envCount, 10);
    }

    // Calculate based on target throughput
    const targetThroughput = this.performanceConfig.getTargetThroughput();
    const messagesPerWorkerPerSecond = this.performanceConfig.getMessagesPerWorkerPerSecond();
    const throughputBasedWorkers = Math.ceil(targetThroughput / messagesPerWorkerPerSecond);

    this.logger.log(
      `📊 Throughput-based calculation: ${targetThroughput} SMS/s ÷ ${messagesPerWorkerPerSecond} SMS/s/worker = ${throughputBasedWorkers} workers`,
    );

    // CPU-based calculation with scaling factor
    const cpuBasedWorkers = Math.max(cpuCores * 2, throughputBasedWorkers);

    this.logger.log(`💻 CPU-based calculation: ${cpuCores} cores × 2 = ${cpuCores * 2} workers`);

    // Use the higher of the two calculations, but with safety limits
    const optimalWorkers = Math.max(throughputBasedWorkers, cpuBasedWorkers);
    const finalWorkerCount = Math.min(optimalWorkers, 50); // Cap at 50 workers for memory safety

    this.logger.log(`🎯 Final worker count: ${finalWorkerCount} (capped at 50 for memory safety)`);

    return finalWorkerCount;
  }

  async onModuleInit() {
    await this.initializeWorkers();
  }

  async onModuleDestroy() {
    await this.terminateWorkers();
  }

  private async initializeWorkers() {
    this.logger.log(
      `🚀 Initializing ${this.numWorkers} optimized worker threads for SMS processing`,
    );
    const singleWorkerCount = this.configService.get('SINGLE_WORKER_COUNT', 2);
    const bulkWorkerCount = this.configService.get('BULK_WORKER_COUNT', 2);

    // Create single workers
    for (let i = 0; i < singleWorkerCount; i++) {
      const worker = await this.createWorker(i, 'single');
      this.singleWorkers.push(worker);
      this.inFlightJobs.set(i, 0); // Initialize in-flight jobs
    }

    // Create bulk workers
    for (let i = 0; i < bulkWorkerCount; i++) {
      const worker = await this.createWorker(i, 'bulk');
      this.bulkWorkers.push(worker);
      this.inFlightJobs.set(i, 0); // Initialize in-flight jobs
    }

    // Create workers in parallel for faster startup
    // const workerPromises = Array.from({ length: this.numWorkers }, (_, i) => this.createWorker(i));
    // await Promise.all(workerPromises);

    this.isInitialized = true;
    this.logger.log(`✅ Worker initialization complete with ${this.workers.length} workers`);

    // Log worker statistics
    this.logWorkerStats();
  }

  private async createWorker(index: number, workerType: 'single' | 'bulk' = 'single') {
    // Create worker-specific logger
    const workerLogger = new EnhancedWorkerLoggerService(this.configService, index);
    this.workerLoggers.set(index, workerLogger);

    // Choose worker script based on type (optimized for both single and bulk)
    // ---
    // IMPORTANT: If your message processing is slow or variable, ensure RabbitMQ consumer prefetch is set to 1
    // This prevents message pile-up and delivery acknowledgement timeouts (see: PRECONDITION_FAILED - delivery acknowledgement timed out)
    // ---
    const workerPath =
      process.env.NODE_ENV === 'production'
        ? path.join(
            __dirname,
            '..',
            'threads',
            workerType === 'bulk' ? 'sms-bulk-worker.js' : 'sms-single-worker.js',
          )
        : path.join(
            process.cwd(),
            'src',
            'worker',
            'threads',
            workerType === 'bulk' ? 'sms-bulk-worker.js' : 'sms-single-worker.js',
          );

    workerLogger.log(`Creating ${workerType} worker ${index} with path: ${workerPath}`);

    const worker = new Worker(workerPath, {
      workerData: {
        workerId: index,
        workerType,
        performanceConfig: {
          targetThroughput: this.performanceConfig.getTargetThroughput(),
          messagesPerWorkerPerSecond: this.performanceConfig.getMessagesPerWorkerPerSecond(),
          httpTimeout: this.performanceConfig.getHttpTimeout(),
          maxConnections: this.performanceConfig.getHttpMaxConnections(),
          retryConfig: this.performanceConfig.getRetryConfig(),
          batchConfig: this.performanceConfig.getBatchConfig(),
          connectionPoolConfig: this.performanceConfig.getConnectionPoolConfig(),
        },
      },
    });

    // Set maximum number of listeners to prevent memory leak warnings
    worker.setMaxListeners(50);

    // Set up message handling with enhanced logging and performance tracking
    const setupMessageHandler = () => {
      worker.on('message', (message) => {
        if (message.type === 'heartbeat') {
          this.workerStatuses.set(index, {
            healthy: true,
            lastHeartbeat: new Date(),
            throughput: message.throughput || 0,
          });
          workerLogger.debug(
            `Heartbeat received from ${message.workerType} worker ${index} (throughput: ${message.throughput || 0} SMS/s) (total_processed: ${message.totalProcessed || 0} SMS, successful: ${message.successfulMessages || 0} SMS, failed: ${message.failedMessages || 0} SMS)`,
          );
        } else if (message.type === 'log') {
          const { level, text, data } = message;
          const workerLogger = this.workerLoggers.get(index);

          if (workerLogger) {
            switch (level) {
              case 'info':
                workerLogger.log(text, data);
                break;
              case 'error':
                workerLogger.error(text, data);
                break;
              case 'warn':
                workerLogger.warn(text, data);
                break;
              case 'success':
                workerLogger.success(text, data);
                break;
              case 'performance':
                workerLogger.performance(data);
                break;
              default:
                workerLogger.debug(text, data);
            }
          }
        } else if (message.type === 'performance') {
          // Track performance metrics
          workerLogger.performance({
            workerId: index,
            ...message.data,
          });
        } else if (message.type === 'bulk_result' || message.type === 'result') {
          // Decrement in-flight jobs
          this.inFlightJobs.set(index, Math.max(0, (this.inFlightJobs.get(index) || 1) - 1));
        }
      });
    };

    // Set up error handling with optimized retry logic
    // ---
    // If a worker crashes or exits, it is recreated and re-registered in all pools.
    // The onWorkerRecreated method ensures the new worker is available for round-robin dispatch.
    // ---
    const setupErrorHandler = () => {
      worker.on('error', (err) => {
        const workerLogger = this.workerLoggers.get(index);
        if (workerLogger) {
          workerLogger.error(`Worker ${index} encountered an error: ${err.message}`, err.stack);
        }

        this.logger.error(`Worker ${index} error: ${err.message}`);
        this.workerStatuses.set(index, {
          healthy: false,
          lastHeartbeat: new Date(),
          throughput: 0,
        });

        // Exponential backoff for repeated crashes
        const prevCrashes = this.workerCrashCounts.get(index) || 0;
        const newCrashCount = prevCrashes + 1;
        this.workerCrashCounts.set(index, newCrashCount);
        const backoff = Math.min(this.INITIAL_CRASH_BACKOFF * Math.pow(2, newCrashCount - 1), this.MAX_CRASH_BACKOFF);
        this.logger.warn(`Worker ${index} has crashed ${newCrashCount} times. Backing off for ${backoff}ms before recreation.`);

        setTimeout(() => {
          if (this.shutdownRequested) return; // Don't recreate if shutting down
          this.logger.log(`🔄 Attempting to recreate worker ${index} after error`);
          this.createWorker(index, workerType).then((newWorker) => {
            if (workerType === 'bulk') this.bulkWorkers[index] = newWorker;
            else this.singleWorkers[index] = newWorker;
            this.workers[index] = newWorker;
            // Notify dispatcher or re-initialize assignment logic
            this.onWorkerRecreated(index, newWorker, workerType);
          });
        }, backoff);
      });
    };

    // Set up exit handling with optimized recovery
    // ---
    // If a worker exits, it is recreated and re-registered in all pools.
    // The onWorkerRecreated method ensures the new worker is available for round-robin dispatch.
    // ---
    const setupExitHandler = () => {
      worker.on('exit', (code) => {
        const workerLogger = this.workerLoggers.get(index);
        if (workerLogger) {
          workerLogger.warn(`Worker ${index} exited with code ${code}`);
        }

        this.logger.warn(`Worker ${index} exited with code ${code}. Replacing worker...`);
        this.workerStatuses.set(index, {
          healthy: false,
          lastHeartbeat: new Date(),
          throughput: 0,
        });

        // Exponential backoff for repeated exits
        const prevCrashes = this.workerCrashCounts.get(index) || 0;
        const newCrashCount = prevCrashes + 1;
        this.workerCrashCounts.set(index, newCrashCount);
        const backoff = Math.min(this.INITIAL_CRASH_BACKOFF * Math.pow(2, newCrashCount - 1), this.MAX_CRASH_BACKOFF);
        this.logger.warn(`Worker ${index} has exited ${newCrashCount} times. Backing off for ${backoff}ms before recreation.`);

        setTimeout(() => {
          if (this.shutdownRequested) return; // Don't recreate if shutting down
          this.logger.log(`🔄 Attempting to recreate worker ${index} after exit`);
          this.createWorker(index, workerType).then((newWorker) => {
            if (workerType === 'bulk') this.bulkWorkers[index] = newWorker;
            else this.singleWorkers[index] = newWorker;
            this.workers[index] = newWorker;
            // Notify dispatcher or re-initialize assignment logic
            this.onWorkerRecreated(index, newWorker, workerType);
          });
        }, backoff);
      });
    };

    // Initialize all handlers
    setupMessageHandler();
    setupErrorHandler();
    setupExitHandler();

    // Initialize worker status
    this.workerStatuses.set(index, {
      healthy: true,
      lastHeartbeat: new Date(),
      throughput: 0,
    });

    // Store the worker in the correct pool and in the main array
    this.workers[index] = worker;
    if (workerType === 'bulk') this.bulkWorkers[index] = worker;
    else this.singleWorkers[index] = worker;

    workerLogger.log(
      `✅ Worker ${index} (${workerType}) initialized successfully with configuration`,
    );
    this.logger.log(`✅ Worker ${index} (${workerType}) initialized successfully`);

    return worker;
  }

  private async terminateWorkers() {
    this.logger.log('🛑 Terminating all workers...');

    this.shutdownRequested = true;
    // Graceful draining: wait for in-flight jobs to finish (up to 30 seconds)
    const maxWaitMs = 30000;
    const pollInterval = 500;
    let waited = 0;
    while (waited < maxWaitMs) {
      const totalInFlight = Array.from(this.inFlightJobs.values()).reduce((a, b) => a + b, 0);
      if (totalInFlight === 0) break;
      this.logger.log(`Waiting for ${totalInFlight} in-flight jobs to finish before shutdown...`);
      await new Promise((res) => setTimeout(res, pollInterval));
      waited += pollInterval;
    }
    if (waited >= maxWaitMs) {
      this.logger.warn('Timeout reached while waiting for in-flight jobs. Proceeding with shutdown.');
    }

    const terminationPromises = this.workers.map((worker, index) => {
      if (!worker) return Promise.resolve();

      return new Promise<void>((resolve) => {
        worker.once('exit', () => {
          this.logger.log(`✅ Worker ${index} terminated`);
          resolve();
        });

        worker.terminate();
      });
    });

    await Promise.all(terminationPromises);
    this.workers = [];
    this.workerStatuses.clear();
    this.workerLoggers.clear();
    this.workerCrashCounts.clear();
    this.inFlightJobs.clear();
    this.logger.log('✅ All workers terminated successfully');
  }

  private logWorkerStats() {
    const totalWorkers = this.workers.length;
    const healthyWorkers = Array.from(this.workerStatuses.values()).filter(
      (status) => status.healthy,
    ).length;
    const totalThroughput = Array.from(this.workerStatuses.values()).reduce(
      (sum, status) => sum + status.throughput,
      0,
    );

    this.logger.log(`📊 Worker Statistics:`);
    this.logger.log(`   👥 Total Workers: ${totalWorkers}`);
    this.logger.log(`   ✅ Healthy Workers: ${healthyWorkers}`);
    this.logger.log(`   ⚡ Total Throughput: ${totalThroughput.toFixed(2)} SMS/second`);
    this.logger.log(
      `   🎯 Target Throughput: ${this.performanceConfig.getTargetThroughput()} SMS/second`,
    );
    this.logger.log(
      `   📈 Performance Ratio: ${((totalThroughput / this.performanceConfig.getTargetThroughput()) * 100).toFixed(2)}%`,
    );
  }

  private getAvailableWorker(messageType: 'single' | 'bulk'): Worker | null {
    const workerPool = messageType === 'bulk' ? this.bulkWorkers : this.singleWorkers;
    return workerPool.find((worker) => this.isWorkerAvailable(worker)) || null;
  }

  // ---
  // Dispatcher logic: getWorker uses pure round-robin for both single and bulk pools.
  // Now uses separate indexes for each pool.
  // ---
  getWorker(workerCategory: 'single' | 'bulk'): Worker | null {
    if (!this.isInitialized || this.workers.length === 0) {
      this.logger.warn('Workers not initialized or no workers available');
      return null;
    }

    let worker: Worker | null = null;
    if (workerCategory === 'single') {
      if (this.singleWorkers.length === 0) return null;
      worker = this.singleWorkers[this.singleWorkerIndex % this.singleWorkers.length];
      this.singleWorkerIndex = (this.singleWorkerIndex + 1) % this.singleWorkers.length;
      this.logger.warn(
        `Using single worker index ${this.singleWorkerIndex % this.singleWorkers.length} from list of ${this.singleWorkers.length} available`,
      );
    } else if (workerCategory === 'bulk') {
      if (this.bulkWorkers.length === 0) return null;
      worker = this.bulkWorkers[this.bulkWorkerIndex % this.bulkWorkers.length];
      this.bulkWorkerIndex = (this.bulkWorkerIndex + 1) % this.bulkWorkers.length;
      this.logger.warn(
        `Using bulk worker index ${this.bulkWorkerIndex % this.bulkWorkers.length} from list of ${this.bulkWorkers.length} available`,
      );
    }
    return worker;
  }

  // ---
  // Always call channel.ack(msg) or channel.nack(msg) in your RabbitMQ consumer callback.
  // Never leave a message un-acked, or you risk delivery acknowledgement timeouts.
  // ---

  // ---
  // In worker thread code, always set timeouts for external API calls (e.g., HTTP requests to SMS providers).
  // This prevents a stuck API call from blocking message acknowledgement and causing RabbitMQ timeouts.
  // ---

  getWorkerForCategory(workerCategory: 'single' | 'bulk', assignmentValue: number): Worker | null {
    // Simplified category handling for better performance
    // return this.getWorker(workerCategory, assignmentValue);
    return this.getWorker(workerCategory);
  }

  // Alerting stub for unhealthy workers (production/enterprise readiness)
  private alertUnhealthyWorker(index: number, status: any) {
    // Integrate with your alerting/monitoring system (e.g., email, Slack, PagerDuty)
    this.logger.error(`ALERT: Worker ${index} is unhealthy! Last heartbeat: ${status.lastHeartbeat}`);
    // TODO: Send alert to external system
  }

  checkHealth(): Record<string, any> {
    const now = new Date();
    const healthData = {
      totalWorkers: this.workers.length,
      healthyWorkers: 0,
      unhealthyWorkers: 0,
      totalThroughput: 0,
      averageThroughput: 0,
      targetThroughput: this.performanceConfig.getTargetThroughput(),
      performanceRatio: 0,
      workerDetails: [] as any[],
      lastUpdated: now.toISOString(),
    };

    this.workerStatuses.forEach((status, index) => {
      const isHealthy =
        status.healthy &&
        now.getTime() - status.lastHeartbeat.getTime() <
          this.performanceConfig.getHeartbeatInterval() * 2;

      if (isHealthy) {
        healthData.healthyWorkers++;
      } else {
        healthData.unhealthyWorkers++;
        this.alertUnhealthyWorker(index, status); // Alert on unhealthy
      }

      healthData.totalThroughput += status.throughput;

      healthData.workerDetails.push({
        workerId: index,
        healthy: isHealthy,
        lastHeartbeat: status.lastHeartbeat.toISOString(),
        throughput: status.throughput,
        uptime: now.getTime() - status.lastHeartbeat.getTime(),
      });
    });

    healthData.averageThroughput =
      healthData.healthyWorkers > 0 ? healthData.totalThroughput / healthData.healthyWorkers : 0;

    healthData.performanceRatio =
      healthData.targetThroughput > 0
        ? (healthData.totalThroughput / healthData.targetThroughput) * 100
        : 0;

    return healthData;
  }

  getWorkerCount(): number {
    return this.numWorkers;
  }

  getActiveWorkerCount(): number {
    return Array.from(this.workerStatuses.values()).filter((status) => status.healthy).length;
  }

  getTotalThroughput(): number {
    return Array.from(this.workerStatuses.values()).reduce(
      (sum, status) => sum + status.throughput,
      0,
    );
  }

  // Add a simple isWorkerAvailable method for pool selection
  private isWorkerAvailable(worker: Worker): boolean {
    // For now, always return true. You can add more logic if needed.
    return !!worker;
  }

  // Add a callback to handle worker recreation and pool/round-robin updates
  private onWorkerRecreated(index: number, newWorker: Worker, workerType: 'single' | 'bulk') {
    // Ensure the new worker is in all pools
    this.workers[index] = newWorker;
    if (workerType === 'bulk') {
      this.bulkWorkers[index] = newWorker;
      if (this.bulkWorkerIndex >= this.bulkWorkers.length) {
        this.bulkWorkerIndex = 0;
      }
    } else {
      this.singleWorkers[index] = newWorker;
      if (this.singleWorkerIndex >= this.singleWorkers.length) {
        this.singleWorkerIndex = 0;
      }
    }
    this.workerCrashCounts.set(index, 0); // Reset crash count on successful recreation
    this.logger.log(`🔄 Worker ${index} (${workerType}) recreated and re-registered in pools.`);
  }
}
