import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SmsUtils } from '../../shared/utils';

@Injectable()
export class SmsHandler {
  private readonly logger = new Logger(SmsHandler.name);

  constructor(
    private readonly configService: ConfigService,
  ) {}

  /**
   * Handle a single SMS message
   *
   * @param data - The single SMS message data
   */
  async handleSingleSms(data: any): Promise<void> {
    const requestId = data.requestId || 'unknown';
    const to = data.to || data.phone || 'unknown';

    // Extract message content using utility
    const messageContent = SmsUtils.extractMessageContent(data);

    // Log message details using utility
    SmsUtils.logMessageDetails(this.logger, messageContent, requestId, 'log');

    // Validate phone number
    if (!SmsUtils.isValidPhoneNumber(to)) {
      this.logger.warn(`[${requestId}] Invalid phone number: ${to}`);
    }

    // Generate a unique message ID
    const messageId = SmsUtils.generateMessageId();

    this.logger.log(`[${requestId}] Processing single SMS to ${to} with ID: ${messageId}`);
  }

  /**
   * Handle a bulk SMS message
   *
   * @param data - The bulk SMS message data
   */
  async handleBulkSms(data: any): Promise<void> {
    const requestId = data.requestId || 'unknown';
    const bulkType = data.bulk_type || 'TRADITIONAL';

    // Extract message content using utility
    const messageContent = SmsUtils.extractMessageContent(data);

    // Log message details using utility
    SmsUtils.logMessageDetails(this.logger, messageContent, requestId, 'log');

    // Generate a unique message ID
    const messageId = SmsUtils.generateMessageId();

    this.logger.log(`[${requestId}] Processing bulk SMS (${bulkType}) with ID: ${messageId}`);
  }
}
