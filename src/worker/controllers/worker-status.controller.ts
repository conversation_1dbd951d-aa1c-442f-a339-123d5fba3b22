import { Controller, Get, HttpStatus, Res } from '@nestjs/common';
import { Response } from 'express';
import { WorkerManagerService } from '../services/worker-manager.service';
import { MetricsService } from '../../shared/services/metrics.service';
import { FailedSmsPersistenceService } from '../../shared/services/failed-sms-persistence.service';
import { FailedSmsRetryService } from '../../shared/services/failed-sms-retry.service';
import { QueueManagerService } from '../../queue/services/queue-manager.service';
import { ConfigService } from '@nestjs/config';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

export interface WorkerMetrics {
  workerId: number;
  status: 'up' | 'down' | 'error';
  lastHeartbeat: string;
  timeSinceLastHeartbeat: string;
  messagesProcessed: number;
  messagesSuccessful: number;
  messagesFailed: number;
  successRate: number;
  averageProcessingTime: number;
  throughputPerSecond: number;
  pendingMessages: number;
  retryAttempts: number;
  uptime: string;
}

export interface WorkerStatusResponse {
  status: 'up' | 'down' | 'error';
  totalWorkers: number;
  activeWorkers: number;
  overallSuccessRate: number;
  overallThroughputPerSecond: number;
  overallAverageProcessingTime: number;
  totalMessagesProcessed: number;
  totalMessagesSuccessful: number;
  totalMessagesFailed: number;
  totalPendingMessages: number;
  totalRetryAttempts: number;
  workers: WorkerMetrics[];
  queueStats: {
    singleQueueDepth: number;
    bulkQueueDepth: number;
    dlrQueueDepth: number;
    failedQueueDepth: number;
    pendingQueueDepth: number;
    retryQueueDepth: number;
    dlxQueueDepth: number;
  };
  persistenceStats: {
    failedCount: number;
    pendingCount: number;
    totalSize: number;
  };
  configuration: {
    workerCount: number;
    throughputPerSecond: number;
    maxRetries: number;
    maxConcurrentMessages: number;
  };
}

@ApiTags('worker')
@Controller('worker-status')
export class WorkerStatusController {
  constructor(
    private readonly workerManager: WorkerManagerService,
    private readonly metricsService: MetricsService,
    private readonly failedSmsPersistence: FailedSmsPersistenceService,
    private readonly failedSmsRetry: FailedSmsRetryService,
    private readonly queueManager: QueueManagerService,
    private readonly configService: ConfigService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get comprehensive worker status' })
  @ApiResponse({
    status: 200,
    description: 'Returns detailed worker status information with metrics',
  })
  @ApiResponse({ status: 503, description: 'Service unavailable - workers are not healthy' })
  async getWorkerStatus(@Res() response: Response): Promise<void> {
    try {
      const healthResult = this.workerManager.checkHealth();
      const metrics = this.metricsService.getMetrics();
      const persistenceStats = await this.failedSmsPersistence.getPersistenceStats();
      
      // Get queue depths
      const queueStats = await this.getQueueStats();
      
      // Get worker-specific metrics
      const workers = await this.getWorkerMetricsData();
      
      // Calculate overall metrics
      const totalWorkers = workers.length;
      const activeWorkers = workers.filter(w => w.status === 'up').length;
      const overallSuccessRate = metrics.totalMessages > 0 
        ? (metrics.successfulMessages / metrics.totalMessages) * 100 
        : 0;
      const overallThroughputPerSecond = this.calculateOverallThroughput(workers);
      const overallAverageProcessingTime = metrics.averageProcessingTime;
      
      const result: WorkerStatusResponse = {
        status: healthResult.status,
        totalWorkers,
        activeWorkers,
        overallSuccessRate: Math.round(overallSuccessRate * 100) / 100,
        overallThroughputPerSecond: Math.round(overallThroughputPerSecond * 100) / 100,
        overallAverageProcessingTime: Math.round(overallAverageProcessingTime * 100) / 100,
        totalMessagesProcessed: metrics.totalMessages,
        totalMessagesSuccessful: metrics.successfulMessages,
        totalMessagesFailed: metrics.failedMessages,
        totalPendingMessages: queueStats.pendingQueueDepth,
        totalRetryAttempts: metrics.retryAttempts,
        workers,
        queueStats,
        persistenceStats,
        configuration: {
          workerCount: this.configService.get<number>('sms.workers.count', 0),
          throughputPerSecond: this.configService.get<number>('sms.workers.throughputPerSecond', 5000),
          maxRetries: this.configService.get<number>('sms.retries', 3),
          maxConcurrentMessages: this.configService.get<number>('sms.workers.maxConcurrentMessages', 100),
        }
      };

      const httpStatus = result.status === 'up' ? HttpStatus.OK : HttpStatus.SERVICE_UNAVAILABLE;
      
      response.status(httpStatus).json({
        statusCode: httpStatus,
        timestamp: new Date().toISOString(),
        ...result
      });
    } catch (error) {
      response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        status: 'error',
        message: error.message || 'Unknown error occurred while getting worker status',
        timestamp: new Date().toISOString()
      });
    }
  }

  @Get('health')
  @ApiOperation({ summary: 'Get worker health status' })
  @ApiResponse({
    status: 200,
    description: 'Returns worker health information',
  })
  async getWorkerHealth(@Res() response: Response): Promise<void> {
    try {
      const healthResult = this.workerManager.checkHealth();
      const httpStatus = healthResult.status === 'up' ? HttpStatus.OK : HttpStatus.SERVICE_UNAVAILABLE;
      
      response.status(httpStatus).json({
        statusCode: httpStatus,
        status: healthResult.status,
        workerCount: healthResult.workerCount,
        expectedWorkerCount: healthResult.expectedWorkerCount,
        details: healthResult.details,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        status: 'error',
        message: error.message || 'Unknown error occurred while checking worker health',
        timestamp: new Date().toISOString()
      });
    }
  }

  @Get('metrics')
  @ApiOperation({ summary: 'Get worker performance metrics' })
  @ApiResponse({
    status: 200,
    description: 'Returns detailed worker performance metrics',
  })
  async getWorkerMetrics(@Res() response: Response): Promise<void> {
    try {
      const metrics = this.metricsService.getMetrics();
      const workers = await this.getWorkerMetricsData();
      
      response.status(HttpStatus.OK).json({
        statusCode: HttpStatus.OK,
        timestamp: new Date().toISOString(),
        metrics: {
          ...metrics,
          workers,
          overallThroughputPerSecond: this.calculateOverallThroughput(workers),
          overallSuccessRate: metrics.totalMessages > 0 
            ? (metrics.successfulMessages / metrics.totalMessages) * 100 
            : 0
        }
      });
    } catch (error) {
      response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        status: 'error',
        message: error.message || 'Unknown error occurred while getting worker metrics',
        timestamp: new Date().toISOString()
      });
    }
  }

  @Get('failed-sms')
  @ApiOperation({ summary: 'Get failed SMS messages' })
  @ApiResponse({
    status: 200,
    description: 'Returns failed SMS messages from persistence',
  })
  async getFailedSms(@Res() response: Response): Promise<void> {
    try {
      const failedMessages = await this.failedSmsPersistence.loadFailedSms();
      const stats = await this.failedSmsPersistence.getPersistenceStats();
      
      response.status(HttpStatus.OK).json({
        statusCode: HttpStatus.OK,
        timestamp: new Date().toISOString(),
        failedMessages,
        stats: {
          totalFailed: stats.failedCount,
          totalSize: stats.totalSize
        }
      });
    } catch (error) {
      response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        status: 'error',
        message: error.message || 'Unknown error occurred while getting failed SMS',
        timestamp: new Date().toISOString()
      });
    }
  }

  @Get('pending-sms')
  @ApiOperation({ summary: 'Get pending SMS messages' })
  @ApiResponse({
    status: 200,
    description: 'Returns pending SMS messages from persistence',
  })
  async getPendingSms(@Res() response: Response): Promise<void> {
    try {
      const pendingMessages = await this.failedSmsPersistence.loadPendingSms();
      const stats = await this.failedSmsPersistence.getPersistenceStats();
      
      response.status(HttpStatus.OK).json({
        statusCode: HttpStatus.OK,
        timestamp: new Date().toISOString(),
        pendingMessages,
        stats: {
          totalPending: stats.pendingCount,
          totalSize: stats.totalSize
        }
      });
    } catch (error) {
      response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        status: 'error',
        message: error.message || 'Unknown error occurred while getting pending SMS',
        timestamp: new Date().toISOString()
      });
    }
  }

  @Get('retry-stats')
  @ApiOperation({ summary: 'Get retry statistics' })
  @ApiResponse({
    status: 200,
    description: 'Returns retry statistics for failed SMS messages',
  })
  async getRetryStats(@Res() response: Response): Promise<void> {
    try {
      const retryStats = await this.failedSmsRetry.getRetryStats();
      
      response.status(HttpStatus.OK).json({
        statusCode: HttpStatus.OK,
        timestamp: new Date().toISOString(),
        retryStats
      });
    } catch (error) {
      response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        status: 'error',
        message: error.message || 'Unknown error occurred while getting retry stats',
        timestamp: new Date().toISOString()
      });
    }
  }

  @Get('retry-all')
  @ApiOperation({ summary: 'Retry all failed SMS messages' })
  @ApiResponse({
    status: 200,
    description: 'Attempts to retry all failed SMS messages',
  })
  async retryAllFailedSms(@Res() response: Response): Promise<void> {
    try {
      const result = await this.failedSmsRetry.retryAllFailedSms();
      
      response.status(HttpStatus.OK).json({
        statusCode: HttpStatus.OK,
        timestamp: new Date().toISOString(),
        message: 'Retry operation completed',
        result
      });
    } catch (error) {
      response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        status: 'error',
        message: error.message || 'Unknown error occurred while retrying failed SMS',
        timestamp: new Date().toISOString()
      });
    }
  }

  private async getWorkerMetricsData(): Promise<WorkerMetrics[]> {
    const healthResult = this.workerManager.checkHealth();
    const metrics = this.metricsService.getMetrics();
    const workers: WorkerMetrics[] = [];
    
    // Get worker details from health result
    if (healthResult.details && typeof healthResult.details === 'object') {
      for (const [workerId, details] of Object.entries(healthResult.details)) {
        if (typeof details === 'object' && details !== null) {
          const workerDetails = details as any;
          const id = parseInt(workerId.replace('worker_', ''));
          const now = new Date();
          const lastHeartbeat = new Date(workerDetails.lastHeartbeat);
          const timeSinceLastHeartbeat = Math.round((now.getTime() - lastHeartbeat.getTime()) / 1000);
          
          // Calculate worker-specific metrics (simplified - in a real implementation, 
          // you'd track these per worker)
          const messagesProcessed = Math.floor(metrics.totalMessages / (healthResult.workerCount || 1));
          const messagesSuccessful = Math.floor(metrics.successfulMessages / (healthResult.workerCount || 1));
          const messagesFailed = Math.floor(metrics.failedMessages / (healthResult.workerCount || 1));
          const successRate = messagesProcessed > 0 ? (messagesSuccessful / messagesProcessed) * 100 : 0;
          
          workers.push({
            workerId: id,
            status: workerDetails.status,
            lastHeartbeat: workerDetails.lastHeartbeat,
            timeSinceLastHeartbeat: `${timeSinceLastHeartbeat}s`,
            messagesProcessed,
            messagesSuccessful,
            messagesFailed,
            successRate: Math.round(successRate * 100) / 100,
            averageProcessingTime: Math.round(metrics.averageProcessingTime * 100) / 100,
            throughputPerSecond: this.configService.get<number>('sms.workers.throughputPerSecond', 5000),
            pendingMessages: 0, // This would be calculated per worker in a real implementation
            retryAttempts: Math.floor(metrics.retryAttempts / (healthResult.workerCount || 1)),
            uptime: `${timeSinceLastHeartbeat}s`
          });
        }
      }
    }
    
    return workers;
  }

  private async getQueueStats(): Promise<WorkerStatusResponse['queueStats']> {
    try {
      // In a real implementation, you'd get actual queue depths from RabbitMQ
      // For now, we'll return estimated values based on metrics
      const metrics = this.metricsService.getMetrics();
      
      return {
        singleQueueDepth: metrics.queueDepths['sms_single_queue'] || 0,
        bulkQueueDepth: metrics.queueDepths['sms_bulk_queue'] || 0,
        dlrQueueDepth: metrics.queueDepths['sms_dlr_queue'] || 0,
        failedQueueDepth: metrics.queueDepths['sms_failed_queue'] || 0,
        pendingQueueDepth: metrics.queueDepths['sms_pending_queue'] || 0,
        retryQueueDepth: metrics.queueDepths['sms_retry_queue'] || 0,
        dlxQueueDepth: metrics.queueDepths['dlx_queue'] || 0,
      };
    } catch (error) {
      return {
        singleQueueDepth: 0,
        bulkQueueDepth: 0,
        dlrQueueDepth: 0,
        failedQueueDepth: 0,
        pendingQueueDepth: 0,
        retryQueueDepth: 0,
        dlxQueueDepth: 0,
      };
    }
  }

  private calculateOverallThroughput(workers: WorkerMetrics[]): number {
    return workers.reduce((total, worker) => total + worker.throughputPerSecond, 0);
  }
}

/**
 * Format uptime in a human-readable format
 *
 * @param uptime - Uptime in seconds
 * @returns Formatted uptime string
 */
function formatUptime(uptime: number): string {
  const hours = Math.floor(uptime / 3600);
  const minutes = Math.floor((uptime % 3600) / 60);
  const seconds = Math.floor(uptime % 60);

  return `${hours}h ${minutes}m ${seconds}s`;
}
