const http = require('http');

/**
 * Make a request to the API
 * @param {string} path - The path to request
 * @param {string|null} token - The API token to include (or null for no token)
 * @returns {Promise<object>} - The response
 */
function makeRequest(path, token = null) {
  return new Promise((resolve, reject) => {
    // Configure the request options
    const options = {
      hostname: 'localhost',
      port: 3000,
      path,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    };

    // Add the token header if provided
    if (token) {
      options.headers['x-api-token'] = token;
    }

    // Create the request
    const req = http.request(options, (res) => {
      let data = '';

      // Collect the response data
      res.on('data', (chunk) => {
        data += chunk;
      });

      // Resolve the promise when the response is complete
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: parsedData
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: data
          });
        }
      });
    });

    // Handle request errors
    req.on('error', (error) => {
      reject(error);
    });

    // End the request
    req.end();
  });
}

/**
 * Test the auth token middleware
 */
async function testAuthToken() {
  console.log('Testing auth token middleware...');

  try {
    // Test 1: Request without token (should fail)
    console.log('\nTest 1: Request without token');
    try {
      const noTokenResponse = await makeRequest('/api/v1/config');
      console.log(`Status code: ${noTokenResponse.statusCode}`);
      console.log(`Response: ${JSON.stringify(noTokenResponse.body, null, 2)}`);

      // Print headers for debugging
      console.log('Response headers:');
      Object.entries(noTokenResponse.headers).forEach(([key, value]) => {
        console.log(`  ${key}: ${value}`);
      });
    } catch (error) {
      console.log(`Error: ${error.message}`);
    }

    // Test 2: Request with invalid token (should fail)
    console.log('\nTest 2: Request with invalid token');
    try {
      const invalidTokenResponse = await makeRequest('/api/v1/config', 'invalid-token');
      console.log(`Status code: ${invalidTokenResponse.statusCode}`);
      console.log(`Response: ${JSON.stringify(invalidTokenResponse.body, null, 2)}`);
    } catch (error) {
      console.log(`Error: ${error.message}`);
    }

    // Test 3: Request with valid token (should succeed)
    console.log('\nTest 3: Request with valid token');
    try {
      const validTokenResponse = await makeRequest('/api/v1/config', 'geezsms-api-token-2025');
      console.log(`Status code: ${validTokenResponse.statusCode}`);
      console.log(`Response: ${JSON.stringify(validTokenResponse.body, null, 2)}`);
    } catch (error) {
      console.log(`Error: ${error.message}`);
    }

    // Test 4: Health endpoint (should succeed without token)
    console.log('\nTest 4: Health endpoint (no token)');
    try {
      const healthResponse = await makeRequest('/api/v1/health');
      console.log(`Status code: ${healthResponse.statusCode}`);
      console.log(`Response body length: ${JSON.stringify(healthResponse.body).length} characters`);
    } catch (error) {
      console.log(`Error: ${error.message}`);
    }

    // Summary
    console.log('\nTest Summary:');
    console.log('Tests completed. Check the logs above for details.');
  } catch (error) {
    console.error('Error testing auth token:', error.message);
  }
}

// Run the test
testAuthToken();
