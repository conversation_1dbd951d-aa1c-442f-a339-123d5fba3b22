const axios = require('axios');
const { performance } = require('perf_hooks');

const testSmsData = {
  to: '251911234567',
  message: 'Performance test message',
  sms_credential: {
    usr: 'test_user',
    pwd: 'test_password',
    from: 'TEST',
    token: 'test_token_12345'
  }
};

// Test configurations
const testConfigs = [
  { name: 'Low Load', concurrentRequests: 10, duration: 10 },
  { name: 'Medium Load', concurrentRequests: 25, duration: 10 },
  { name: 'High Load', concurrentRequests: 50, duration: 10 },
  { name: 'Very High Load', concurrentRequests: 100, duration: 10 }
];

async function sendBatch(concurrentRequests, batchNumber) {
  console.log(`📦 Sending batch ${batchNumber} (${concurrentRequests} concurrent requests)...`);
  
  const batchStartTime = performance.now();
  const promises = Array.from({ length: concurrentRequests }, (_, i) => {
    const data = {
      ...testSmsData,
      message: `Batch ${batchNumber} - Message ${i + 1}`
    };
    
    return axios.post('http://localhost:3000/api/v1/sms/send/single', data, {
      timeout: 15000,
      headers: {
        'Authorization': 'Bearer ccda1683d8c97f8f2dff2ea7d649b42c',
        'Content-Type': 'application/json'
      }
    }).then(response => ({ success: true, responseTime: 0, status: response.status }))
      .catch(error => ({ 
        success: false, 
        responseTime: 0, 
        error: error.message,
        status: error.response?.status 
      }));
  });
  
  const results = await Promise.all(promises);
  const batchEndTime = performance.now();
  const batchDuration = (batchEndTime - batchStartTime) / 1000;
  
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  const throughput = successful / batchDuration;
  
  console.log(`✅ Batch ${batchNumber} completed:`);
  console.log(`   - Success: ${successful}/${concurrentRequests} (${((successful / concurrentRequests) * 100).toFixed(2)}%)`);
  console.log(`   - Batch duration: ${batchDuration.toFixed(2)}s`);
  console.log(`   - Throughput: ${throughput.toFixed(2)} SMS/s`);
  
  if (failed > 0) {
    const errorTypes = {};
    results.filter(r => !r.success).forEach(r => {
      const errorKey = r.error || `Status ${r.status}`;
      errorTypes[errorKey] = (errorTypes[errorKey] || 0) + 1;
    });
    console.log(`   - Error types: ${JSON.stringify(errorTypes)}`);
  }
  
  return { successful, failed, throughput, batchDuration };
}

async function runGradualTest() {
  console.log('🚀 Starting Gradual Performance Test');
  console.log('Target: Find optimal throughput for 5K SMS/second');
  console.log('');
  
  for (const config of testConfigs) {
    console.log(`🎯 Testing ${config.name} (${config.concurrentRequests} concurrent requests)`);
    console.log(`⏱️  Duration: ${config.duration} seconds`);
    console.log('');
    
    const startTime = performance.now();
    let totalSuccessful = 0;
    let totalFailed = 0;
    let batchCount = 0;
    
    while ((performance.now() - startTime) / 1000 < config.duration) {
      batchCount++;
      const result = await sendBatch(config.concurrentRequests, batchCount);
      totalSuccessful += result.successful;
      totalFailed += result.failed;
      
      // Small delay between batches
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    const totalTime = (performance.now() - startTime) / 1000;
    const overallThroughput = totalSuccessful / totalTime;
    const successRate = (totalSuccessful / (totalSuccessful + totalFailed)) * 100;
    
    console.log(`📊 ${config.name} Results:`);
    console.log(`   - Total time: ${totalTime.toFixed(2)}s`);
    console.log(`   - Total requests: ${totalSuccessful + totalFailed}`);
    console.log(`   - Successful: ${totalSuccessful}`);
    console.log(`   - Failed: ${totalFailed}`);
    console.log(`   - Success rate: ${successRate.toFixed(2)}%`);
    console.log(`   - Overall throughput: ${overallThroughput.toFixed(2)} SMS/s`);
    console.log('');
    
    // If we're getting good results, we can try higher loads
    if (successRate >= 95 && overallThroughput > 100) {
      console.log(`✅ ${config.name} is performing well!`);
    } else if (successRate < 80) {
      console.log(`⚠️  ${config.name} has issues - stopping escalation`);
      break;
    }
  }
  
  console.log('🏁 Gradual performance test completed!');
}

runGradualTest().catch(console.error); 