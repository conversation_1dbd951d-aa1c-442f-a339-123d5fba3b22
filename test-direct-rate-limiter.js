// This script tests the rate limiter middleware directly without relying on the server

// Import the rate limiter middleware
const { RateLimiterMiddleware } = require('./dist/shared/middleware/rate-limiter.middleware');
const { ConfigService } = require('@nestjs/config');

// Create a mock config service
class MockConfigService {
  constructor(config = {}) {
    this.config = config;
  }

  get(key, defaultValue) {
    // Split the key by dots to support nested properties
    const parts = key.split('.');
    let value = this.config;
    
    for (const part of parts) {
      if (value && typeof value === 'object' && part in value) {
        value = value[part];
      } else {
        return defaultValue;
      }
    }
    
    return value !== undefined ? value : defaultValue;
  }
}

// Create a mock request
const mockRequest = {
  method: 'GET',
  originalUrl: '/api/v1/config',
  ip: '127.0.0.1',
  headers: {},
  query: {},
  requestId: 'test-request-id',
};

// Create a mock response
const mockResponse = {
  setHeader: (name, value) => {
    console.log(`Setting header: ${name} = ${value}`);
  },
  statusCode: 200,
};

// Create a mock next function
const mockNext = () => {
  console.log('Next function called');
};

// Test the rate limiter middleware
async function testRateLimiter() {
  console.log('Testing rate limiter middleware directly...');
  
  // Create a config service with rate limiting enabled
  const configService = new MockConfigService({
    api: {
      throttle: {
        enabled: true,
        limit: 5,
        ttl: 60,
      },
    },
  });
  
  // Create the rate limiter middleware
  const rateLimiter = new RateLimiterMiddleware(configService);
  
  // Make multiple requests to test rate limiting
  const numRequests = 10;
  
  console.log(`Making ${numRequests} requests...`);
  
  for (let i = 0; i < numRequests; i++) {
    console.log(`\nRequest ${i + 1}:`);
    
    try {
      // Call the middleware
      rateLimiter.use(mockRequest, mockResponse, mockNext);
      console.log('Request allowed');
    } catch (error) {
      console.log(`Request blocked: ${error.message}`);
      
      // Check if it's a rate limit error
      if (error.status === 429) {
        console.log('Rate limit exceeded as expected');
      }
    }
  }
}

// Run the test
testRateLimiter().catch(error => {
  console.error('Error testing rate limiter:', error);
});
