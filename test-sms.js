/**
 * Test script for sending single and bulk SMS messages
 * 
 * This script sends test messages to the SMS API to verify functionality
 */
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

// Configuration
const API_BASE_URL = 'http://localhost:3000/api/v1';
const SINGLE_SMS_ENDPOINT = `${API_BASE_URL}/sms/send/single`;
const BULK_SMS_ENDPOINT = `${API_BASE_URL}/sms/send/bulk`;

// Test data
const singleSmsData = {
  to: '+1234567890',
  message: 'Test single SMS message',
  sms_credential: {
    usr: 'testuser',
    pwd: 'testpass',
    from: 'SENDER',
    token: 'testtoken'
  },
  requestId: `test-single-${uuidv4()}`
};

const bulkSmsData = {
  message: 'Test bulk SMS message',
  receivers: 'contact_group_123',
  bulk_type: 'DASHBOARD',
  user_id: 12345,
  billing_id: 67890,
  job_name: 'Test Bulk SMS',
  job_unique_id: `test-bulk-${uuidv4()}`,
  sms_body_id: 456,
  sms_credential: {
    usr: 'testuser',
    pwd: 'testpass',
    from: 'SENDER',
    token: 'testtoken'
  },
  requestId: `test-bulk-${uuidv4()}`
};

/**
 * Send a single SMS message
 */
async function testSingleSms() {
  console.log('Testing Single SMS...');
  console.log(`Request ID: ${singleSmsData.requestId}`);
  
  try {
    const response = await axios.post(SINGLE_SMS_ENDPOINT, singleSmsData);
    console.log('Single SMS Response:', JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.error('Single SMS Error:', error.response ? error.response.data : error.message);
    return false;
  }
}

/**
 * Send a bulk SMS message
 */
async function testBulkSms() {
  console.log('\nTesting Bulk SMS...');
  console.log(`Request ID: ${bulkSmsData.requestId}`);
  
  try {
    const response = await axios.post(BULK_SMS_ENDPOINT, bulkSmsData);
    console.log('Bulk SMS Response:', JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.error('Bulk SMS Error:', error.response ? error.response.data : error.message);
    return false;
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('Starting SMS Tests...\n');
  
  // Test single SMS
  const singleResult = await testSingleSms();
  
  // Test bulk SMS
  const bulkResult = await testBulkSms();
  
  // Summary
  console.log('\n--- Test Results ---');
  console.log(`Single SMS: ${singleResult ? 'SUCCESS ✅' : 'FAILED ❌'}`);
  console.log(`Bulk SMS: ${bulkResult ? 'SUCCESS ✅' : 'FAILED ❌'}`);
  
  process.exit(singleResult && bulkResult ? 0 : 1);
}

// Run the tests
runTests();
