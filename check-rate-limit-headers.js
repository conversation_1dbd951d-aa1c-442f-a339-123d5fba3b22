const http = require('http');

/**
 * Make a request to the API and check the rate limit headers
 */
function makeRequest() {
  return new Promise((resolve, reject) => {
    // Configure the request options
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/config',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    };

    // Create the request
    const req = http.request(options, (res) => {
      let data = '';

      // Collect the response data
      res.on('data', (chunk) => {
        data += chunk;
      });

      // Resolve the promise when the response is complete
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });

    // Handle request errors
    req.on('error', (error) => {
      reject(error);
    });

    // End the request
    req.end();
  });
}

/**
 * Check if the rate limit headers are present
 */
async function checkRateLimitHeaders() {
  console.log('Checking rate limit headers...');

  try {
    const response = await makeRequest();
    
    console.log(`Status code: ${response.statusCode}`);
    
    // Check for rate limit headers
    const rateLimitLimit = response.headers['x-ratelimit-limit'];
    const rateLimitRemaining = response.headers['x-ratelimit-remaining'];
    const rateLimitReset = response.headers['x-ratelimit-reset'];
    
    if (rateLimitLimit && rateLimitRemaining && rateLimitReset) {
      console.log('Rate limit headers are present:');
      console.log(`  X-RateLimit-Limit: ${rateLimitLimit}`);
      console.log(`  X-RateLimit-Remaining: ${rateLimitRemaining}`);
      console.log(`  X-RateLimit-Reset: ${rateLimitReset}`);
      
      // Convert reset timestamp to human-readable date
      const resetDate = new Date(rateLimitReset * 1000);
      console.log(`  Reset time: ${resetDate.toISOString()}`);
      
      console.log('\nRate limiting appears to be working!');
    } else {
      console.log('Rate limit headers are not present.');
      console.log('Rate limiting does not appear to be working.');
      
      // Log all headers for debugging
      console.log('\nAll response headers:');
      Object.entries(response.headers).forEach(([key, value]) => {
        console.log(`  ${key}: ${value}`);
      });
    }
  } catch (error) {
    console.error('Error checking rate limit headers:', error.message);
  }
}

// Run the check
checkRateLimitHeaders();
