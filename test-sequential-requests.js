const http = require('http');

/**
 * Make a request to the API
 * @param {string} path - The path to request
 * @returns {Promise<object>} - The response
 */
function makeRequest(path) {
  return new Promise((resolve, reject) => {
    // Configure the request options
    const options = {
      hostname: 'localhost',
      port: 3000,
      path,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    };

    // Create the request
    const req = http.request(options, (res) => {
      let data = '';

      // Collect the response data
      res.on('data', (chunk) => {
        data += chunk;
      });

      // Resolve the promise when the response is complete
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: parsedData
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: data
          });
        }
      });
    });

    // Handle request errors
    req.on('error', (error) => {
      reject(error);
    });

    // End the request
    req.end();
  });
}

/**
 * Make sequential requests to test rate limiting
 */
async function testSequentialRequests() {
  console.log('Testing rate limiting with sequential requests...');

  try {
    // Make multiple requests to the config endpoint
    const numRequests = 15; // More than our expected limit of 10
    
    console.log(`Making ${numRequests} sequential requests to the config endpoint...`);
    
    // Track responses by status code
    const statusCounts = {};
    
    for (let i = 0; i < numRequests; i++) {
      console.log(`\nRequest ${i + 1}:`);
      
      // Make the request
      const response = await makeRequest('/api/v1/config');
      
      // Count the status code
      const status = response.statusCode;
      statusCounts[status] = (statusCounts[status] || 0) + 1;
      
      // Log the response
      console.log(`Status code: ${status}`);
      
      // Check for rate limit headers
      const rateLimitLimit = response.headers['x-ratelimit-limit'];
      const rateLimitRemaining = response.headers['x-ratelimit-remaining'];
      const rateLimitReset = response.headers['x-ratelimit-reset'];
      
      if (rateLimitLimit && rateLimitRemaining && rateLimitReset) {
        console.log(`Rate limit: ${rateLimitRemaining}/${rateLimitLimit} remaining`);
        
        // Check if we're close to the limit
        if (parseInt(rateLimitRemaining) <= 2) {
          console.log('Warning: Close to rate limit!');
        }
      } else {
        console.log('No rate limit headers found');
      }
      
      // If we get a 429 response, we've hit the rate limit
      if (status === 429) {
        console.log('Rate limit exceeded!');
        console.log(`Retry-After: ${response.headers['retry-after']} seconds`);
      }
      
      // Add a small delay between requests
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // Log the results
    console.log('\nResponse status counts:');
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`  ${status}: ${count}`);
    });
    
    // Check if rate limiting is working
    if (statusCounts['429']) {
      console.log('\nRate limiting is working! Some requests were rate limited.');
    } else {
      console.log('\nRate limiting does not appear to be working. No requests were rate limited.');
    }
  } catch (error) {
    console.error('Error testing rate limiting:', error.message);
  }
}

// Run the test
testSequentialRequests();
