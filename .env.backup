# .env
# App Configuration
NODE_ENV=development
PORT=3000
# Zergaw=http://***************
# Local=http://**************
APP_URL=http://*************:3000

# Security
API_THROTTLE_ENABLED=false
API_THROTTLE_LIMIT=10      # Requests per minute (low value for testing)
API_THROTTLE_TTL=60        # Window size in seconds
API_AUTH_TOKEN=ccda1683d8c97f8f2dff2ea7d649b42c  # API authentication token
CORS_ORIGINS=http://localhost:3000

# Logging
LOG_LEVEL=debug

# RabbitMQ
RABBITMQ_USER=geezsl
RABBITMQ_PASSWORD=g1e2eG7sa
RABBITMQ_URL=amqp://geezsl:g1e2eG7sa@rabbitmq:5672
RABBITMQ_QUEUE=sms_queue
RABBITMQ_CONNECTION_TIMEOUT=5000  # 5 seconds
RABBITMQ_HEARTBEAT=60             # 60 seconds
# RabbitMQ Health Check
RABBITMQ_HEALTHCHECK_TIMEOUT=5000
RABBITMQ_HEALTHCHECK_QUEUE=healthcheck

# SMS Gateway (example)
SMS_API_KEY=your_api_key
SMS_API_URL=https://api.smsprovider.com/v1
BACKENDHOST_RESTFULL_API=https://api.geezsms.com/api
TELE_JASMINE_API_URL=http://127.0.0.1:8080/secure
# TELE_JASMINE_API_URL=http://127.0.0.1:8080/secure
SAFARICOM_JASMINE_API_URL=http://127.0.0.1:8080/secure

# Queue
QUEUE_SMS_SINGLE=sms_single_queue
QUEUE_SMS_BULK=sms_bulk_queue
SMS_MAX_RETRIES=2
SMS_RETRY_DELAY_MS=1000

# Backend
BACKENDHOST="https://api.geezsms.com"FAILED_SMS_PERSISTENCE_PATH=./logs/failed-sms.json
PENDING_SMS_PERSISTENCE_PATH=./logs/pending-sms.json
