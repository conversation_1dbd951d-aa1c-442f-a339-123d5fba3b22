const { exec } = require('child_process');
const { performance } = require('perf_hooks');

class SystemMonitor {
  constructor() {
    this.metrics = [];
    this.isMonitoring = false;
    this.startTime = null;
  }

  async getSystemMetrics() {
    return new Promise((resolve) => {
      exec('top -bn1 | head -20', (error, stdout) => {
        if (error) {
          resolve({ error: error.message });
          return;
        }
        
        const lines = stdout.split('\n');
        const loadAvg = lines[0].match(/load average: ([\d.]+), ([\d.]+), ([\d.]+)/);
        const cpuLine = lines[2];
        const memLine = lines[3];
        
        const metrics = {
          timestamp: new Date().toISOString(),
          loadAverage: loadAvg ? {
            '1min': parseFloat(loadAvg[1]),
            '5min': parseFloat(loadAvg[2]),
            '15min': parseFloat(loadAvg[3])
          } : null,
          cpu: this.parseCpuLine(cpuLine),
          memory: this.parseMemoryLine(memLine)
        };
        
        resolve(metrics);
      });
    });
  }

  parseCpuLine(line) {
    if (!line) return null;
    
    const match = line.match(/%Cpu\(s\):\s+([\d.]+)\s+us,\s+([\d.]+)\s+sy,\s+([\d.]+)\s+ni,\s+([\d.]+)\s+id/);
    if (match) {
      return {
        user: parseFloat(match[1]),
        system: parseFloat(match[2]),
        nice: parseFloat(match[3]),
        idle: parseFloat(match[4])
      };
    }
    return null;
  }

  parseMemoryLine(line) {
    if (!line) return null;
    
    const match = line.match(/MiB\s+Mem\s*:\s*([\d.]+)\s+total,\s+([\d.]+)\s+free,\s+([\d.]+)\s+used,\s+([\d.]+)\s+buff\/cache/);
    if (match) {
      return {
        total: parseFloat(match[1]),
        free: parseFloat(match[2]),
        used: parseFloat(match[3]),
        buffCache: parseFloat(match[4])
      };
    }
    return null;
  }

  async getProcessMetrics() {
    return new Promise((resolve) => {
      exec('ps aux | grep -E "(node|nest)" | grep -v grep', (error, stdout) => {
        if (error) {
          resolve({ error: error.message });
          return;
        }
        
        const processes = stdout.split('\n').filter(line => line.trim()).map(line => {
          const parts = line.split(/\s+/);
          return {
            pid: parts[1],
            cpu: parseFloat(parts[2]),
            memory: parseFloat(parts[3]),
            command: parts.slice(10).join(' ')
          };
        });
        
        resolve(processes);
      });
    });
  }

  async getNetworkMetrics() {
    return new Promise((resolve) => {
      exec('netstat -i | grep -E "(eth0|wlan0|lo)"', (error, stdout) => {
        if (error) {
          resolve({ error: error.message });
          return;
        }
        
        const interfaces = stdout.split('\n').filter(line => line.trim()).map(line => {
          const parts = line.split(/\s+/);
          return {
            interface: parts[0],
            rxPackets: parseInt(parts[3]) || 0,
            txPackets: parseInt(parts[7]) || 0,
            rxErrors: parseInt(parts[4]) || 0,
            txErrors: parseInt(parts[8]) || 0
          };
        });
        
        resolve(interfaces);
      });
    });
  }

  async getDiskMetrics() {
    return new Promise((resolve) => {
      exec('df -h / | tail -1', (error, stdout) => {
        if (error) {
          resolve({ error: error.message });
          return;
        }
        
        const parts = stdout.split(/\s+/);
        resolve({
          filesystem: parts[0],
          size: parts[1],
          used: parts[2],
          available: parts[3],
          usePercent: parts[4]
        });
      });
    });
  }

  async startMonitoring(intervalMs = 2000) {
    this.isMonitoring = true;
    this.startTime = performance.now();
    this.metrics = [];
    
    console.log('🔍 Starting system monitoring...');
    console.log(`📊 Monitoring interval: ${intervalMs}ms`);
    console.log('');
    
    const monitor = async () => {
      if (!this.isMonitoring) return;
      
      try {
        const [systemMetrics, processMetrics, networkMetrics, diskMetrics] = await Promise.all([
          this.getSystemMetrics(),
          this.getProcessMetrics(),
          this.getNetworkMetrics(),
          this.getDiskMetrics()
        ]);
        
        const metric = {
          timestamp: new Date().toISOString(),
          elapsed: (performance.now() - this.startTime) / 1000,
          system: systemMetrics,
          processes: processMetrics,
          network: networkMetrics,
          disk: diskMetrics
        };
        
        this.metrics.push(metric);
        
        // Display current metrics
        this.displayCurrentMetrics(metric);
        
      } catch (error) {
        console.log(`❌ Error collecting metrics: ${error.message}`);
      }
      
      if (this.isMonitoring) {
        setTimeout(monitor, intervalMs);
      }
    };
    
    monitor();
  }

  displayCurrentMetrics(metric) {
    const elapsed = metric.elapsed.toFixed(1);
    
    console.log(`⏱️  [${elapsed}s] System Metrics:`);
    
    if (metric.system.loadAverage) {
      console.log(`   📈 Load Average: ${metric.system.loadAverage['1min']}/${metric.system.loadAverage['5min']}/${metric.system.loadAverage['15min']}`);
    }
    
    if (metric.system.cpu) {
      const cpu = metric.system.cpu;
      console.log(`   🖥️  CPU: ${cpu.user.toFixed(1)}% user, ${cpu.system.toFixed(1)}% system, ${cpu.idle.toFixed(1)}% idle`);
    }
    
    if (metric.system.memory) {
      const mem = metric.system.memory;
      const usedPercent = ((mem.used / mem.total) * 100).toFixed(1);
      console.log(`   💾 Memory: ${mem.used.toFixed(0)}/${mem.total.toFixed(0)} MB (${usedPercent}% used)`);
    }
    
    if (metric.processes && metric.processes.length > 0) {
      const nodeProcesses = metric.processes.filter(p => p.command.includes('node') || p.command.includes('nest'));
      if (nodeProcesses.length > 0) {
        console.log(`   🔧 Node Processes: ${nodeProcesses.length} running`);
        nodeProcesses.forEach(p => {
          console.log(`      PID ${p.pid}: ${p.cpu.toFixed(1)}% CPU, ${p.memory.toFixed(1)}% MEM`);
        });
      }
    }
    
    console.log('');
  }

  stopMonitoring() {
    this.isMonitoring = false;
    console.log('🛑 System monitoring stopped');
    return this.metrics;
  }

  generateReport() {
    if (this.metrics.length === 0) {
      console.log('❌ No metrics collected');
      return;
    }
    
    console.log('📊 System Performance Report');
    console.log('============================');
    console.log(`Duration: ${((performance.now() - this.startTime) / 1000).toFixed(2)} seconds`);
    console.log(`Samples: ${this.metrics.length}`);
    console.log('');
    
    // Calculate averages
    const avgLoad = this.metrics.reduce((sum, m) => sum + (m.system.loadAverage?.['1min'] || 0), 0) / this.metrics.length;
    const avgCpu = this.metrics.reduce((sum, m) => sum + (m.system.cpu?.user || 0) + (m.system.cpu?.system || 0), 0) / this.metrics.length;
    const avgMem = this.metrics.reduce((sum, m) => sum + ((m.system.memory?.used / m.system.memory?.total) * 100 || 0), 0) / this.metrics.length;
    
    console.log(`📈 Average Load: ${avgLoad.toFixed(2)}`);
    console.log(`🖥️  Average CPU Usage: ${avgCpu.toFixed(1)}%`);
    console.log(`💾 Average Memory Usage: ${avgMem.toFixed(1)}%`);
    console.log('');
    
    // Find peak values
    const peakLoad = Math.max(...this.metrics.map(m => m.system.loadAverage?.['1min'] || 0));
    const peakCpu = Math.max(...this.metrics.map(m => (m.system.cpu?.user || 0) + (m.system.cpu?.system || 0)));
    const peakMem = Math.max(...this.metrics.map(m => ((m.system.memory?.used / m.system.memory?.total) * 100) || 0));
    
    console.log(`📈 Peak Load: ${peakLoad.toFixed(2)}`);
    console.log(`🖥️  Peak CPU Usage: ${peakCpu.toFixed(1)}%`);
    console.log(`💾 Peak Memory Usage: ${peakMem.toFixed(1)}%`);
    console.log('');
    
    return {
      duration: (performance.now() - this.startTime) / 1000,
      samples: this.metrics.length,
      averages: { load: avgLoad, cpu: avgCpu, memory: avgMem },
      peaks: { load: peakLoad, cpu: peakCpu, memory: peakMem },
      metrics: this.metrics
    };
  }
}

// Export for use in other scripts
module.exports = SystemMonitor;

// If run directly, start monitoring
if (require.main === module) {
  const monitor = new SystemMonitor();
  
  console.log('🚀 System Monitor Started');
  console.log('Press Ctrl+C to stop monitoring');
  console.log('');
  
  monitor.startMonitoring(2000);
  
  process.on('SIGINT', () => {
    console.log('\n🛑 Stopping system monitor...');
    const report = monitor.generateReport();
    process.exit(0);
  });
} 