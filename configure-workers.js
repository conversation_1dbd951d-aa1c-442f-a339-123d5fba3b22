#!/usr/bin/env node

const fs = require('fs');
const os = require('os');

/**
 * Worker Configuration Script for GeezSMS
 * Helps configure optimal worker count based on system resources and target throughput
 */

const CONFIG_SCENARIOS = {
  // Development/Testing scenarios
  development: {
    description: 'Development environment with minimal resources',
    targetThroughput: 100,
    workerCount: 2,
    memoryLimit: 256,
    logLevel: 'debug'
  },
  
  testing: {
    description: 'Testing environment with moderate load',
    targetThroughput: 500,
    workerCount: 5,
    memoryLimit: 512,
    logLevel: 'info'
  },
  
  // Production scenarios
  production_small: {
    description: 'Small production server (2-4 CPU cores)',
    targetThroughput: 1000,
    workerCount: 8,
    memoryLimit: 512,
    logLevel: 'warn'
  },
  
  production_medium: {
    description: 'Medium production server (4-8 CPU cores)',
    targetThroughput: 2500,
    workerCount: 15,
    memoryLimit: 512,
    logLevel: 'warn'
  },
  
  production_large: {
    description: 'Large production server (8+ CPU cores)',
    targetThroughput: 5000,
    workerCount: 25,
    memoryLimit: 512,
    logLevel: 'error'
  },
  
  production_enterprise: {
    description: 'Enterprise server (16+ CPU cores)',
    targetThroughput: 10000,
    workerCount: 40,
    memoryLimit: 1024,
    logLevel: 'error'
  },
  
  // Custom scenarios
  custom_1k: {
    description: 'Custom 1K SMS/second target',
    targetThroughput: 1000,
    workerCount: 10,
    memoryLimit: 512,
    logLevel: 'warn'
  },
  
  custom_2k: {
    description: 'Custom 2K SMS/second target',
    targetThroughput: 2000,
    workerCount: 15,
    memoryLimit: 512,
    logLevel: 'warn'
  },
  
  custom_5k: {
    description: 'Custom 5K SMS/second target',
    targetThroughput: 5000,
    workerCount: 25,
    memoryLimit: 512,
    logLevel: 'error'
  },
  
  custom_10k: {
    description: 'Custom 10K SMS/second target',
    targetThroughput: 10000,
    workerCount: 50,
    memoryLimit: 1024,
    logLevel: 'error'
  }
};

function analyzeSystem() {
  const cpuCores = os.cpus().length;
  const totalMemory = Math.round(os.totalmem() / (1024 * 1024 * 1024)); // GB
  const freeMemory = Math.round(os.freemem() / (1024 * 1024 * 1024)); // GB
  
  console.log('🔍 System Analysis');
  console.log('==================');
  console.log(`💻 CPU Cores: ${cpuCores}`);
  console.log(`🧠 Total Memory: ${totalMemory}GB`);
  console.log(`🆓 Available Memory: ${freeMemory}GB`);
  console.log(`📊 Memory Usage: ${((totalMemory - freeMemory) / totalMemory * 100).toFixed(1)}%`);
  console.log('');
  
  return { cpuCores, totalMemory, freeMemory };
}

function recommendScenario(systemInfo) {
  const { cpuCores, totalMemory } = systemInfo;
  
  console.log('💡 Recommended Scenarios');
  console.log('========================');
  
  if (cpuCores <= 2 && totalMemory <= 4) {
    console.log('✅ Development: Perfect for development/testing');
    console.log('✅ Testing: Good for moderate testing');
  } else if (cpuCores <= 4 && totalMemory <= 8) {
    console.log('✅ Testing: Good for testing');
    console.log('✅ Production Small: Suitable for small production');
  } else if (cpuCores <= 8 && totalMemory <= 16) {
    console.log('✅ Production Medium: Good for medium production');
    console.log('✅ Custom 2K: Good for 2K SMS/second target');
  } else if (cpuCores <= 16 && totalMemory <= 32) {
    console.log('✅ Production Large: Excellent for large production');
    console.log('✅ Custom 5K: Perfect for 5K SMS/second target');
  } else {
    console.log('✅ Production Enterprise: Excellent for enterprise');
    console.log('✅ Custom 10K: Perfect for 10K SMS/second target');
  }
  
  console.log('');
}

function displayScenarios() {
  console.log('📋 Available Configuration Scenarios');
  console.log('====================================');
  
  Object.entries(CONFIG_SCENARIOS).forEach(([key, config]) => {
    console.log(`${key.padEnd(20)} - ${config.description}`);
    console.log(`  Target: ${config.targetThroughput.toLocaleString()} SMS/s | Workers: ${config.workerCount} | Memory: ${config.memoryLimit}MB | Log: ${config.logLevel}`);
    console.log('');
  });
}

function generateEnvFile(scenario) {
  const config = CONFIG_SCENARIOS[scenario];
  if (!config) {
    console.error(`❌ Unknown scenario: ${scenario}`);
    return false;
  }
  
  const messagesPerWorker = Math.ceil(config.targetThroughput / config.workerCount);
  
  const envContent = `# GeezSMS Configuration for ${scenario}
# Generated on ${new Date().toISOString()}
# ${config.description}

# Performance Settings
SMS_TARGET_THROUGHPUT=${config.targetThroughput}
SMS_PER_WORKER_PER_SECOND=${messagesPerWorker}
SMS_WORKER_COUNT=${config.workerCount}
SMS_MAX_RETRIES=2
SMS_RETRY_DELAY_MS=500
SMS_BACKOFF_MULTIPLIER=1.5

# Worker Throughput Settings
WORKER_SMS_THROUGHPUT_PER_SECOND=${messagesPerWorker}
WORKER_MAX_CONCURRENT_MESSAGES=50

# RabbitMQ Settings
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_POOL_SIZE=${Math.max(10, config.workerCount * 2)}
QUEUE_PREFETCH_COUNT=50
QUEUE_SMS_SINGLE=sms_single_queue
QUEUE_SMS_BULK=sms_bulk_queue
QUEUE_SMS_FAILED=sms_failed_queue
QUEUE_SMS_PENDING=sms_pending_queue

# HTTP Client Settings
HTTP_TIMEOUT_MS=5000
HTTP_MAX_CONNECTIONS=${config.workerCount * 20}

# Worker Settings
WORKER_MEMORY_LIMIT_MB=${config.memoryLimit}
WORKER_HEARTBEAT_INTERVAL_MS=5000

# Logging Settings
LOG_LEVEL=${config.logLevel}
ENABLE_WORKER_LOGS=true
LOGS_DIR=./logs
LOG_MAX_SIZE_MB=25
LOG_MAX_FILES=3

# Monitoring Settings
ENABLE_METRICS=true
METRICS_INTERVAL_MS=5000
ENABLE_HEALTH_CHECKS=true

# API Settings
API_AUTH_TOKEN=ccda1683d8c97f8f2dff2ea7d649b42c
API_THROTTLE_ENABLED=false
API_THROTTLE_TTL=60
API_THROTTLE_LIMIT=1000000

# Provider URLs
TELE_JASMINE_API_URL=https://api.tele.com/send
TELE_ENDPOINT=https://api.tele.com/send
SAFARICOM_JASMINE_API_URL=https://api.safaricom.com/send

# Application Settings
NODE_ENV=production
PORT=3000
APP_URL=http://localhost:3000

# Database/Backend Settings
BACKENDHOST_RESTFULL_API=http://localhost:8002

# Persistence Settings
ENABLE_FAILED_SMS_PERSISTENCE=true
FAILED_SMS_STORAGE_PATH=./data/failed_sms
PENDING_SMS_STORAGE_PATH=./data/pending_sms

# Batch Processing Settings
ENABLE_SMS_BATCHING=false
SMS_BATCH_SIZE=10
SMS_BATCH_TIMEOUT_MS=100

# Connection Pool Settings
MAX_CONNECTIONS=${config.workerCount * 10}
CONNECTION_TIMEOUT_MS=5000
KEEP_ALIVE=true
KEEP_ALIVE_MSECS=1000

# System Optimization
NODE_OPTIONS=--max-old-space-size=${Math.max(2048, config.workerCount * 100)} --max-semi-space-size=64
UV_THREADPOOL_SIZE=${Math.min(32, config.workerCount * 2)}
`;
  
  const filename = `env.${scenario}`;
  fs.writeFileSync(filename, envContent);
  
  console.log(`✅ Generated configuration file: ${filename}`);
  console.log(`📊 Configuration Summary:`);
  console.log(`   Target Throughput: ${config.targetThroughput.toLocaleString()} SMS/second`);
  console.log(`   Worker Count: ${config.workerCount}`);
  console.log(`   Per-Worker Throughput: ${messagesPerWorker} SMS/second`);
  console.log(`   Memory Limit: ${config.memoryLimit}MB per worker`);
  console.log(`   Log Level: ${config.logLevel}`);
  console.log('');
  console.log(`🚀 To use this configuration:`);
  console.log(`   cp ${filename} .env`);
  console.log(`   npm run start:prod`);
  console.log('');
  
  return true;
}

function showUsage() {
  console.log('Usage: node configure-workers.js [command] [scenario]');
  console.log('');
  console.log('Commands:');
  console.log('  analyze     - Analyze system resources and show recommendations');
  console.log('  list        - List all available configuration scenarios');
  console.log('  generate    - Generate .env file for specified scenario');
  console.log('  help        - Show this help message');
  console.log('');
  console.log('Examples:');
  console.log('  node configure-workers.js analyze');
  console.log('  node configure-workers.js list');
  console.log('  node configure-workers.js generate production_medium');
  console.log('  node configure-workers.js generate custom_5k');
  console.log('');
}

// Main execution
function main() {
  const command = process.argv[2];
  const scenario = process.argv[3];
  
  switch (command) {
    case 'analyze':
      const systemInfo = analyzeSystem();
      recommendScenario(systemInfo);
      break;
      
    case 'list':
      displayScenarios();
      break;
      
    case 'generate':
      if (!scenario) {
        console.error('❌ Please specify a scenario');
        console.log('Available scenarios:');
        Object.keys(CONFIG_SCENARIOS).forEach(key => console.log(`  - ${key}`));
        process.exit(1);
      }
      generateEnvFile(scenario);
      break;
      
    case 'help':
    case '--help':
    case '-h':
      showUsage();
      break;
      
    default:
      console.log('❌ Unknown command. Use "help" for usage information.');
      showUsage();
      process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { CONFIG_SCENARIOS, analyzeSystem, generateEnvFile }; 