const http = require('http');
const { performance } = require('perf_hooks');

/**
 * Performance test configuration
 */
const CONFIG = {
  targetThroughput: 5000, // SMS per second (5K target)
  testDuration: 60, // seconds (1 minute test)
  concurrentRequests: 500, // concurrent requests per batch
  apiToken: 'ccda1683d8c97f8f2dff2ea7d649b42c',
  baseUrl: 'http://localhost:3000',
  endpoint: '/api/v1/sms/send/single'
};

/**
 * Generate test SMS data
 */
function generateTestSms(index) {
  return {
    message: `Performance test message ${index}`,
    to: index % 2 === 0 ? '251953960596' : '251711220033', // Alternate between providers
    sms_credential: {
      usr: "testuser",
      pwd: "testpass",
      from: "SENDER",
      token: "testtoken",
      sms_service_provider: index % 2 === 0 ? "TELE" : "SAFARICOM"
    }
  };
}

/**
 * Send a single SMS request
 */
function sendSms(data, requestId) {
  return new Promise((resolve, reject) => {
    const startTime = performance.now();
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: CONFIG.endpoint,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${CONFIG.apiToken}`,
        'Content-Length': JSON.stringify(data).length
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            requestId,
            status: res.statusCode,
            duration,
            success: res.statusCode === 202,
            data: parsedData
          });
        } catch (e) {
          resolve({
            requestId,
            status: res.statusCode,
            duration,
            success: false,
            error: 'Failed to parse response'
          });
        }
      });
    });

    req.on('error', (e) => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      reject({
        requestId,
        error: e.message,
        duration
      });
    });

    req.write(JSON.stringify(data));
    req.end();
  });
}

/**
 * Send concurrent requests
 */
async function sendConcurrentRequests(count) {
  const promises = [];
  
  for (let i = 0; i < count; i++) {
    const data = generateTestSms(i);
    const requestId = `perf-${Date.now()}-${i}`;
    promises.push(sendSms(data, requestId));
  }
  
  return Promise.allSettled(promises);
}

/**
 * Calculate statistics
 */
function calculateStats(results) {
  const successful = results.filter(r => r.status === 'fulfilled' && r.value.success);
  const failed = results.filter(r => r.status === 'rejected' || !r.value.success);
  
  const durations = successful.map(r => r.value.duration);
  const avgDuration = durations.length > 0 ? durations.reduce((a, b) => a + b, 0) / durations.length : 0;
  const minDuration = durations.length > 0 ? Math.min(...durations) : 0;
  const maxDuration = durations.length > 0 ? Math.max(...durations) : 0;
  
  return {
    total: results.length,
    successful: successful.length,
    failed: failed.length,
    successRate: (successful.length / results.length) * 100,
    avgDuration,
    minDuration,
    maxDuration,
    throughput: successful.length // per batch
  };
}

/**
 * Run performance test
 */
async function runPerformanceTest() {
  console.log('🚀 Starting Performance Test');
  console.log(`Target: ${CONFIG.targetThroughput} SMS/second`);
  console.log(`Duration: ${CONFIG.testDuration} seconds`);
  console.log(`Concurrent requests: ${CONFIG.concurrentRequests}`);
  console.log('=' * 50);

  const startTime = performance.now();
  const results = [];
  let batchCount = 0;

  // Run test for specified duration
  const testInterval = setInterval(async () => {
    batchCount++;
    console.log(`\n📦 Sending batch ${batchCount} (${CONFIG.concurrentRequests} concurrent requests)...`);
    
    const batchStartTime = performance.now();
    const batchResults = await sendConcurrentRequests(CONFIG.concurrentRequests);
    const batchEndTime = performance.now();
    const batchDuration = (batchEndTime - batchStartTime) / 1000; // seconds
    
    results.push(...batchResults);
    
    const stats = calculateStats(batchResults);
    console.log(`✅ Batch ${batchCount} completed:`);
    console.log(`   - Success: ${stats.successful}/${stats.total} (${stats.successRate.toFixed(2)}%)`);
    console.log(`   - Avg response time: ${stats.avgDuration.toFixed(2)}ms`);
    console.log(`   - Batch duration: ${batchDuration.toFixed(2)}s`);
    console.log(`   - Throughput: ${(stats.successful / batchDuration).toFixed(2)} SMS/s`);
    
  }, 1000); // Send batch every second

  // Stop after test duration
  setTimeout(() => {
    clearInterval(testInterval);
    
    const totalTime = (performance.now() - startTime) / 1000;
    const totalStats = calculateStats(results);
    
    console.log('\n' + '=' * 50);
    console.log('📊 PERFORMANCE TEST RESULTS');
    console.log('=' * 50);
    console.log(`Total time: ${totalTime.toFixed(2)} seconds`);
    console.log(`Total requests: ${totalStats.total}`);
    console.log(`Successful: ${totalStats.successful}`);
    console.log(`Failed: ${totalStats.failed}`);
    console.log(`Success rate: ${totalStats.successRate.toFixed(2)}%`);
    console.log(`Average response time: ${totalStats.avgDuration.toFixed(2)}ms`);
    console.log(`Min response time: ${totalStats.minDuration.toFixed(2)}ms`);
    console.log(`Max response time: ${totalStats.maxDuration.toFixed(2)}ms`);
    console.log(`Overall throughput: ${(totalStats.successful / totalTime).toFixed(2)} SMS/second`);
    
    // Performance assessment
    const achievedThroughput = totalStats.successful / totalTime;
    const targetThroughput = CONFIG.targetThroughput;
    
    console.log('\n🎯 PERFORMANCE ASSESSMENT');
    console.log(`Target throughput: ${targetThroughput.toLocaleString()} SMS/second`);
    console.log(`Achieved throughput: ${achievedThroughput.toLocaleString()} SMS/second`);
    console.log(`Performance ratio: ${(achievedThroughput / targetThroughput * 100).toFixed(2)}%`);
    
    if (achievedThroughput >= targetThroughput * 0.8) {
      console.log('✅ EXCELLENT: System can handle high throughput!');
    } else if (achievedThroughput >= targetThroughput * 0.5) {
      console.log('⚠️  GOOD: System performance is acceptable but can be improved');
    } else {
      console.log('❌ NEEDS IMPROVEMENT: System needs optimization for target throughput');
    }
    
    process.exit(0);
  }, CONFIG.testDuration * 1000);
}

// Run the test
runPerformanceTest().catch(console.error); 