const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000';
const API_TOKEN = 'ccda1683d8c97f8f2dff2ea7d649b42c';

// Test data for 5 SMS messages
const testMessages = [
  {
    sms_credential: {
      sms_service_provider: 'TELE',
      usr: 'testuser',
      pwd: 'testpass',
      from: 'SENDER',
      token: 'testtoken'
    },
    message: 'Test SMS 1 - TELE Provider',
    to: '251953960596'
  },
  {
    sms_credential: {
      sms_service_provider: 'SAFARICOM',
      usr: 'testuser',
      pwd: 'testpass',
      from: 'SENDER',
      token: 'testtoken'
    },
    message: 'Test SMS 2 - SAFARICOM Provider',
    to: '251711220033'
  },
  {
    sms_credential: {
      sms_service_provider: 'TELE',
      usr: 'testuser',
      pwd: 'testpass',
      from: 'SENDER',
      token: 'testtoken'
    },
    message: 'Test SMS 3 - TELE Provider',
    to: '251953960596'
  },
  {
    sms_credential: {
      sms_service_provider: 'SAFARICOM',
      usr: 'testuser',
      pwd: 'testpass',
      from: 'SENDER',
      token: 'testtoken'
    },
    message: 'Test SMS 4 - SAFARICOM Provider',
    to: '251711220033'
  },
  {
    sms_credential: {
      sms_service_provider: 'TELE',
      usr: 'testuser',
      pwd: 'testpass',
      from: 'SENDER',
      token: 'testtoken'
    },
    message: 'Test SMS 5 - TELE Provider',
    to: '251953960596'
  }
];

async function sendSMS(smsData, index) {
  try {
    console.log(`📤 Sending SMS ${index + 1} to ${smsData.to} (${smsData.sms_credential.sms_service_provider})...`);
    
    const response = await axios.post(`${BASE_URL}/api/v1/sms/send/single`, smsData, {
      headers: {
        'Authorization': `Bearer ${API_TOKEN}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    console.log(`   ✅ SMS ${index + 1} sent successfully (Status: ${response.status})`);
    console.log(`   📊 Response: ${JSON.stringify(response.data, null, 2)}`);
    return true;
  } catch (error) {
    console.log(`   ❌ SMS ${index + 1} failed: ${error.response ? error.response.status : error.message}`);
    if (error.response) {
      console.log(`   📊 Response: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    return false;
  }
}

async function checkMetrics() {
  try {
    console.log('\n📊 Checking current metrics...');
    const response = await axios.get(`${BASE_URL}/api/v1/worker-status/metrics`, {
      headers: {
        'Authorization': `Bearer ${API_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    const metrics = response.data.metrics;
    console.log(`   📈 Total Messages: ${metrics.totalMessages}`);
    console.log(`   ✅ Successful: ${metrics.successfulMessages}`);
    console.log(`   ❌ Failed: ${metrics.failedMessages}`);
    console.log(`   🔄 Retry Attempts: ${metrics.retryAttempts}`);
    console.log(`   🚫 Max Retries Reached: ${metrics.maxRetriesReached}`);
    
    return metrics;
  } catch (error) {
    console.log(`   ❌ Failed to get metrics: ${error.message}`);
    return null;
  }
}

async function checkFailedSMS() {
  try {
    console.log('\n📋 Checking failed SMS persistence...');
    const response = await axios.get(`${BASE_URL}/api/v1/worker-status/failed-sms`, {
      headers: {
        'Authorization': `Bearer ${API_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    const data = response.data;
    console.log(`   📊 Failed SMS Count: ${data.stats.totalFailed}`);
    console.log(`   💾 Total Size: ${data.stats.totalSize} bytes`);
    
    if (data.failedMessages.length > 0) {
      console.log(`   📝 Failed Messages: ${data.failedMessages.length}`);
      data.failedMessages.forEach((msg, index) => {
        console.log(`      ${index + 1}. To: ${msg.recipient}, Error: ${msg.errorType}`);
      });
    } else {
      console.log(`   📝 No failed messages in persistence`);
    }
    
    return data;
  } catch (error) {
    console.log(`   ❌ Failed to get failed SMS: ${error.message}`);
    return null;
  }
}

async function checkWorkerStatus() {
  try {
    console.log('\n👥 Checking worker status...');
    const response = await axios.get(`${BASE_URL}/api/v1/worker-status`, {
      headers: {
        'Authorization': `Bearer ${API_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    const data = response.data;
    console.log(`   🏥 Status: ${data.status}`);
    console.log(`   👥 Total Workers: ${data.totalWorkers}`);
    console.log(`   ✅ Active Workers: ${data.activeWorkers}`);
    console.log(`   📊 Overall Success Rate: ${data.overallSuccessRate}%`);
    console.log(`   ⚡ Throughput: ${data.overallThroughputPerSecond} msg/sec`);
    console.log(`   📨 Total Processed: ${data.totalMessagesProcessed}`);
    console.log(`   ✅ Total Successful: ${data.totalMessagesSuccessful}`);
    console.log(`   ❌ Total Failed: ${data.totalMessagesFailed}`);
    console.log(`   🔄 Total Retry Attempts: ${data.totalRetryAttempts}`);
    
    return data;
  } catch (error) {
    console.log(`   ❌ Failed to get worker status: ${error.message}`);
    return null;
  }
}

async function triggerRetry() {
  try {
    console.log('\n🔄 Triggering retry for all failed SMS...');
    const response = await axios.get(`${BASE_URL}/api/v1/worker-status/retry-all`, {
      headers: {
        'Authorization': `Bearer ${API_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    const data = response.data;
    console.log(`   📊 Retry Result: ${JSON.stringify(data.result, null, 2)}`);
    
    return data;
  } catch (error) {
    console.log(`   ❌ Failed to trigger retry: ${error.message}`);
    return null;
  }
}

async function runTest() {
  console.log('🚀 Starting Small SMS Batch Test (5 messages)');
  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log(`🔑 API Token: ${API_TOKEN ? 'Configured' : 'Missing'}\n`);

  // Step 1: Send 5 SMS messages
  console.log('📤 STEP 1: Sending 5 SMS messages...\n');
  const results = [];
  
  for (let i = 0; i < testMessages.length; i++) {
    const success = await sendSMS(testMessages[i], i);
    results.push(success);
    
    // Small delay between messages
    if (i < testMessages.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  console.log(`\n📊 SMS Sending Summary: ${results.filter(r => r).length}/${results.length} successful`);

  // Step 2: Wait for processing
  console.log('\n⏳ STEP 2: Waiting for messages to be processed...');
  await new Promise(resolve => setTimeout(resolve, 5000));

  // Step 3: Check metrics
  console.log('\n📊 STEP 3: Checking metrics...');
  await checkMetrics();

  // Step 4: Check worker status
  console.log('\n👥 STEP 4: Checking worker status...');
  await checkWorkerStatus();

  // Step 5: Check failed SMS persistence
  console.log('\n📋 STEP 5: Checking failed SMS persistence...');
  await checkFailedSMS();

  // Step 6: Trigger retry
  console.log('\n🔄 STEP 6: Testing retry mechanism...');
  await triggerRetry();

  // Step 7: Wait and check final metrics
  console.log('\n⏳ STEP 7: Waiting for retry processing...');
  await new Promise(resolve => setTimeout(resolve, 3000));

  console.log('\n📊 STEP 8: Final metrics check...');
  await checkMetrics();

  console.log('\n🎉 Test completed!');
  console.log('\n📋 Summary:');
  console.log('   - 5 SMS messages sent');
  console.log('   - Metrics and worker status checked');
  console.log('   - Failed SMS persistence verified');
  console.log('   - Retry mechanism tested');
  console.log('   - All monitoring endpoints functional');
}

// Run the test
runTest().catch(console.error); 