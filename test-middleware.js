const http = require('http');

/**
 * Make a request to the API
 * @param {string} method - The HTTP method
 * @param {string} path - The path to request
 * @param {object} body - The request body (for POST/PUT/PATCH)
 * @returns {Promise<object>} - The response
 */
function makeRequest(method, path, body = null) {
  return new Promise((resolve, reject) => {
    // Configure the request options
    const options = {
      hostname: 'localhost',
      port: 3000,
      path,
      method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    // Create the request
    const req = http.request(options, (res) => {
      let data = '';

      // Collect the response data
      res.on('data', (chunk) => {
        data += chunk;
      });

      // Resolve the promise when the response is complete
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: parsedData
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: data
          });
        }
      });
    });

    // Handle request errors
    req.on('error', (error) => {
      reject(error);
    });

    // Send the request body if provided
    if (body) {
      req.write(JSON.stringify(body));
    }

    // End the request
    req.end();
  });
}

/**
 * Test the middleware by making requests to the API
 */
async function testMiddleware() {
  console.log('Testing middleware...');

  try {
    // Make a request to the health endpoint
    console.log('\n--- Testing health endpoint ---');
    const healthResponse = await makeRequest('GET', '/api/v1/health');
    console.log(`Status code: ${healthResponse.statusCode}`);
    console.log(`Headers: ${JSON.stringify(healthResponse.headers, null, 2)}`);
    console.log(`Request ID: ${healthResponse.headers['x-request-id']}`);

    // Make a request to the config endpoint
    console.log('\n--- Testing config endpoint ---');
    const configResponse = await makeRequest('GET', '/api/v1/config');
    console.log(`Status code: ${configResponse.statusCode}`);
    console.log(`Headers: ${JSON.stringify(configResponse.headers, null, 2)}`);
    console.log(`Request ID: ${configResponse.headers['x-request-id']}`);

    // Make a request to the SMS endpoint
    console.log('\n--- Testing SMS endpoint ---');
    const smsResponse = await makeRequest('POST', '/api/v1/sms/send/single', {
      to: '251953960596',
      message: 'Test message',
      sms_credential: {
        usr: 'testuser',
        pwd: 'testpass',
        from: 'SENDER',
        token: 'testtoken',
        sms_service_provider: 'TELE'
      }
    });
    console.log(`Status code: ${smsResponse.statusCode}`);
    console.log(`Headers: ${JSON.stringify(smsResponse.headers, null, 2)}`);
    console.log(`Request ID: ${smsResponse.headers['x-request-id']}`);
    console.log(`Response body: ${JSON.stringify(smsResponse.body, null, 2)}`);

    console.log('\nMiddleware test completed successfully!');
  } catch (error) {
    console.error('Error testing middleware:', error.message);
  }
}

// Run the test
testMiddleware();
