# Compiled output
/dist
/node_modules
/build

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store
Thumbs.db

# Tests
/coverage
/.nyc_output
/test-results

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production

# Keep example env file
!.env.example

# Temporary files
tmp/
temp/

# RabbitMQ specific
rabbitmq-data/

# Worker specific
worker-data/

# Debug files
*.heapsnapshot
*.cpuprofile

# Package manager locks (uncomment based on your preference)
# yarn.lock
# package-lock.json
# pnpm-lock.yaml

# Build tools
.webpack/
.serverless/
.fusebox/
.dynamodb/

# Misc
.npm
.eslintcache
.stylelintcache
.node_repl_history
.yarn-integrity
