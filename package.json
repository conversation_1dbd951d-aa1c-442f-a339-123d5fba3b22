{"name": "geezsms-sms-layer-app", "version": "1.0.0", "description": "", "main": "dist/src/main", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "prestart:prod": "npm run build", "start:prod": "node dist/src/main", "start:4_workers": "cross-env SMS_WORKER_COUNT=4 npm run start:dev", "start:7_workers": "cross-env SMS_WORKER_COUNT=4 npm run start:dev", "start:workers:high": "cross-env SMS_WORKER_COUNT=4 SMS_WORKER_CATEGORIES=high-priority,default npm run start:dev", "start:workers:bulk": "cross-env SMS_WORKER_COUNT=8 SMS_WORKER_CATEGORIES=bulk,default npm run start:dev", "start:workers:max": "cross-env SMS_WORKER_COUNT=0 npm run start:dev", "test:sms": "node test-sms.js", "lint": "eslint \"{src,test}/**/*.ts\" --fix", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand"}, "keywords": [], "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.13", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.13", "@nestjs/microservices": "^11.0.15", "@nestjs/platform-express": "^11.0.13", "@nestjs/swagger": "^11.1.3", "@nestjs/terminus": "^11.0.0", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.7", "axios": "^1.9.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "helmet": "^8.1.0", "iconv-lite": "^0.6.3", "nest-winston": "^1.10.2", "reflect-metadata": "^0.2.2", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "winston": "^3.17.0", "worker-thread": "^1.1.0"}, "devDependencies": {"@nestjs/cli": "^11.0.5", "@nestjs/schematics": "^11.0.2", "@nestjs/testing": "^11.0.20", "@types/amqp-connection-manager": "^2.0.12", "@types/amqplib": "^0.10.7", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/node": "^22.14.0", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@types/winston": "^2.4.4", "cross-env": "^7.0.3", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "jest": "^29.7.0", "prettier": "^3.5.3", "rimraf": "^6.0.1", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "typescript": "^5.8.2"}}