# App Configuration
NODE_ENV=development
PORT=3000
APP_URL=http://127.0.0.1:3000

# Logging
LOG_LEVEL=debug

# RabbitMQ
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_HEARTBEAT=60
RABBITMQ_HEALTHCHECK_TIMEOUT=5000
SMS_MAX_RETRIES=2
QUEUE_SMS_SINGLE=sms_single_queue
QUEUE_SMS_BULK=sms_bulk_queue
QUEUE_SMS_FAILED=sms_failed_queue
QUEUE_SMS_DLR=sms_dlr_queue

# SMS Gateway (example)
SMS_API_KEY=your_api_key
SMS_API_URL=https://api.smsprovider.com/v1
PL_API_URL=http://127.0.0.1:8000/api
TELE_JASMINE_API_URL=http://127.0.0.1:8080/secure
SAFARICOM_JASMINE_API_URL=http://127.0.0.1:8080/secure
SMPP_NESTJS_API_URL=https://webhook.site/d1a900af-c0e0-4ff8-a40d-c3d21572577e
# SMPP_NESTJS_API_URL=**************:5001/sms/send

# Worker Configuration
SMS_WORKER_CATEGORIES=single,bulk,dlr
# Number of worker threads for SMS processing
SINGLE_SMS_WORKER_COUNT=2
BULK_SMS_WORKER_COUNT=4
DLR_SMS_WORKER_COUNT=2
