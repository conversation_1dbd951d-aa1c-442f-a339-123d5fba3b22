version: '3.9'
services:
  sms-layer-app:
    build:
      context: .
      dockerfile: Dockerfile
    image: sms-layer-image
    ports:
      - '3000:3000'
    # network_mode: host
    env_file:
      - .env
    environment:
      - NODE_ENV=production
      - RABBITMQ_URL=amqp://geezsl:g1e2eG7sa@rabbitmq:5672
      - RABBITMQ_PORT=5672
    volumes:
      - ./logs:/app/logs
    depends_on:
      rabbitmq:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/api/v1/health']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  rabbitmq:
    image: rabbitmq:4-management
    ports:
      - '5672:5672' # AMQP protocol port
      - '15672:15672' # Management UI port
    environment:
      - RABBITMQ_AMQP_PORT=5672
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER:-geezsl}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD:-g1e2eG7sa}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ['CMD', 'rabbitmq-diagnostics', 'check_port_connectivity']
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped

volumes:
  rabbitmq_data:
