const axios = require('axios');

// Configuration
const RABBITMQ_HOST = 'localhost';
const RABBITMQ_PORT = '15672';
const RABBITMQ_USER = 'guest';
const RABBITMQ_PASS = 'guest';

// SMS-related queue names to clear
const SMS_QUEUES = [
  'sms_single_queue',
  'sms_bulk_queue', 
  'sms_retry_queue',
  'sms_failed_queue',
  'sms_pending_queue',
  'dlx_queue'
];

async function clearQueue(queueName) {
  try {
    const url = `http://${RABBITMQ_HOST}:${RABBITMQ_PORT}/api/queues/%2F/${queueName}/contents`;
    const response = await axios.delete(url, {
      auth: {
        username: RABBITMQ_USER,
        password: RABBITMQ_PASS
      }
    });
    
    console.log(`   ✅ Cleared queue: ${queueName}`);
    return true;
  } catch (error) {
    if (error.response && error.response.status === 404) {
      console.log(`   ⚠️  Queue not found: ${queueName}`);
      return false;
    } else {
      console.log(`   ❌ Failed to clear queue ${queueName}: ${error.message}`);
      return false;
  }
  }
}

async function getQueueInfo(queueName) {
  try {
    const url = `http://${RABBITMQ_HOST}:${RABBITMQ_PORT}/api/queues/%2F/${queueName}`;
    const response = await axios.get(url, {
      auth: {
        username: RABBITMQ_USER,
        password: RABBITMQ_PASS
      }
    });
    
    return {
      name: response.data.name,
      messages: response.data.messages,
      message_bytes: response.data.message_bytes
    };
  } catch (error) {
    return null;
  }
}

async function getAllQueues() {
  try {
    const url = `http://${RABBITMQ_HOST}:${RABBITMQ_PORT}/api/queues`;
    const response = await axios.get(url, {
      auth: {
        username: RABBITMQ_USER,
        password: RABBITMQ_PASS
      }
    });
    
    return response.data;
  } catch (error) {
    console.log(`❌ Failed to get queues: ${error.message}`);
    return [];
  }
}

async function showQueueStatus() {
  console.log('\n📊 Current Queue Status:');
  console.log('========================');
  
  const queues = await getAllQueues();
  let totalMessages = 0;
  let totalBytes = 0;
  
  queues.forEach(queue => {
    if (queue.messages > 0) {
      console.log(`   📦 ${queue.name}: ${queue.messages} messages (${queue.message_bytes} bytes)`);
      totalMessages += queue.messages;
      totalBytes += queue.message_bytes;
    }
  });
  
  if (totalMessages === 0) {
    console.log('   🎉 All queues are empty!');
  } else {
    console.log(`\n   📈 Total: ${totalMessages} messages (${totalBytes} bytes)`);
  }
}

async function clearAllSMSQueues() {
  console.log('🧹 Clearing All SMS Queues');
  console.log('==========================');
  
  let successCount = 0;
  let totalCount = SMS_QUEUES.length;
  
  for (const queueName of SMS_QUEUES) {
    const success = await clearQueue(queueName);
    if (success) successCount++;
    
    // Small delay to avoid overwhelming the API
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log(`\n📊 Clear Results:`);
  console.log(`   ✅ Successfully cleared: ${successCount}/${totalCount} queues`);
  
  if (successCount < totalCount) {
    console.log(`   ⚠️  Some queues may not exist or couldn't be cleared`);
  }
}

async function clearAllQueues() {
  console.log('🧹 Clearing ALL Queues (Use with caution!)');
  console.log('==========================================');
  
  const queues = await getAllQueues();
  let successCount = 0;
  let totalCount = queues.length;
  
  for (const queue of queues) {
    if (queue.messages > 0) {
      const success = await clearQueue(queue.name);
      if (success) successCount++;
      
      // Small delay to avoid overwhelming the API
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  console.log(`\n📊 Clear Results:`);
  console.log(`   ✅ Successfully cleared: ${successCount} queues with messages`);
  console.log(`   📦 Total queues checked: ${totalCount}`);
}

async function main() {
  console.log('🐰 RabbitMQ Queue Management Tool');
  console.log('================================');
  console.log(`📍 Host: ${RABBITMQ_HOST}:${RABBITMQ_PORT}`);
  console.log(`👤 User: ${RABBITMQ_USER}`);
  
  // Get command line arguments
  const args = process.argv.slice(2);
  const command = args[0] || 'status';
  
  try {
    switch (command) {
      case 'status':
        await showQueueStatus();
        break;
        
      case 'clear-sms':
        await clearAllSMSQueues();
        console.log('\n⏳ Waiting 2 seconds...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        await showQueueStatus();
        break;
        
      case 'clear-all':
        console.log('\n⚠️  WARNING: This will clear ALL queues!');
        console.log('Press Ctrl+C to cancel or wait 5 seconds to continue...');
        
        await new Promise(resolve => setTimeout(resolve, 5000));
        await clearAllQueues();
        console.log('\n⏳ Waiting 2 seconds...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        await showQueueStatus();
        break;
        
      default:
        console.log('\n📖 Usage:');
        console.log('   node clear-queues.js status     - Show current queue status');
        console.log('   node clear-queues.js clear-sms  - Clear only SMS-related queues');
        console.log('   node clear-queues.js clear-all  - Clear ALL queues (dangerous!)');
        console.log('\n💡 Examples:');
        console.log('   node clear-queues.js');
        console.log('   node clear-queues.js clear-sms');
        console.log('   node clear-queues.js clear-all');
        break;
    }
  } catch (error) {
    console.log(`\n❌ Error: ${error.message}`);
    console.log('💡 Make sure RabbitMQ is running and accessible');
  }
  
  console.log('\n🎉 Done!');
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n\n⏹️  Operation cancelled by user');
  process.exit(0);
});

// Run the script
main().catch(console.error); 