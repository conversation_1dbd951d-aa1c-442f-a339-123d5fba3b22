# GeezSMS Configuration for custom_5k
# Generated on 2025-07-07T00:53:28.670Z
# Custom 5K SMS/second target

# Performance Settings
SMS_TARGET_THROUGHPUT=5000
SMS_PER_WORKER_PER_SECOND=200
SMS_WORKER_COUNT=25
SMS_MAX_RETRIES=2
SMS_RETRY_DELAY_MS=500
SMS_BACKOFF_MULTIPLIER=1.5

# Worker Throughput Settings
WORKER_SMS_THROUGHPUT_PER_SECOND=200
WORKER_MAX_CONCURRENT_MESSAGES=50

# RabbitMQ Settings
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_POOL_SIZE=50
QUEUE_PREFETCH_COUNT=50
QUEUE_SMS_SINGLE=sms_single_queue
QUEUE_SMS_BULK=sms_bulk_queue
QUEUE_SMS_FAILED=sms_failed_queue
QUEUE_SMS_PENDING=sms_pending_queue

# HTTP Client Settings
HTTP_TIMEOUT_MS=5000
HTTP_MAX_CONNECTIONS=500

# Worker Settings
WORKER_MEMORY_LIMIT_MB=512
WORKER_HEARTBEAT_INTERVAL_MS=5000

# Logging Settings
LOG_LEVEL=error
ENABLE_WORKER_LOGS=true
LOGS_DIR=./logs
LOG_MAX_SIZE_MB=25
LOG_MAX_FILES=3

# Monitoring Settings
ENABLE_METRICS=true
METRICS_INTERVAL_MS=5000
ENABLE_HEALTH_CHECKS=true

# API Settings
API_AUTH_TOKEN=ccda1683d8c97f8f2dff2ea7d649b42c
API_THROTTLE_ENABLED=false
API_THROTTLE_TTL=60
API_THROTTLE_LIMIT=1000000

# Provider URLs
TELE_JASMINE_API_URL=https://api.tele.com/send
TELE_ENDPOINT=https://api.tele.com/send
SAFARICOM_JASMINE_API_URL=https://api.safaricom.com/send

# Application Settings
NODE_ENV=production
PORT=3000
APP_URL=http://localhost:3000

# Database/Backend Settings
BACKENDHOST_RESTFULL_API=http://localhost:8002

# Persistence Settings
ENABLE_FAILED_SMS_PERSISTENCE=true
FAILED_SMS_STORAGE_PATH=./data/failed_sms
PENDING_SMS_STORAGE_PATH=./data/pending_sms

# Batch Processing Settings
ENABLE_SMS_BATCHING=false
SMS_BATCH_SIZE=10
SMS_BATCH_TIMEOUT_MS=100

# Connection Pool Settings
MAX_CONNECTIONS=250
CONNECTION_TIMEOUT_MS=5000
KEEP_ALIVE=true
KEEP_ALIVE_MSECS=1000

# System Optimization
NODE_OPTIONS=--max-old-space-size=2500 --max-semi-space-size=64
UV_THREADPOOL_SIZE=32
