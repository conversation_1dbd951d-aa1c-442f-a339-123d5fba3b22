# .env.example

# App Configuration
NODE_ENV=development
PORT=3000

# Logging
LOG_LEVEL=debug

# RabbitMQ Configuration
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_URL=amqp://localhost:5672

# SMS Configuration
SMS_RETRIES=3
SMS_PROVIDER_PRIMARY=default
SMS_PROVIDER_FALLBACK=none
SMS_API_KEY=your_api_key
SMS_API_URL=https://api.smsprovider.com/v1

# Queue Configuration
QUEUE_SMS_SINGLE=sms_single_queue
QUEUE_SMS_BULK=sms_bulk_queue

# Worker Configuration
SMS_WORKER_COUNT=4  # Set to 0 to use CPU count
SMS_WORKER_CATEGORIES=default,bulk,marketing,transactional,high-priority